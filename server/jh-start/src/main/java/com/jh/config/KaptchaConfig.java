package com.jh.config;

import com.google.code.kaptcha.impl.DefaultKaptcha;
import com.google.code.kaptcha.util.Config;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.Properties;

import static com.google.code.kaptcha.Constants.*;

/**
 * 图形验证码配置
 *
 * <AUTHOR>
 */
@Component
public class KaptchaConfig {
    @Bean(name = "captchaProducer")
    public DefaultKaptcha getKaptchaBean() {
        DefaultKaptcha defaultKaptcha = new DefaultKaptcha();
        Properties properties = new Properties();
        // 是否有边框 默认为true 我们可以自己设置yes，no
        properties.setProperty(KAPTCHA_BORDER, "yes");
        // 边框颜色
        properties.setProperty(KAPTCHA_BORDER_COLOR, "220,223,230");
        // 验证码文本字符颜色 默认为Color.BLACK
        properties.setProperty(KAPTCHA_TEXTPRODUCER_FONT_COLOR, "30,144,255");
        // 验证码图片宽度 默认为200
        properties.setProperty(KAPTCHA_IMAGE_WIDTH, "150");
        // 验证码图片高度 默认为50
        properties.setProperty(KAPTCHA_IMAGE_HEIGHT, "50");
        // 验证码文本字符大小 默认为40
        properties.setProperty(KAPTCHA_TEXTPRODUCER_FONT_SIZE, "40");
        // 验证码文本字符长度 默认为5
        properties.setProperty(KAPTCHA_TEXTPRODUCER_CHAR_LENGTH, "4");
        // KAPTCHA_BACKGROUND_CLR_FROM
        properties.setProperty(KAPTCHA_BACKGROUND_CLR_FROM, "255,255,255");
        // KAPTCHA_BACKGROUND_CLR_TO
        properties.setProperty(KAPTCHA_BACKGROUND_CLR_TO, "255,255,255");
        // KAPTCHA_SESSION_CONFIG_KEY
        properties.setProperty(KAPTCHA_SESSION_CONFIG_KEY, "code");
        // 验证码字体
        properties.setProperty(KAPTCHA_TEXTPRODUCER_FONT_NAMES, "宋体,楷体,微软雅黑");

        Config config = new Config(properties);
        defaultKaptcha.setConfig(config);
        return defaultKaptcha;
    }

}