package com.jh.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 阿里云配置
 *
 * <AUTHOR>
 */
@Configuration
public class AliyunConfig {

    @Value("${file.aliyun.endpoint}")
    private String endpoint;
    @Value("${file.aliyun.accessKeyId}")
    private String accessKeyId;
    @Value("${file.aliyun.accessKeySecret}")
    private String accessKeySecret;

    /**
     * 阿里云文件存储client
     */
    @Bean
    public OSS ossClient() {
        return  new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
    }

}
