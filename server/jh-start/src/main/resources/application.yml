spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  profiles:
    active: local
  #应用名称
  application:
    name: financeReport
  jackson:
    default-property-inclusion: always
  config:
    import:
#      - "classpath:/cms-${spring.profiles.active}.yml"
#      - "classpath:/llm-${spring.profiles.active}.yml"
#      - "classpath:/project-${spring.profiles.active}.yml"
  main:
    allow-circular-references: true
jasypt:
  encryptor:
    password: "!)QPA:Z?10qpa;z/"
    zbappName: abc
    zbipAddr: abc
    zbusername: abc
    zbPwd: 123456
    zbbaseUrl: https://
#服务端口
server:
  port: 9090
  tomcat:
    basedir: /home/<USER>
