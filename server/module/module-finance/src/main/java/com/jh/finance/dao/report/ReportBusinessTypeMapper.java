package com.jh.finance.dao.report;

import com.jh.common.mybatis.BaseInfoMapper;
import com.jh.finance.bean.report.ReportBusinessType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 业务类型表Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Mapper
public interface ReportBusinessTypeMapper extends BaseInfoMapper<ReportBusinessType> {
    
    /**
     * 查询顶级业务类型
     * @return List<ReportBusinessType>
     */
    List<ReportBusinessType> findParentTypes();
    
    /**
     * 根据父ID查询子业务类型
     * @param parentId 父ID
     * @return List<ReportBusinessType>
     */
    List<ReportBusinessType> findByParentId(@Param("parentId") Integer parentId);
    
    /**
     * 根据组织ID查询业务类型
     * @param orgId 组织ID
     * @return List<ReportBusinessType>
     */
    List<ReportBusinessType> findByOrgId(@Param("orgId") Integer orgId);
    
    /**
     * 查询业务类型树形结构
     * @return List<ReportBusinessType>
     */
    List<ReportBusinessType> findTypeTree();
} 