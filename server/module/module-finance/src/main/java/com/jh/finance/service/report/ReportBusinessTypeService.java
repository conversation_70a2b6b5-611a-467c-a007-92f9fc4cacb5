package com.jh.finance.service.report;

import java.util.List;
import com.github.pagehelper.PageInfo;
import com.jh.finance.bean.report.ReportBusinessType;
import com.jh.finance.bean.report.vo.ReportBusinessTypeVo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 业务类型表Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface ReportBusinessTypeService {
	/**
	 * 保存或更新业务类型表
	 *@param reportBusinessType 业务类型表对象
	 *@return String 业务类型表ID
	 *<AUTHOR>
	 */
	String saveOrUpdateReportBusinessType(ReportBusinessType reportBusinessType);
	
	/**
	 * 删除业务类型表
	 *@param ids void 业务类型表ID
	 *<AUTHOR>
	 */
	void deleteReportBusinessType(List<String> ids);

	/**
	 * 查询业务类型表详情
	 *@param id
	 *@return ReportBusinessType
	 *<AUTHOR>
	 */
	ReportBusinessType findById(String id);

	/**
	 * 分页查询业务类型表
	 *@param reportBusinessTypeVo
	 *@return PageInfo<ReportBusinessType>
	 *<AUTHOR>
	 */
	PageInfo<ReportBusinessType> findPageByQuery(ReportBusinessTypeVo reportBusinessTypeVo);
	
	/**
     * 查询顶级业务类型
     * @return List<ReportBusinessType>
     */
    List<ReportBusinessType> findParentTypes();
    
    /**
     * 根据父ID查询子业务类型
     * @param parentId 父ID
     * @return List<ReportBusinessType>
     */
    List<ReportBusinessType> findByParentId(Integer parentId);
    
    /**
     * 根据组织ID查询业务类型
     * @param orgId 组织ID
     * @return List<ReportBusinessType>
     */
    List<ReportBusinessType> findByOrgId(Integer orgId);
    
    /**
     * 查询业务类型树形结构
     * @return List<ReportBusinessType>
     */
    List<ReportBusinessType> findTypeTree();
} 