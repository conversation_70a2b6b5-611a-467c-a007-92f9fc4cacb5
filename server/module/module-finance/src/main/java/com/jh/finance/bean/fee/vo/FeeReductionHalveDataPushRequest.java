package com.jh.finance.bean.fee.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 减半收取数据推送请求
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@Schema(description = "减半收取数据推送请求")
public class FeeReductionHalveDataPushRequest {

    /**
     * 唯一标识
     */
    @Schema(description = "唯一标识")
    private String uniqueCode;

    /**
     * 企业名称
     */
    @Schema(description = "企业名称")
    private String company;

    /**
     * 行业门类代码
     */
    @Schema(description = "行业门类代码")
    private String industryCategoryCode;

    /**
     * 产品名称
     */
    @Schema(description = "产品名称")
    private String productName;

    /**
     * 行业代码
     */
    @Schema(description = "行业代码")
    private String industryCode;

    /**
     * 访问令牌
     */
    @Schema(description = "访问令牌")
    private String token;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private String userId;

    /**
     * 金额
     */
    @Schema(description = "金额")
    private BigDecimal money;

    /**
     * 机构代码
     */
    @Schema(description = "机构代码")
    private String orgCode;

    /**
     * 统一社会信用代码
     */
    @Schema(description = "统一社会信用代码")
    private String uscc;

    /**
     * 产品数量
     */
    @Schema(description = "产品数量")
    private Integer productCount;

    /**
     * 享受时间
     */
    @Schema(description = "享受时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date enjoyTime;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    private String tel;

    /**
     * 区县代码
     */
    @Schema(description = "区县代码")
    private String countryCode;

    /**
     * 政策ID
     */
    @Schema(description = "政策ID")
    private String policyId;
}