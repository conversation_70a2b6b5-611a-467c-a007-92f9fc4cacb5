package com.jh.finance.controller.fee;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.finance.bean.fee.FeeNotice;
import com.jh.finance.bean.fee.vo.FeeNoticeVo;
import com.jh.finance.service.fee.FeeNoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.jh.common.bean.RestApiResponse;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.controller.BaseController;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

/**
 * 收费公示Controller
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@RestController
@RequestMapping("/fee/notice")
@Tag(name = "收费公示")
public class FeeNoticeController extends BaseController {

    @Autowired
    private FeeNoticeService feeNoticeService;

    private static final String PER_PREFIX = "btn:fee:notice:";

    /**
     * 新增收费公示（保存或发布）
     *
     * @param feeNotice 收费公示数据 json
     * @return RestApiResponse<?>
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @Operation(summary = "新增收费公示")
    @SystemLogAnnotation(type = "收费公示", value = "新增收费公示")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveFeeNotice(@RequestBody FeeNotice feeNotice) {
        String id = feeNoticeService.saveOrUpdateFeeNotice(feeNotice);
        return RestApiResponse.ok(id);
    }

    /**
     * 修改收费公示（保存或发布）
     *
     * @param feeNotice 收费公示数据 json
     * @return RestApiResponse<?>
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @Operation(summary = "修改收费公示")
    @SystemLogAnnotation(type = "收费公示", value = "修改收费公示")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateFeeNotice(@RequestBody FeeNotice feeNotice) {
        String id = feeNoticeService.saveOrUpdateFeeNotice(feeNotice);
        return RestApiResponse.ok(id);
    }

    /**
     * 批量删除收费公示
     *
     * @param ids 收费公示ID列表
     * @return RestApiResponse<?>
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @Operation(summary = "批量删除收费公示")
    @SystemLogAnnotation(type = "收费公示", value = "批量删除收费公示")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteFeeNotice(@RequestBody List<String> ids) {
        feeNoticeService.deleteFeeNotice(ids);
        return RestApiResponse.ok();
    }

    /**
     * 查询收费公示详情
     *
     * @param id 收费公示ID
     * @return RestApiResponse<?>
     */
    @GetMapping("/findById")
    @Operation(summary = "查询收费公示详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        FeeNotice feeNotice = feeNoticeService.findById(id);
        return RestApiResponse.ok(feeNotice);
    }

    /**
     * 分页查询收费公示
     * 支持按标题、发布日期范围、发布状态等条件查询
     *
     * @param vo 收费公示查询条件，包含：
     *           title - 标题（模糊查询）
     *           isPublish - 发布状态（0保存 1发布）
     *           publishDateStart - 发布日期开始（格式：yyyy-MM-dd）
     *           publishDateEnd - 发布日期结束（格式：yyyy-MM-dd）
     * @return RestApiResponse<?>
     */
    @PostMapping("/findPageByQuery")
    @Operation(summary = "分页查询收费公示")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody FeeNoticeVo vo) {
        PageInfo<FeeNotice> pageInfo = feeNoticeService.findPageByQuery(vo);
        return RestApiResponse.ok(pageInfo);
    }

    /**
     * 根据机构代码查询最新的已发布收费公示
     *
     * @param orgCode 机构代码
     * @return RestApiResponse<?>
     */
    @GetMapping("/findLatestPublishedByOrgCode")
    @Operation(summary = "根据机构代码查询最新的已发布收费公示")
    public RestApiResponse<?> findLatestPublishedByOrgCode(@RequestParam("orgCode") String orgCode) {
        FeeNotice feeNotice = feeNoticeService.findLatestPublishedByOrgCode(orgCode);
        return RestApiResponse.ok(feeNotice);
    }

}