package com.jh.finance.controller.fee;

import java.util.List;
import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.finance.bean.fee.FeeReductionFeedback;
import com.jh.finance.bean.fee.vo.FeeReductionFeedbackVo;
import com.jh.finance.service.fee.FeeReductionFeedbackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.github.pagehelper.PageInfo;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.controller.BaseController;
import org.springframework.web.multipart.MultipartFile;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

/**
 * 减费惠企改革-在线反馈Controller
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@RestController
@RequestMapping("/fee/feedback")
@Tag(name = "减费惠企改革-在线反馈")
public class FeeReductionFeedbackController extends BaseController {
    @Autowired
    private FeeReductionFeedbackService feeReductionFeedbackService;

    private static final String PER_PREFIX = "btn:fee:feedback:";

    /**
     * 新增减费惠企改革-在线反馈
     *
     * @param feeReductionFeedback 减费惠企改革-在线反馈数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @Operation(summary = "新增减费惠企改革-在线反馈")
    @SystemLogAnnotation(type = "减费惠企改革-在线反馈", value = "新增减费惠企改革-在线反馈")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveFeeReductionFeedback(@RequestBody FeeReductionFeedback feeReductionFeedback) {
        String id = feeReductionFeedbackService.saveOrUpdateFeeReductionFeedback(feeReductionFeedback);
        return RestApiResponse.ok(id);
    }

    /**
     * 修改减费惠企改革-在线反馈
     *
     * @param feeReductionFeedback 减费惠企改革-在线反馈数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @Operation(summary = "修改减费惠企改革-在线反馈")
    @SystemLogAnnotation(type = "减费惠企改革-在线反馈", value = "修改减费惠企改革-在线反馈")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateFeeReductionFeedback(@RequestBody FeeReductionFeedback feeReductionFeedback) {
        String id = feeReductionFeedbackService.saveOrUpdateFeeReductionFeedback(feeReductionFeedback);
        return RestApiResponse.ok(id);
    }

    /**
     * 批量删除减费惠企改革-在线反馈(判断 关联数据是否可以删除)
     *
     * @param ids
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @Operation(summary = "批量删除减费惠企改革-在线反馈")
    @SystemLogAnnotation(type = "减费惠企改革-在线反馈", value = "批量删除减费惠企改革-在线反馈")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteFeeReductionFeedback(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        feeReductionFeedbackService.deleteFeeReductionFeedback(ids);
        return RestApiResponse.ok();
    }

    /**
     * 查询减费惠企改革-在线反馈详情
     *
     * @param id
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findById")
    @Operation(summary = "查询减费惠企改革-在线反馈详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        FeeReductionFeedback feeReductionFeedback = feeReductionFeedbackService.findById(id);
        return RestApiResponse.ok(feeReductionFeedback);
    }

    /**
     * 分页查询减费惠企改革-在线反馈
     *
     * @param feeReductionFeedbackVo 减费惠企改革-在线反馈 查询条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/findPageByQuery")
    @Operation(summary = "分页查询减费惠企改革-在线反馈")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody FeeReductionFeedbackVo feeReductionFeedbackVo) {
        PageInfo<FeeReductionFeedback> feeReductionFeedback = feeReductionFeedbackService.findPageByQuery(feeReductionFeedbackVo);
        return RestApiResponse.ok(feeReductionFeedback);
    }

    @PostMapping("/excel")
    @Operation(summary = "导出减费惠企改革-在线反馈")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "excel')")
    // 这里的模块名称和子模块名称，请根据实际情况填写
    @ExportRespAnnotation(modType = "减费惠企改革-在线反馈", sonMod = "减费惠企改革-在线反馈", key = "jh_feedback_Table", isNotice = true, fileName = "减费惠企改革-在线反馈")
    public RestApiResponse<?> excel(@RequestBody FeeReductionFeedbackVo feeReductionFeedbackVo) {
        List<FeeReductionFeedback> feeReductionFeedbackList = feeReductionFeedbackService.findByQuery(feeReductionFeedbackVo);
        //这里对数据进行转换处理，如没使用字典的或关联其它表的数据。如在查询中已处理请忽略
        return RestApiResponse.ok(feeReductionFeedbackList);
    }

    @PostMapping("/import")
    @Operation(summary = "导入减费惠企改革-在线反馈")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "import')")
    public RestApiResponse<?> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "cover") Integer cover) {
        feeReductionFeedbackService.importFeeReductionFeedbackAsync(file, cover);
        return RestApiResponse.ok();
    }
}
