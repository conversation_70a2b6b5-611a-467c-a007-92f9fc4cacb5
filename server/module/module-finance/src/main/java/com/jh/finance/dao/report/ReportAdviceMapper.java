package com.jh.finance.dao.report;

import com.jh.common.mybatis.BaseInfoMapper;
import com.jh.finance.bean.report.ReportAdvice;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据上报流程-审批意见表Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-10
 */
public interface ReportAdviceMapper extends BaseInfoMapper<ReportAdvice> {
    
    /**
     * 根据申请ID查询审批意见
     * @param applyId 申请ID
     * @return List<ReportAdvice>
     */
    List<ReportAdvice> findByApplyId(@Param("applyId") String applyId);
    
    /**
     * 根据申请ID和节点查询审批意见
     * @param applyId 申请ID
     * @param stage 节点
     * @return ReportAdvice
     */
    ReportAdvice findByApplyIdAndStage(@Param("applyId") String applyId, @Param("stage") String stage);
} 