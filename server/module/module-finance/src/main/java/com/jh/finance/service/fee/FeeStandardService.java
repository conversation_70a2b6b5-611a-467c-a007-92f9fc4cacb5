package com.jh.finance.service.fee;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.jh.finance.bean.fee.FeeStandard;
import com.jh.finance.bean.fee.vo.FeeStandardVo;
import com.jh.finance.bean.fee.vo.FeeStandardDataPushRequest;

/**
 * 收费信息Service接口
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
public interface FeeStandardService {
    /**
     * 保存或更新收费信息
     *
     * @param feeStandard 收费信息对象
     * @return String 收费信息ID
     * <AUTHOR>
     */
    String saveOrUpdateFeeStandard(FeeStandard feeStandard);

    /**
     * 删除收费信息
     *
     * @param ids void 收费信息ID
     * <AUTHOR>
     */
    void deleteFeeStandard(List<String> ids);

    /**
     * 查询收费信息详情
     *
     * @param id
     * @return FeeStandard
     * <AUTHOR>
     */
    FeeStandard findById(String id);

    /**
     * 分页查询收费信息
     *
     * @param feeStandardVo
     * @return PageInfo<FeeStandard>
     * <AUTHOR>
     */
    PageInfo<FeeStandard> findPageByQuery(FeeStandardVo feeStandardVo);

    /**
     * 按条件导出查询收费信息
     *
     * @param feeStandardVo
     * @return PageInfo<FeeStandard>
     * <AUTHOR>
     */
    List<FeeStandard> findByQuery(FeeStandardVo feeStandardVo);

    /**
     * 处理外部推送的收费数据
     *
     * @param request 推送数据请求
     * @return String 处理结果信息
     * <AUTHOR>
     */
    String processPushData(FeeStandardDataPushRequest request);

    /**
     * 根据机构代码查询检验检测服务项目（标准类）
     *
     * @param orgCode 机构代码
     * @return List<FeeStandard> 标准类收费信息列表
     * <AUTHOR>
     */
    List<FeeStandard> findStandardByOrgCode(String orgCode);

}
