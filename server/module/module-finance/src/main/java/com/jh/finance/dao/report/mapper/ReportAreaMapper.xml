<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.finance.dao.report.ReportAreaMapper">

    <!-- 查询省级数据 -->
    <select id="findProvinceList" resultType="com.jh.finance.bean.report.ReportArea">
        SELECT *
        FROM report_area
        WHERE LEVEL = 0
          AND YN = 1
        ORDER BY SORT ASC
    </select>

    <!-- 查询市级数据 -->
    <select id="findCityList" resultType="com.jh.finance.bean.report.ReportArea">
        SELECT *
        FROM report_area
        WHERE LEVEL = 1
          AND YN = 1
        ORDER BY SORT ASC
    </select>

    <!-- 查询区县级数据 -->
    <select id="findCountyList" resultType="com.jh.finance.bean.report.ReportArea">
        SELECT *
        FROM report_area
        WHERE LEVEL = 2
          AND YN = 1
        ORDER BY SORT ASC
    </select>

    <!-- 根据父级代码查询下级区划 -->
    <select id="findByParentCode" resultType="com.jh.finance.bean.report.ReportArea" parameterType="java.lang.String">
        SELECT *
        FROM report_area
        WHERE YN = 1
        <choose>
            <!-- 如果是省级代码（6位，后4位为0000），查询市级 -->
            <when test="parentCode != null and parentCode.length() == 6 and parentCode.endsWith('0000')">
                AND CODE LIKE CONCAT(SUBSTRING(#{parentCode}, 1, 2), '%')
                AND LEVEL = 1
            </when>
            <!-- 如果是市级代码（4位，后2位为00），查询区县级 -->
            <when test="parentCode != null and parentCode.length() == 4 and parentCode.endsWith('00')">
                AND CODE LIKE CONCAT(#{parentCode}, '%')
                AND LEVEL = 2
            </when>
            <!-- 如果是市级代码（6位，后2位为00），查询区县级 -->
            <when test="parentCode != null and parentCode.length() == 6 and parentCode.endsWith('00')">
                AND CODE LIKE CONCAT(SUBSTRING(#{parentCode}, 1, 4), '%')
                AND LEVEL = 2
            </when>
            <!-- 其他情况，按原逻辑查询 -->
            <otherwise>
                AND CODE LIKE CONCAT(#{parentCode}, '%')
                AND LENGTH(CODE) > LENGTH(#{parentCode})
            </otherwise>
        </choose>
        ORDER BY SORT ASC
    </select>

    <!-- 根据级别查询区划 -->
    <select id="findByLevel" resultType="com.jh.finance.bean.report.ReportArea" parameterType="java.lang.Long">
        SELECT *
        FROM report_area
        WHERE LEVEL = #{level}
          AND YN = 1
        ORDER BY SORT ASC
    </select>

</mapper>