package com.jh.finance.bean.report;

import com.jh.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Table;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;

/**
 * 文件管理附件表 report_document_file
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@Table(name = "REPORT_DOCUMENT_FILE")
@Schema(description = "文件管理附件表")
public class ReportDocumentFile extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "DOCUMENT_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "文件管理主表ID")
    private String documentId;

    @Column(name = "FILE_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "文件名称")
    private String fileName;

    @Column(name = "FILE_URL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "文件URL")
    private String fileUrl;

    @Column(name = "FILE_PATH")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "文件路径")
    private String filePath;

    @Column(name = "SORT_ORDER")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "排序")
    private Integer sortOrder;

    @Column(name = "CREATE_USER_NICKNAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "创建用户名称")
    private String createUserNickname;

    @Column(name = "UPDATE_USER_NICKNAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "更新用户名称")
    private String updateUserNickname;

    @Column(name = "ATTR1")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "备用字段1")
    private String attr1;

    @Column(name = "ATTR2")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "备用字段2")
    private String attr2;

    @Column(name = "ATTR3")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "备用字段3")
    private String attr3;

    @Column(name = "ATTR4")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "备用字段4")
    private String attr4;

    @Column(name = "ATTR5")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "备用字段5")
    private String attr5;

}