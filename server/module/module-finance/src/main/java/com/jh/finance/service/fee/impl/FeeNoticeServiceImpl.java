package com.jh.finance.service.fee.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.AppUserUtil;
import com.jh.common.util.security.UUIDUtils;
import com.jh.common.constant.CommonConstant;
import com.jh.finance.bean.fee.FeeNotice;
import com.jh.finance.bean.fee.vo.FeeNoticeVo;
import com.jh.finance.dao.fee.FeeNoticeMapper;
import com.jh.finance.service.fee.FeeNoticeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * 收费公示Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Service
@Transactional(readOnly = true)
public class FeeNoticeServiceImpl extends BaseServiceImpl<FeeNoticeMapper, FeeNotice> implements FeeNoticeService {

    private static final Logger logger = LoggerFactory.getLogger(FeeNoticeServiceImpl.class);

    @Autowired
    private FeeNoticeMapper feeNoticeMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     * 保存或更新收费公示
     *@param feeNotice 收费公示对象
     *@return String 收费公示ID
     */
    public String saveOrUpdateFeeNotice(FeeNotice feeNotice) {
        if (feeNotice == null) {
            throw new ServiceException("数据异常");
        }

        // 设置发布相关信息
        if (feeNotice.getIsPublish() != null && feeNotice.getIsPublish() == 1) {
            // 如果是发布状态，设置发布日期
            feeNotice.setPublishDate(new Date());
        }

        if (StringUtils.isEmpty(feeNotice.getId())) {
            // 新增
            feeNotice.setId(UUIDUtils.getUUID());
            // 设置用户ID
            feeNotice.setUserId(AppUserUtil.getCurrentUserId());
            feeNotice.setOegCode(AppUserUtil.getCurrentUserName());
            feeNoticeMapper.insertSelective(feeNotice);
        } else {
            // 更新
            // 检查当前数据是否存在
            FeeNotice existingNotice = feeNoticeMapper.selectByPrimaryKey(feeNotice.getId());
            if (existingNotice == null) {
                throw new ServiceException("要更新的数据不存在");
            }

            // 如果是发布操作，检查是否已经发布
            if (feeNotice.getIsPublish() == 1 && existingNotice.getIsPublish() == 1) {
                throw new ServiceException("公示已发布，不能重复发布");
            }

            // 避免页面传入修改YN字段
            feeNotice.setYn(null);
            feeNoticeMapper.updateByPrimaryKeySelective(feeNotice);
        }
        return feeNotice.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     * 删除收费公示
     *@param ids 收费公示ID列表
     */
    public void deleteFeeNotice(List<String> ids) {
        for (String id : ids) {
            // 查询数据是否存在
            FeeNotice feeNotice = feeNoticeMapper.selectByPrimaryKey(id);
            if (feeNotice == null) {
                throw new ServiceException("非法请求");
            }

            // 检查当前用户是否有权限删除
            String currentUserId = AppUserUtil.getCurrentUserId();
            if (!currentUserId.equals(feeNotice.getUserId())) {
                throw new ServiceException("无权删除他人创建的公示");
            }

            // 已发布的公示不能删除
            if (feeNotice.getIsPublish() == 1) {
                throw new ServiceException("已发布的公示不能删除，请先取消发布");
            }

            // 逻辑删除
            FeeNotice tempFeeNotice = new FeeNotice();
            tempFeeNotice.setId(feeNotice.getId());
            tempFeeNotice.setYn(CommonConstant.FLAG_NO);
            feeNoticeMapper.updateByPrimaryKeySelective(tempFeeNotice);
        }
    }

    /**
     * 查询收费公示详情
     *
     * @param id 收费公示ID
     * @return FeeNotice
     */
    @Override
    public FeeNotice findById(String id) {
        return feeNoticeMapper.selectByPrimaryKey(id);
    }

    /**
     * 分页查询收费公示
     *
     * @param vo 查询条件
     * @return PageInfo<FeeNotice>
     */
    @Override
    public PageInfo<FeeNotice> findPageByQuery(FeeNoticeVo vo) {
        // 分页设置
        PageHelper.startPage(vo.getPageNum(), vo.getPageSize());

        // 设置当前用户ID作为查询条件
        vo.setUserId(AppUserUtil.getCurrentUserId());

        // 查询条件
        List<FeeNotice> feeNoticeList = feeNoticeMapper.findPageByQuery(vo);

        return new PageInfo<>(feeNoticeList);
    }

    /**
     * 根据机构代码查询最新的已发布收费公示
     *
     * @param orgCode 机构代码
     * @return FeeNotice 最新的已发布收费公示
     */
    @Override
    public FeeNotice findLatestPublishedByOrgCode(String orgCode) {
        return feeNoticeMapper.findLatestPublishedByOrgCode(orgCode);
    }
}