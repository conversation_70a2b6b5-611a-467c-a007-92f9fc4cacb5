package com.jh.finance.bean.basic;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.jh.common.bean.BaseEntity;
import com.jh.common.bean.SysUser;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

import java.util.Date;

/**
 * 用户平台信息对象 basic_user_info
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Table(name = "basic_user_info")
@Schema(description = "用户平台信息")
@Data
public class BasicUserInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "USER_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "用户表关联ID")
    private String userId;
    @Column(name = "APP_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "平台ID")
    private String appId;
    @Column(name = "APP_KEY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "平台KEY")
    private String appKey;
    
    @Column(name = "TOKEN")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "访问令牌")
    private String token;
    
    @Column(name = "TOKEN_EXPIRE_TIME")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @Schema(description = "令牌过期时间")
    private Date tokenExpireTime;

}
