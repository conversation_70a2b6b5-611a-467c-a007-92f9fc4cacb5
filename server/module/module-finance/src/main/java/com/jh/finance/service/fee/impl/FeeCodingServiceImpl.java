package com.jh.finance.service.fee.impl;

import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.common.bean.LoginUser;
import com.jh.common.bean.SysRole;
import com.jh.common.util.AppUserUtil;
import com.jh.common.util.io.FileConvertUtils;
import com.jh.common.util.io.FileUtil;
import com.jh.common.util.poi.EasyExcelUtils;
import com.jh.constant.enums.ImportStatusEnum;
import com.jh.finance.bean.fee.FeeCoding;
import com.jh.finance.bean.fee.vo.FeeCodingExcel;
import com.jh.finance.bean.fee.vo.FeeCodingVo;
import com.jh.finance.bean.report.vo.ReportDocumentVo;
import com.jh.finance.constant.RoleConstant;
import com.jh.finance.dao.fee.FeeCodingMapper;
import com.jh.finance.service.fee.FeeCodingService;
import com.jh.utils.ImportService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.concurrent.CompletableFuture;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;
import com.jh.common.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import static com.github.pagehelper.page.PageMethod.orderBy;
import static com.jh.common.controller.BaseController.getUserRoleList;

import org.springframework.web.multipart.MultipartFile;

/**
 * 收费赋码Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Service
@Transactional(readOnly = true)
public class FeeCodingServiceImpl extends BaseServiceImpl<FeeCodingMapper, FeeCoding> implements FeeCodingService {

    private static final Logger logger = LoggerFactory.getLogger(FeeCodingServiceImpl.class);
    @Autowired
    private FeeCodingMapper feeCodingMapper;

    @Autowired
    private ImportService importService;

    @Value("${file.temp.path}")
    private String tempPath;

    @Override
    @Transactional(readOnly = false)
    /**
     * 保存或更新收费赋码
     *@param feeCoding 收费赋码对象
     *@return String 收费赋码ID
     *<AUTHOR>
     */
    public String saveOrUpdateFeeCoding(FeeCoding feeCoding) {
        if (feeCoding == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(feeCoding.getId())) {
            //新增
            feeCoding.setId(UUIDUtils.getUUID());
            // 设置当前用户ID
            feeCoding.setUserId(AppUserUtil.getCurrentUserId());
            feeCodingMapper.insertSelective(feeCoding);
        } else {
            //TODO 做判断后方能执行修改 一般判断是否是自己的数据
            //避免页面传入修改
            feeCoding.setYn(null);
            feeCodingMapper.updateByPrimaryKeySelective(feeCoding);
        }
        return feeCoding.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     * 删除收费赋码
     *@param ids void 收费赋码ID
     *<AUTHOR>
     */
    public void deleteFeeCoding(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除 一般判断是否是自己的数据
            FeeCoding feeCoding = feeCodingMapper.selectByPrimaryKey(id);
            if (feeCoding == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            FeeCoding temfeeCoding = new FeeCoding();
            temfeeCoding.setYn(CommonConstant.FLAG_NO);
            temfeeCoding.setId(feeCoding.getId());
            feeCodingMapper.updateByPrimaryKeySelective(temfeeCoding);
        }
    }

    /**
     * 查询收费赋码详情
     *
     * @param id
     * @return FeeCoding
     * <AUTHOR>
     */
    @Override
    public FeeCoding findById(String id) {
        // TODO 做判断后方能反回数据 一般判断是否是自己的数据
        return feeCodingMapper.selectByPrimaryKey(id);
    }


    /**
     * 分页查询收费赋码
     *
     * @param feeCodingVo
     * @return PageInfo<FeeCoding>
     * <AUTHOR>
     */
    @Override
    public PageInfo<FeeCoding> findPageByQuery(FeeCodingVo feeCodingVo) {
        // TODO 做判断后方能执行查询 一般判断是否是自己的数据
        PageHelper.startPage(feeCodingVo.getPageNum(), feeCodingVo.getPageSize());
        orderBy(feeCodingVo.getOrderColumn() + " " + feeCodingVo.getOrderValue());
        Example example = getExample(feeCodingVo);
        List<FeeCoding> feeCodingList = feeCodingMapper.selectByExample(example);
        return new PageInfo<FeeCoding>(feeCodingList);
    }

    private Example getExample(FeeCodingVo feeCodingVo) {
        Example example = new Example(FeeCoding.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);


        // 获取当前用户角色列表
        List<SysRole> roleList = getUserRoleList();
        boolean hasAdminRole = false;
        if (roleList != null && !roleList.isEmpty()) {
            for (SysRole role : roleList) {
                String roleCode = role.getRoleCode();
                if (RoleConstant.ROLE_ADMIN.equals(roleCode) || RoleConstant.ROLE_SUPERADMIN.equals(roleCode)) {
                    hasAdminRole = true;
                }
            }
        }
        //不是管理员
        if (!hasAdminRole) {
            String currentUserId = AppUserUtil.getCurrentUserId();
            criteria.andEqualTo("userId", currentUserId);
        }

        // 查询条件 - 模糊查询
        if (!StringUtils.isEmpty(feeCodingVo.getMajorCategory())) {
            criteria.andLike("majorCategory", "%" + feeCodingVo.getMajorCategory() + "%");
        }
        if (!StringUtils.isEmpty(feeCodingVo.getMiddleCategory())) {
            criteria.andLike("middleCategory", "%" + feeCodingVo.getMiddleCategory() + "%");
        }
        if (!StringUtils.isEmpty(feeCodingVo.getMinorCategory())) {
            criteria.andLike("minorCategory", "%" + feeCodingVo.getMinorCategory() + "%");
        }
        if (!StringUtils.isEmpty(feeCodingVo.getProduct())) {
            criteria.andLike("product", "%" + feeCodingVo.getProduct() + "%");
        }
        if (!StringUtils.isEmpty(feeCodingVo.getDetectionItem())) {
            criteria.andLike("detectionItem", "%" + feeCodingVo.getDetectionItem() + "%");
        }

        example.orderBy("createTime").desc();
        return example;
    }


    /**
     * 按条件导出查询收费赋码
     *
     * @param feeCodingVo
     * @return PageInfo<FeeCoding>
     * <AUTHOR>
     */
    @Override
    public List<FeeCoding> findByQuery(FeeCodingVo feeCodingVo) {
        orderBy(feeCodingVo.getOrderColumn() + " " + feeCodingVo.getOrderValue());
        Example example = getExample(feeCodingVo);

        return feeCodingMapper.selectByExample(example);
    }

    /**
     * 导入收费赋码（只做新增导入）
     *
     * @param file  Excel文件
     * @param cover 是否覆盖参数（已废弃，现在只做新增导入）
     * @return
     * <AUTHOR>
     */
    @Override
    public void importFeeCodingAsync(MultipartFile file, Integer cover) {
        //为后续数据处理线程设置用户信息
        Object user = AppUserUtil.getLoginAppUser();
        LoginUser appLoginUser = (LoginUser) user;
        importService.notification(appLoginUser, "已创建收费赋码导入任务，完成后会通知", ExportRespAnnotation.SocketMessageTypeEnum.NORMAL, "收费赋码", null);
        // 开始时间
        Date startTime = new Date();
        // 验证excel
        importService.verifyExcel(file);
        List<FeeCodingExcel> list;
        // 读取数据
        try (InputStream inputStream = file.getInputStream()) {
            list = (List<FeeCodingExcel>) EasyExcelUtils.readExcel(FeeCodingExcel.class, inputStream);
            if (CollectionUtils.isEmpty(list)) {
                throw new ServiceException("模板文件为空或者异常，请使用提供的模板进行导入");
            }
        } catch (IOException e) {
            throw new ServiceException("请检查填写内容");
        }
        // 复制file 文件
        String pathForSave = tempPath + "/" + UUIDUtils.getUUID() + "&&" + file.getOriginalFilename();
        File copyFile = FileUtil.copyFile(file, pathForSave);
        MultipartFile copyMultipartFile = FileConvertUtils.createFileItem(copyFile);
        //这里写数据处理逻辑
        CompletableFuture.runAsync(() -> {
            try {
                // 设置用户信息 避免异步处理数据时丢失用户信息
                UsernamePasswordAuthenticationToken authentication =
                        new UsernamePasswordAuthenticationToken(appLoginUser, null, appLoginUser.getAuthorities());
                SecurityContextHolder.getContext().setAuthentication(authentication);
                List<FeeCoding> insertList = new ArrayList<>();
                //数据处理
                for (FeeCodingExcel item : list) {
                    //数据校验 - 判空逻辑
                    StringBuilder errorMsg = new StringBuilder();

                    // 必填字段验证
                    if (StringUtils.isEmpty(item.getCode())) {
                        errorMsg.append("收费代码不能为空;");
                    }
                    if (StringUtils.isEmpty(item.getMajorCategory())) {
                        errorMsg.append("收费大类不能为空;");
                    }
                    if (StringUtils.isEmpty(item.getMiddleCategory())) {
                        errorMsg.append("收费中类不能为空;");
                    }
                    if (StringUtils.isEmpty(item.getMinorCategory())) {
                        errorMsg.append("收费小类不能为空;");
                    }
                    if (StringUtils.isEmpty(item.getProduct())) {
                        errorMsg.append("收费产品不能为空;");
                    }
                    if (StringUtils.isEmpty(item.getDetectionItem())) {
                        errorMsg.append("收费项目/参数不能为空;");
                    }
                    if (item.getFeePrice() == null) {
                        errorMsg.append("收费价格不能为空;");
                    }
                    if (StringUtils.isEmpty(item.getBillingUnit())) {
                        errorMsg.append("收费计费单元不能为空;");
                    }

                    // 如果有验证错误，记录错误信息并跳过
                    if (errorMsg.length() > 0) {
                        item.setImportSituation(ImportStatusEnum.ERROR.getName() + ":" + errorMsg.toString());
                        continue;
                    }

                    // 创建新的收费赋码对象（全部新增导入）
                    FeeCoding feeCoding = new FeeCoding();
                    BeanUtils.copyProperties(item, feeCoding);
                    feeCoding.setId(UUIDUtils.getUUID());
                    feeCoding.setYn(CommonConstant.FLAG_YES);
                    feeCoding.setUserId(AppUserUtil.getCurrentUserId());
                    insertList.add(feeCoding);
                    item.setImportSituation(ImportStatusEnum.IMPORT_SUCCESS.getName());
                }
                //保存数据（只做新增导入）
                if (!CollectionUtils.isEmpty(insertList)) {
                    feeCodingMapper.insertList(insertList);
                }
                String fileUrl = importService.faultDataAsync(list, FeeCodingExcel.class, "导入结果.xls",
                        "收费赋码", "收费赋码", startTime, copyMultipartFile, appLoginUser);
                importService.notification(appLoginUser, "收费赋码-导入结果.xls", ExportRespAnnotation.SocketMessageTypeEnum.DOWN, file.getOriginalFilename() + "导入成功", fileUrl);
            } catch (Exception e) {
                importService.notification(appLoginUser, e.getMessage(), ExportRespAnnotation.SocketMessageTypeEnum.NORMAL, "收费赋码导入【出现异常】", null);
            } finally {
                try {
                    Files.deleteIfExists(Paths.get(pathForSave));
                } catch (Exception e) {
                    logger.error("删除文件失败", e);
                }
            }
        });
    }
}
