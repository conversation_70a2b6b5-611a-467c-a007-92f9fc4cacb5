package com.jh.finance.dao.fee;

import com.jh.common.mybatis.BaseInfoMapper;
import com.jh.finance.bean.fee.FeeNotice;
import com.jh.finance.bean.fee.vo.FeeNoticeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 收费公示Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Mapper
public interface FeeNoticeMapper extends BaseInfoMapper<FeeNotice> {

    /**
     * 分页查询收费公示
     *
     * @param vo 查询条件
     * @return 收费公示列表
     */
    List<FeeNotice> findPageByQuery(FeeNoticeVo vo);

    /**
     * 根据机构代码查询最新的已发布收费公示
     *
     * @param orgCode 机构代码
     * @return 最新的已发布收费公示
     */
    @Select("SELECT * FROM fee_notice WHERE ORG_CODE = #{orgCode} AND YN = 1 AND IS_PUBLISH = 1 ORDER BY CREATE_TIME DESC LIMIT 1")
    FeeNotice findLatestPublishedByOrgCode(@Param("orgCode") String orgCode);
}