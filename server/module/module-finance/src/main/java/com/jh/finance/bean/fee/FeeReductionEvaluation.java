package com.jh.finance.bean.fee;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.jh.common.bean.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

/**
 * 减费惠企改革-一指评价对象 fee_reduction_evaluation
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Table(name = "fee_reduction_evaluation")
@Schema(description = "减费惠企改革-一指评价")
@Data
public class FeeReductionEvaluation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "ORG_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "机构名称")
    private String orgName;
    @Column(name = "AREA_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "地区名称")
    private String areaName;
    @Column(name = "AREA_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "地区CODE")
    private String areaCode;
    @Column(name = "DETECTION_EQUIPMENT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "检测设备")
    private String detectionEquipment;
    @Column(name = "EQUIPMENT_COUNT")
    @ColumnType(jdbcType = JdbcType.BIGINT)
    @Schema(description = "台件数")
    private Long equipmentCount;
    @Column(name = "INDUSTRY_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "行业名称")
    private String industryName;
    @Column(name = "POLICY_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "政策名称")
    private String policyName;
    @Column(name = "REDUCTION_AMOUNT")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @Schema(description = "减免费用")
    private BigDecimal reductionAmount;
    @Column(name = "ENJOY_TIME")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @Schema(description = "享受时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date enjoyTime;
    @Column(name = "SATISFACTION")
    @ColumnType(jdbcType = JdbcType.BIGINT)
    @Schema(description = "满意度（1-非常满意，2-满意，3-不满意）")
    private Integer satisfaction;

}
