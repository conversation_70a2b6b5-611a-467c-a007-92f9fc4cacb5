package com.jh.finance.controller.fee;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.List;

import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.finance.bean.fee.FeeCoding;
import com.jh.finance.bean.fee.vo.FeeCodingVo;
import com.jh.finance.service.fee.FeeCodingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.github.pagehelper.PageInfo;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.controller.BaseController;
import org.springframework.web.multipart.MultipartFile;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

/**
 * 收费赋码Controller
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RestController
@RequestMapping("/fee/coding")
@Tag(name = "收费赋码")
public class FeeCodingController extends BaseController {
    @Autowired
    private FeeCodingService feeCodingService;

    private static final String PER_PREFIX = "btn:fee:coding:";

    /**
     * 新增收费赋码
     *
     * @param feeCoding 收费赋码数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @Operation(summary = "新增收费赋码")
    @SystemLogAnnotation(type = "收费赋码", value = "新增收费赋码")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveFeeCoding(@RequestBody FeeCoding feeCoding) {
        String id = feeCodingService.saveOrUpdateFeeCoding(feeCoding);
        return RestApiResponse.ok(id);
    }

    /**
     * 修改收费赋码
     *
     * @param feeCoding 收费赋码数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @Operation(summary = "修改收费赋码")
    @SystemLogAnnotation(type = "收费赋码", value = "修改收费赋码")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateFeeCoding(@RequestBody FeeCoding feeCoding) {
        String id = feeCodingService.saveOrUpdateFeeCoding(feeCoding);
        return RestApiResponse.ok(id);
    }

    /**
     * 批量删除收费赋码(判断 关联数据是否可以删除)
     *
     * @param ids
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @Operation(summary = "批量删除收费赋码")
    @SystemLogAnnotation(type = "收费赋码", value = "批量删除收费赋码")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteFeeCoding(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        feeCodingService.deleteFeeCoding(ids);
        return RestApiResponse.ok();
    }

    /**
     * 查询收费赋码详情
     *
     * @param id
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findById")
    @Operation(summary = "查询收费赋码详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        FeeCoding feeCoding = feeCodingService.findById(id);
        return RestApiResponse.ok(feeCoding);
    }

    /**
     * 分页查询收费赋码
     *
     * @param feeCodingVo 收费赋码 查询条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/findPageByQuery")
    @Operation(summary = "分页查询收费赋码")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody FeeCodingVo feeCodingVo) {
        PageInfo<FeeCoding> feeCoding = feeCodingService.findPageByQuery(feeCodingVo);
        return RestApiResponse.ok(feeCoding);
    }

    @PostMapping("/excel")
    @Operation(summary = "导出收费赋码")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "excel')")
    // 这里的模块名称和子模块名称，请根据实际情况填写
    @ExportRespAnnotation(modType = "收费赋码", sonMod = "收费赋码", key = "jh_coding_Table", isNotice = true, fileName = "收费赋码")
    public RestApiResponse<?> excel(@RequestBody FeeCodingVo feeCodingVo) {
        List<FeeCoding> feeCodingList = feeCodingService.findByQuery(feeCodingVo);
        //这里对数据进行转换处理，如没使用字典的或关联其它表的数据。如在查询中已处理请忽略
        return RestApiResponse.ok(feeCodingList);
    }

    @PostMapping("/import")
    @Operation(summary = "导入收费赋码")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "import')")
    public RestApiResponse<?> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "cover") Integer cover) {
        feeCodingService.importFeeCodingAsync(file, cover);
        return RestApiResponse.ok();
    }



    /**
     * 收费赋码导入模板
     *
     * @return ResponseEntity<byte [ ]>
     * <AUTHOR>
     */
    @PostMapping("/downloadTemplate")
    @Operation(summary = "收费赋码导入模板")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "template')")
    public ResponseEntity<byte[]> downloadTemplate() {
        try {
            ClassPathResource resource = new ClassPathResource("template/收费赋码导入模板.xls");
            InputStream inputStream = resource.getInputStream();
            byte[] fileBytes = StreamUtils.copyToByteArray(inputStream);

            // 对中文文件名进行URL编码
            String fileName = "收费赋码导入模板.xls";
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.add("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
            headers.setContentLength(fileBytes.length);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(fileBytes);
        } catch (IOException e) {
            throw new RuntimeException("模板文件下载失败", e);
        }
    }
}
