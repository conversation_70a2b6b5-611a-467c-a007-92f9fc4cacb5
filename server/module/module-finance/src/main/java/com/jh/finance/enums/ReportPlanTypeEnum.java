package com.jh.finance.enums;

/**
 * 计划管理政策类型枚举
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public enum ReportPlanTypeEnum {
    CATERING_ACCOMMODATION_FEE_REDUCTION("1", "减免省内餐饮住宿企业检验检测费用"),
    EPIDEMIC_PREVENTION_FEE_REDUCTION("2", "减免防疫防护产品质量检验检测费用"),
    TECHNICAL_INSTITUTION_FEE_REDUCTION("3", "省级和部分市级市场监管部门直属技术机构减半收取产品检验、计量器具检定校准、特种设备检验费用，减免标准咨询服务费用"),
    NON_PROFIT_LABORATORY_OPENING("4", "以非营利方式向企业开放实验室");

    private final String code;
    private final String value;

    ReportPlanTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 根据code获取枚举对象
     *
     * @param code code
     * @return 枚举对象
     */
    public static ReportPlanTypeEnum getPolicyTypeEnum(String code) {
        for (ReportPlanTypeEnum e : ReportPlanTypeEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }

    /**
     * 根据value获取枚举对象
     *
     * @param value value
     * @return 枚举对象
     */
    public static ReportPlanTypeEnum getPolicyTypeEnumByValue(String value) {
        for (ReportPlanTypeEnum e : ReportPlanTypeEnum.values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}