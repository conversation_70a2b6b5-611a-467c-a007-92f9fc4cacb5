package com.jh.finance.enums;

import lombok.Getter;

@Getter
public enum TodoModuleEnum {
    ZJ_CHARGE("数据上报-收费统计-质检", "ZjCharge", "zjCharge", "datareport/charge/zjCharge", "质检"),
    JL_CHARGE("数据上报-收费统计-计量", "JlCharge", "jlCharge", "datareport/charge/jlCharge", "计量"),
    TJ_CHARGE("数据上报-收费统计-特检", "TjCharge", "tjCharge", "datareport/charge/tjCharge", "特检"),
    BZH_CHARGE("数据上报-收费统计-标准化", "BzhCharge", "bzhCharge", "datareport/charge/bzhCharge", "标准化"),
    ZSCQ_CHARGE("数据上报-收费统计-知识产权", "ZscqCharge", "zscqCharge", "datareport/charge/zscqCharge", "知识产权"),
    SCDBS_CHARGE("数据上报-收费统计-市场导报社", "ScdbsCharge", "scdbsCharge", "datareport/charge/scdbsCharge", "市场导报社"),
    GGJC_CHARGE("数据上报-收费统计-广告监测", "GgjcCharge", "ggjcCharge", "datareport/charge/ggjcCharge", "广告监测"),
    XH_CHARGE("数据上报-收费统计-学(协)会", "XhCharge", "xhCharge", "datareport/charge/xhCharge", "市场监管系统学（协）会"),
    SP_CHARGE("数据上报-收费统计-食品", "SpCharge", "spCharge", "datareport/charge/spCharge", "食品"),
    YP_CHARGE("数据上报-收费统计-药品", "YpCharge", "ypCharge", "datareport/charge/ypCharge", "药品"),
    ZJ_LIGHTEN("数据上报-消费减负-质检", "ZjLighten", "zjLighten", "datareport/lighten/zjLighten", "质检"),
    JL_LIGHTEN("数据上报-消费减负-计量", "JlLighten", "jlLighten", "datareport/lighten/jlLighten", "计量"),
    TJ_LIGHTEN("数据上报-消费减负-特检", "TjLighten", "tjLighten", "datareport/lighten/tjLighten", "特检"),
    BZH_LIGHTEN("数据上报-消费减负-标准化", "BzhLighten", "bzhLighten", "datareport/lighten/bzhLighten", "标准化"),
    ZSCQ_LIGHTEN("数据上报-消费减负-知识产权", "ZscqLighten", "zscqLighten", "datareport/lighten/zscqLighten", "知识产权"),
    SCDBS_LIGHTEN("数据上报-消费减负-市场导报社", "ScdbsLighten", "scdbsLighten", "datareport/lighten/scdbsLighten", "市场导报社"),
    GGJC_LIGHTEN("数据上报-消费减负-广告监测", "GgjcLighten", "ggjcLighten", "datareport/lighten/ggjcLighten", "广告监测"),
    XH_LIGHTEN("数据上报-消费减负-学(协)会", "XhLighten", "xhLighten", "datareport/lighten/xhLighten", "市场监管系统学（协）会"),
    SP_LIGHTEN("数据上报-消费减负-食品", "SpLighten", "spLighten", "datareport/lighten/spLighten", "食品"),
    YP_LIGHTEN("数据上报-消费减负-药品", "YpLighten", "ypLighten", "datareport/lighten/ypLighten", "药品"),

    TODO_REDUCE("数据上报-减负降本", "TodoReduce", "todoReduce", "datareport/reduce/todoReduce", "减负降本"),

    FILE_MANAGE("文件管理", "FileManage", "fileManage", "fileManage/manage", "文件管理");


    private final String displayName;
    private final String name;
    private final String path;
    private final String component;
    private final String typeName;

    TodoModuleEnum(String displayName, String name, String path, String component, String typeName) {
        this.displayName = displayName;
        this.name = name;
        this.path = path;
        this.component = component;
        this.typeName = typeName;
    }
}