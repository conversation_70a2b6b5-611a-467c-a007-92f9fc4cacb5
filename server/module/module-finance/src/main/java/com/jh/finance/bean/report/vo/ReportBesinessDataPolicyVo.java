package com.jh.finance.bean.report.vo;


import com.jh.finance.bean.report.ReportBesinessDataPolicy;
import io.swagger.v3.oas.annotations.media.Schema;


/**
 * 收费统计Vo
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Schema(description = "ReportBesinessDataPolicyVo")
public class ReportBesinessDataPolicyVo extends ReportBesinessDataPolicy {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}