package com.jh.finance.bean.report;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.jh.common.bean.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

import java.util.Date;

/**
 * 数据上报流程-审批意见表对象 report_advice
 *
 * <AUTHOR>
 * @date 2024-06-10
 */
@Table(name = "report_advice")
@Schema(description = "数据上报流程-审批意见表")
@Data
public class ReportAdvice extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "APPLY_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "主表ID")
    private String applyId;

    @Column(name = "FLOW_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "流程名称")
    private String flowName;

    @Column(name = "STAGE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "节点")
    private String stage;

    @Column(name = "AUDIT_STATUS")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "通过:1;驳回:0")
    private Integer auditStatus;

    @Column(name = "OPERATE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "同意或驳回")
    private String operate;

    @Column(name = "ADVICE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "意见")
    private String advice;
} 