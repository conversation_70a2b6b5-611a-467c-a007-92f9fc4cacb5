package com.jh.finance.controller.report;

import java.util.List;

import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.finance.bean.report.ReportBasiness;
import com.jh.finance.bean.report.vo.ReportBasinessVo;
import com.jh.finance.service.report.ReportBasinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.jh.common.bean.RestApiResponse;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.controller.BaseController;

import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

/**
 * 收费统计表Controller
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@RestController
@RequestMapping("/report/basiness")
@Tag(name = "收费统计表")
public class ReportBasinessController extends BaseController {
    @Autowired
    private ReportBasinessService reportBasinessService;

    private static final String PER_PREFIX = "btn:report:basiness:";

    /**
     * 新增收费统计表
     *
     * @param reportBasiness 收费统计表数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @Operation(summary = "新增收费统计表")
    @SystemLogAnnotation(type = "收费统计表", value = "新增收费统计表")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveReportBasiness(@RequestBody ReportBasiness reportBasiness) {
        String id = reportBasinessService.saveOrUpdateReportBasiness(reportBasiness);
        return RestApiResponse.ok(id);
    }

    /**
     * 修改收费统计表
     *
     * @param reportBasiness 收费统计表数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @Operation(summary = "修改收费统计表")
    @SystemLogAnnotation(type = "收费统计表", value = "修改收费统计表")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateReportBasiness(@RequestBody ReportBasiness reportBasiness) {
        String id = reportBasinessService.saveOrUpdateReportBasiness(reportBasiness);
        return RestApiResponse.ok(id);
    }

    /**
     * 批量删除收费统计表(判断 关联数据是否可以删除)
     *
     * @param ids
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @Operation(summary = "批量删除收费统计表")
    @SystemLogAnnotation(type = "收费统计表", value = "批量删除收费统计表")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteReportBasiness(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        reportBasinessService.deleteReportBasiness(ids);
        return RestApiResponse.ok();
    }

    /**
     * 查询收费统计表详情
     *
     * @param id
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findById")
    @Operation(summary = "查询收费统计表详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        ReportBasiness reportBasiness = reportBasinessService.findById(id);
        return RestApiResponse.ok(reportBasiness);
    }

    /**
     * 分页查询收费统计表
     *
     * @param reportBasinessVo 收费统计表 查询条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/findPageByQuery")
    @Operation(summary = "分页查询收费统计表")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody ReportBasinessVo reportBasinessVo) {
        PageInfo<ReportBasiness> reportBasiness = reportBasinessService.findPageByQuery(reportBasinessVo);
        return RestApiResponse.ok(reportBasiness);
    }
} 