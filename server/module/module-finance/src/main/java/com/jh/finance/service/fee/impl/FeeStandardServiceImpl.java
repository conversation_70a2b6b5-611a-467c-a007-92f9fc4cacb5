package com.jh.finance.service.fee.impl;

import java.util.ArrayList;
import java.util.List;
import java.math.BigDecimal;

import com.jh.common.bean.SysUser;
import com.jh.common.constant.CommonConstant;
import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.AppUserUtil;
import com.jh.common.util.security.UUIDUtils;
import com.jh.finance.bean.basic.BasicUserInfo;
import com.jh.finance.bean.fee.FeeStandard;
import com.jh.finance.bean.fee.vo.FeeStandardVo;
import com.jh.finance.bean.fee.vo.FeeStandardDataPushRequest;
import com.jh.finance.dao.fee.FeeStandardMapper;
import com.jh.finance.service.basic.BasicUserInfoService;
import com.jh.finance.service.fee.FeeStandardService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import static com.github.pagehelper.page.PageMethod.orderBy;

/**
 * 收费信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Service
@Transactional(readOnly = true)
public class FeeStandardServiceImpl extends BaseServiceImpl<FeeStandardMapper, FeeStandard> implements FeeStandardService {

    private static final Logger logger = LoggerFactory.getLogger(FeeStandardServiceImpl.class);
    @Autowired
    private FeeStandardMapper feeStandardMapper;
    @Autowired
    private BasicUserInfoService basicUserInfoService;


    @Override
    @Transactional(readOnly = false)
    /**
     * 保存或更新收费信息
     *@param feeStandard 收费信息对象
     *@return String 收费信息ID
     *<AUTHOR>
     */
    public String saveOrUpdateFeeStandard(FeeStandard feeStandard) {
        if (feeStandard == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(feeStandard.getId())) {
            //新增
            feeStandard.setId(UUIDUtils.getUUID());
            feeStandardMapper.insertSelective(feeStandard);
        } else {
            //TODO 做判断后方能执行修改 一般判断是否是自己的数据
            //避免页面传入修改
            feeStandard.setYn(null);
            feeStandardMapper.updateByPrimaryKeySelective(feeStandard);
        }
        return feeStandard.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     * 删除收费信息
     *@param ids void 收费信息ID
     *<AUTHOR>
     */
    public void deleteFeeStandard(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除 一般判断是否是自己的数据
            FeeStandard feeStandard = feeStandardMapper.selectByPrimaryKey(id);
            if (feeStandard == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            FeeStandard temfeeStandard = new FeeStandard();
            temfeeStandard.setYn(CommonConstant.FLAG_NO);
            temfeeStandard.setId(feeStandard.getId());
            feeStandardMapper.updateByPrimaryKeySelective(temfeeStandard);
        }
    }

    /**
     * 查询收费信息详情
     *
     * @param id
     * @return FeeStandard
     * <AUTHOR>
     */
    @Override
    public FeeStandard findById(String id) {
        // TODO 做判断后方能反回数据 一般判断是否是自己的数据
        return feeStandardMapper.selectByPrimaryKey(id);
    }


    /**
     * 分页查询收费信息
     *
     * @param feeStandardVo
     * @return PageInfo<FeeStandard>
     * <AUTHOR>
     */
    @Override
    public PageInfo<FeeStandard> findPageByQuery(FeeStandardVo feeStandardVo) {
        // TODO 做判断后方能执行查询 一般判断是否是自己的数据
        PageHelper.startPage(feeStandardVo.getPageNum(), feeStandardVo.getPageSize());
        orderBy(feeStandardVo.getOrderColumn() + " " + feeStandardVo.getOrderValue());
        Example example = getExample(feeStandardVo);
        List<FeeStandard> feeStandardList = feeStandardMapper.selectByExample(example);
        return new PageInfo<FeeStandard>(feeStandardList);
    }

    private Example getExample(FeeStandardVo feeStandardVo) {
        Example example = new Example(FeeStandard.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        criteria.andEqualTo("orgCode", AppUserUtil.getCurrentUserName());
        example.orderBy("createTime").desc();
        return example;
    }

    /**
     * 按条件导出查询收费信息
     *
     * @param feeStandardVo
     * @return PageInfo<FeeStandard>
     * <AUTHOR>
     */
    @Override
    public List<FeeStandard> findByQuery(FeeStandardVo feeStandardVo) {
        orderBy(feeStandardVo.getOrderColumn() + " " + feeStandardVo.getOrderValue());
        Example example = getExample(feeStandardVo);
        return feeStandardMapper.selectByExample(example);
    }

    /**
     * 处理外部推送的收费数据
     *
     * @param request 推送数据请求
     * @return String 处理结果信息
     * <AUTHOR>
     */
    @Override
    @Transactional(readOnly = false)
    public String processPushData(FeeStandardDataPushRequest request) {
        logger.info("收费数据推送请求：{}", request);

        // 验证请求参数
        if (request == null || request.getData() == null || request.getData().isEmpty()) {
            throw new ServiceException("推送数据不能为空");
        }

        if (StringUtils.isEmpty(request.getToken())) {
            throw new ServiceException("Token不能为空");
        }

        // 验证Token并获取用户信息
        SysUser user = basicUserInfoService.validateTokenAndGetUser(request.getToken());
        if (user == null) {
            throw new ServiceException("用户信息验证不通过");
        }

        // 处理数据
        int processedCount = 0;

        for (FeeStandardVo item : request.getData()) {
            // 验证必要字段
            if (StringUtils.isEmpty(item.getOrgCode())) {
                throw new ServiceException("单位统一社会信用代码不能为空");
            }

            if (StringUtils.isEmpty(item.getChargeCode())) {
                throw new ServiceException("收费编码不能为空");
            }

            // 根据IsUpdate字段进行不同操作
            Integer isUpdate = item.getIsUpdate();
            if (isUpdate == null) {
                isUpdate = 2; // 默认为新增
            }

            switch (isUpdate) {
                case 1:
                    // 更新操作
                    Example updateExample = new Example(FeeStandard.class);
                    Criteria updateCriteria = updateExample.createCriteria();
                    updateCriteria.andEqualTo("orgCode", item.getOrgCode());
                    updateCriteria.andEqualTo("chargeCode", item.getChargeCode());
                    updateCriteria.andEqualTo("yn", CommonConstant.FLAG_YES);

                    List<FeeStandard> existingUpdateList = feeStandardMapper.selectByExample(updateExample);
                    if (existingUpdateList == null || existingUpdateList.isEmpty()) {
                        throw new ServiceException("未找到要更新的记录: orgCode=" + item.getOrgCode() + ", chargeCode=" + item.getChargeCode());
                    }

                    FeeStandard existing = existingUpdateList.get(0);
                    updateFeeStandardFields(existing, item);
                    // 设置更新用户信息
                    existing.setUpdateUser(user.getUsername());
                    existing.setUpdateUserNickname(user.getNickname());
                    feeStandardMapper.updateByPrimaryKeySelective(existing);
                    logger.info("更新收费数据: orgCode={}, chargeCode={}", item.getOrgCode(), item.getChargeCode());
                    break;

                case 2:
                    // 新增操作
                    FeeStandard feeStandard = new FeeStandard();
                    updateFeeStandardFields(feeStandard, item);
                    feeStandard.setId(UUIDUtils.getUUID());
                    // 设置创建用户信息
                    feeStandard.setCreateUser(user.getUsername());
                    feeStandard.setCreateUserNickname(user.getNickname());
                    feeStandardMapper.insertSelective(feeStandard);
                    logger.info("新增收费数据: orgCode={}, chargeCode={}", item.getOrgCode(), item.getChargeCode());
                    break;

                case 3:
                    // 删除操作
                    Example deleteExample = new Example(FeeStandard.class);
                    Criteria deleteCriteria = deleteExample.createCriteria();
                    deleteCriteria.andEqualTo("orgCode", item.getOrgCode());
                    deleteCriteria.andEqualTo("chargeCode", item.getChargeCode());
                    deleteCriteria.andEqualTo("yn", CommonConstant.FLAG_YES);

                    List<FeeStandard> existingDeleteList = feeStandardMapper.selectByExample(deleteExample);
                    if (existingDeleteList == null || existingDeleteList.isEmpty()) {
                        throw new ServiceException("未找到要删除的记录: orgCode=" + item.getOrgCode() + ", chargeCode=" + item.getChargeCode());
                    }

                    for (FeeStandard existingDelete : existingDeleteList) {
                        existingDelete.setYn(CommonConstant.FLAG_NO);
                        // 设置更新用户信息（逻辑删除也是更新操作）
                        existingDelete.setUpdateUser(user.getUsername());
                        existingDelete.setUpdateUserNickname(user.getNickname());
                        feeStandardMapper.updateByPrimaryKeySelective(existingDelete);
                    }
                    logger.info("删除收费数据: orgCode={}, chargeCode={}", item.getOrgCode(), item.getChargeCode());
                    break;

                default:
                    throw new ServiceException("无效的操作类型: " + isUpdate);
            }

            processedCount++;
        }

        logger.info("推送数据处理完成，总共处理 {} 条数据", processedCount);
        return "成功处理 " + processedCount + " 条数据";
    }

    /**
     * 更新FeeStandard字段
     */
    private void updateFeeStandardFields(FeeStandard target, FeeStandard source) {
        BeanUtils.copyProperties(source, target, "id", "yn");

        // 处理金额字段转换
        if (source.getMoney() != null) {
            target.setMoney(source.getMoney());
        }
    }

    /**
     * 根据机构代码查询检验检测服务项目（标准类）
     *
     * @param orgCode 机构代码
     * @return List<FeeStandard> 标准类收费信息列表
     * <AUTHOR>
     */
    @Override
    public List<FeeStandard> findStandardByOrgCode(String orgCode) {
        return feeStandardMapper.findStandardByOrgCode(orgCode);
    }

}
