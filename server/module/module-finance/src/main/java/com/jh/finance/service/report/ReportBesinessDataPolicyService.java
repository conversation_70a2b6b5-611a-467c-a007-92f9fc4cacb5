package com.jh.finance.service.report;

import java.util.List;
import com.github.pagehelper.PageInfo;

import com.jh.finance.bean.report.ReportBesinessDataPolicy;
import com.jh.finance.bean.report.vo.ReportBesinessDataPolicyVo;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;
/**
 * 收费统计Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface ReportBesinessDataPolicyService{
	/**
	 * 保存或更新收费统计
	 *@param reportBesinessDataPolicy 收费统计对象
	 *@return String 收费统计ID
	 *<AUTHOR>
	 */
	String saveOrUpdateReportBesinessDataPolicy(ReportBesinessDataPolicy reportBesinessDataPolicy);
	
	/**
	 * 删除收费统计
	 *@param ids void 收费统计ID
	 *<AUTHOR>
	 */
	void deleteReportBesinessDataPolicy(List<String> ids);


	/**
	 * 分页查询收费统计
	 *@param reportBesinessDataPolicyVo
	 *@return PageInfo<ReportBesinessDataPolicy>
	 *<AUTHOR>
	 */
	PageInfo<ReportBesinessDataPolicy> findPageByQuery(ReportBesinessDataPolicyVo reportBesinessDataPolicyVo);

}
