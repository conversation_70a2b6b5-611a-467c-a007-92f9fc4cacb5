package com.jh.finance.service.report.impl;

import com.jh.common.bean.SysRole;
import com.jh.common.bean.SysUser;
import com.jh.common.util.AppUserUtil;
import com.jh.constant.FlowConstant;
import com.jh.finance.bean.report.ReportDocument;
import com.jh.finance.bean.report.ReportAdvice;
import com.jh.finance.bean.report.vo.ReportDocumentVo;
import com.jh.finance.constant.RoleConstant;
import com.jh.finance.dao.report.ReportDocumentMapper;
import com.jh.finance.dao.report.ReportAdviceMapper;
import com.jh.finance.dao.report.ReportDocumentFileMapper;
import com.jh.finance.bean.report.ReportDocumentFile;
import com.jh.finance.service.report.ReportDocumentService;
import com.jh.finance.enums.FeeReportStatusEnum;
import com.jh.sys.dao.SysUserMapper;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;
import com.jh.common.constant.CommonConstant;

import static com.jh.common.controller.BaseController.getUserRoleList;

import java.util.Date;

/**
 * 文件管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@Service
@Transactional(readOnly = true)
public class ReportDocumentServiceImpl extends BaseServiceImpl<ReportDocumentMapper, ReportDocument> implements ReportDocumentService {

    private static final Logger logger = LoggerFactory.getLogger(ReportDocumentServiceImpl.class);
    @Autowired
    private ReportDocumentMapper reportDocumentMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private ReportAdviceMapper reportAdviceMapper;

    @Autowired
    private ReportDocumentFileMapper reportDocumentFileMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     * 保存或更新文件管理
     *@param reportDocument 文件管理对象
     *@return String 文件管理ID
     *<AUTHOR>
     */
    public String saveOrUpdateReportDocument(ReportDocument reportDocument) {
        if (reportDocument == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(reportDocument.getId())) {
            //新增
            reportDocument.setId(UUIDUtils.getUUID());
            reportDocumentMapper.insertSelective(reportDocument);
        } else {
            //TODO 做判断后方能执行修改 一般判断是否是自己的数据
            //避免页面传入修改
            reportDocument.setYn(null);
            reportDocumentMapper.updateByPrimaryKeySelective(reportDocument);
        }
        return reportDocument.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     * 删除文件管理
     *@param ids void 文件管理ID
     *<AUTHOR>
     */
    public void deleteReportDocument(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除 一般判断是否是自己的数据
            ReportDocument reportDocument = reportDocumentMapper.selectByPrimaryKey(id);
            if (reportDocument == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            ReportDocument temreportDocument = new ReportDocument();
            temreportDocument.setYn(CommonConstant.FLAG_NO);
            temreportDocument.setId(reportDocument.getId());
            reportDocumentMapper.updateByPrimaryKeySelective(temreportDocument);
        }
    }

    /**
     * 查询文件管理详情
     *
     * @param id
     * @return ReportDocument
     * <AUTHOR>
     */
    public ReportDocument findById(String id) {
        // TODO 做判断后方能反回数据 一般判断是否是自己的数据
        return reportDocumentMapper.selectByPrimaryKey(id);
    }


    /**
     * 分页查询文件管理
     *
     * @param reportDocumentVo
     * @return PageInfo<ReportDocument>
     * <AUTHOR>
     */
    @Override
    public PageInfo<ReportDocument> findPageByQuery(ReportDocumentVo reportDocumentVo) {
        // 分页设置
        PageHelper.startPage(reportDocumentVo.getPageNum(), reportDocumentVo.getPageSize());

        // 检查用户角色权限
        checkUserRole(reportDocumentVo);

        List<ReportDocument> reportDocumentList = reportDocumentMapper.query(reportDocumentVo);

        return new PageInfo<ReportDocument>(reportDocumentList);
    }

    /**
     * 文件管理上报
     *
     * @param vo 包含上报参数的VO对象
     */
    @Override
    @Transactional(readOnly = false)
    public void documentReport(ReportDocumentVo vo) {
        // 验证参数
        if (vo == null) {
            throw new ServiceException("数据异常");
        }

        // 判断是新增还是更新
        boolean isUpdate = !StringUtils.isEmpty(vo.getId());
        ReportDocument existingData = null;

        if (isUpdate) {
            // 更新现有数据
            existingData = reportDocumentMapper.selectByPrimaryKey(vo.getId());
            if (existingData == null) {
                throw new ServiceException("要更新的数据不存在");
            }

            // 检查数据状态是否允许更新
            if (FeeReportStatusEnum.YCL.getCode().equals(existingData.getActNodeKey())) {
                throw new ServiceException("该数据已审核通过，不能再次修改");
            }

            // 只有创建者可以修改
            if (!existingData.getUserId().equals(AppUserUtil.getCurrentUserId())) {
                throw new ServiceException("只有创建者可以修改");
            }
        }

        // 设置状态
        String actNodeName = vo.getIsCommit() == 1 ? FeeReportStatusEnum.DCL.getValue() : FeeReportStatusEnum.DTC.getValue();
        String actNodeKey = vo.getIsCommit() == 1 ? FeeReportStatusEnum.DCL.getCode() : FeeReportStatusEnum.DTC.getCode();
        String nextFlow = vo.getIsCommit() == 1 ? "提交" : "保存";

        ReportDocument data;

        if (isUpdate) {
            // 更新现有数据
            data = existingData;
            // 更新主表字段
            data.setActNodeKey(actNodeKey);
            data.setActNodeName(actNodeName);
            data.setTitle(vo.getTitle());
            data.setNumber(vo.getNumber());
            data.setDtype(vo.getDtype());
            data.setDgrade(vo.getDgrade());
            data.setPubDate(vo.getPubDate());

            data.setIsCommit(vo.getIsCommit());

            // 更新主表
            reportDocumentMapper.updateByPrimaryKeySelective(data);

            // 删除原有文件记录
            reportDocumentFileMapper.deleteByDocumentId(data.getId());
        } else {
            // 新增数据
            data = new ReportDocument();
            data.setId(UUIDUtils.getUUID());
            data.setFlowName(FlowConstant.REPORT_DOCUMENT);
            data.setFlowKey(FlowConstant.REPORT_DOCUMENT_KEY);
            data.setActNodeKey(actNodeKey);
            data.setActNodeName(actNodeName);
            data.setUserId(AppUserUtil.getCurrentUserId());
            data.setTitle(vo.getTitle());
            data.setNumber(vo.getNumber());
            data.setDtype(vo.getDtype());
            data.setDgrade(vo.getDgrade());
            data.setPubDate(vo.getPubDate());

            data.setIsCommit(vo.getIsCommit());
            data.setYn(CommonConstant.FLAG_YES);
            // 插入主表数据
            reportDocumentMapper.insertSelective(data);
        }

        // 保存文件信息
        if (vo.getFileList() != null && !vo.getFileList().isEmpty()) {
            int sortOrder = 1;
            for (ReportDocumentVo.FileInfo fileInfo : vo.getFileList()) {
                ReportDocumentFile documentFile = new ReportDocumentFile();
                documentFile.setId(UUIDUtils.getUUID());
                documentFile.setDocumentId(data.getId());
                documentFile.setFileName(fileInfo.getFileName());
                documentFile.setFileUrl(fileInfo.getFileUrl());
                documentFile.setFilePath(fileInfo.getFilePath());
                documentFile.setSortOrder(sortOrder++);
                documentFile.setYn(CommonConstant.FLAG_YES);
                reportDocumentFileMapper.insertSelective(documentFile);
            }
        }

        // 添加审批流程记录
        ReportAdvice advice = new ReportAdvice();
        advice.setId(UUIDUtils.getUUID());
        advice.setApplyId(data.getId());
        advice.setFlowName(FlowConstant.REPORT_DOCUMENT);
        advice.setStage(actNodeKey);
        advice.setOperate(nextFlow);
        advice.setAdvice(nextFlow);
        reportAdviceMapper.insertSelective(advice);
    }

    /**
     * 文件管理审核
     *
     * @param vo 包含审核参数的VO对象
     */
    @Override
    @Transactional(readOnly = false)
    public void documentAudit(ReportDocumentVo vo) {
        // 1. 验证参数
        if (StringUtils.isEmpty(vo.getId())) {
            throw new ServiceException("主表ID不能为空");
        }
        if (vo.getAuditStatus() == null) {
            throw new ServiceException("审核状态不能为空");
        }

        // 2. 验证数据是否存在
        ReportDocument data = findById(vo.getId());
        if (data == null) {
            throw new ServiceException("数据不存在");
        }

        // 3. 获取当前用户角色
        List<SysRole> roleList = getUserRoleList();
        boolean isFinance = false;

        if (roleList != null && !roleList.isEmpty()) {
            for (SysRole role : roleList) {
                String roleCode = role.getRoleCode();
                if (RoleConstant.ROLE_FINANCE.equals(roleCode)) {
                    isFinance = true;
                    break;
                }
            }
        }

        // 4. 验证状态是否正确
        String currentNodeKey = data.getActNodeKey();
        String nextNodeKey;
        String nextNodeName;

        // 财务处审核
        if (FeeReportStatusEnum.DCL.getCode().equals(currentNodeKey) && isFinance) {
            if (vo.getAuditStatus() == 1) {
                // 通过，流程结束
                nextNodeKey = FeeReportStatusEnum.YCL.getCode(); // 已审核
                nextNodeName = FeeReportStatusEnum.YCL.getValue();
            } else {
                // 驳回
                nextNodeKey = FeeReportStatusEnum.DTC.getCode(); // 待提交
                nextNodeName = FeeReportStatusEnum.DTC.getValue();
            }
        } else {
            throw new ServiceException("当前状态不允许进行审核操作或您没有权限进行审核");
        }

        String operateDesc = vo.getAuditStatus() == 1 ? "通过" : "驳回";

        // 5. 添加审批记录
        ReportAdvice reportAdvice = new ReportAdvice();
        reportAdvice.setId(UUIDUtils.getUUID());
        reportAdvice.setApplyId(vo.getId());
        reportAdvice.setFlowName("文件管理流程");
        reportAdvice.setStage(nextNodeKey);
        reportAdvice.setAuditStatus(vo.getAuditStatus());
        reportAdvice.setOperate(operateDesc);
        reportAdvice.setAdvice(vo.getAdvice());
        reportAdviceMapper.insertSelective(reportAdvice);

        // 6. 更新主表状态
        ReportDocument updateData = new ReportDocument();
        updateData.setId(vo.getId());
        updateData.setActNodeKey(nextNodeKey);
        updateData.setActNodeName(nextNodeName);

        // 如果是驳回，重置提交状态
        if (vo.getAuditStatus() == 0) {
            updateData.setIsCommit(CommonConstant.FLAG_NO);
        }

        reportDocumentMapper.updateByPrimaryKeySelective(updateData);
    }

    /**
     * 检查用户角色权限
     *
     * @param vo 查询参数对象
     */
    private void checkUserRole(ReportDocumentVo vo) {
        // 获取当前用户ID
        String userId = AppUserUtil.getCurrentUserId();
        vo.setUserId(userId);

        // 获取当前用户角色列表
        List<SysRole> roleList = getUserRoleList();

        // 判断用户角色
        boolean hasOrgRole = false;
        boolean hasAdminRole = false;
        boolean hasFinanceRole = false;

        // 检查用户是否有机构角色
        if (roleList != null && !roleList.isEmpty()) {
            for (SysRole role : roleList) {
                String roleCode = role.getRoleCode();
                if (RoleConstant.ROLE_ORG.equals(roleCode)) {
                    hasOrgRole = true;
                } else if (RoleConstant.ROLE_ADMIN.equals(roleCode) || RoleConstant.ROLE_SUPERADMIN.equals(roleCode)) {
                    hasAdminRole = true;
                } else if (RoleConstant.ROLE_FINANCE.equals(roleCode)) {
                    hasFinanceRole = true;
                }
            }
        }

        // 设置角色标志
        vo.setHasOrgRole(hasOrgRole);
        vo.setHasAdminRole(hasAdminRole);
        vo.setHasFinanceRole(hasFinanceRole);
    }

    /**
     * 根据文件管理ID查询附件列表
     *
     * @param documentId 文件管理ID
     * @return 附件列表
     */
    @Override
    public List<ReportDocumentFile> getFilesByDocumentId(String documentId) {
        if (StringUtils.isEmpty(documentId)) {
            return new ArrayList<>();
        }
        return reportDocumentFileMapper.selectByDocumentId(documentId);
    }

    @Override
    public ReportDocumentVo findDetailById(String id) {
        // 获取文档基本信息
        ReportDocument document = findById(id);
        if (document == null) {
            return null;
        }
        
        // 获取关联的文件列表
        List<ReportDocumentFile> files = getFilesByDocumentId(id);
        List<ReportAdvice> reportAdviceList = reportAdviceMapper.findByApplyId(id);

        ReportDocumentVo vo = new ReportDocumentVo();
        BeanUtils.copyProperties(document, vo);

        List<ReportDocumentVo.FileInfo> fileInfoList = new ArrayList<>();
        if (files != null && !files.isEmpty()) {
            for (ReportDocumentFile file : files) {
                ReportDocumentVo.FileInfo fileInfo = new ReportDocumentVo.FileInfo();
                fileInfo.setFileName(file.getFileName());
                fileInfo.setFileUrl(file.getFileUrl());
                fileInfo.setFilePath(file.getFilePath());
                fileInfo.setUrl(file.getFileUrl());
                fileInfo.setName(file.getFileName());
                fileInfo.setStatus("done");
                fileInfoList.add(fileInfo);
            }
        }
        vo.setFileList(fileInfoList);
        vo.setReportAdviceList(reportAdviceList);
        
        return vo;
    }
}
