package com.jh.finance.service.fee;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.jh.finance.bean.fee.FeeCoding;
import com.jh.finance.bean.fee.vo.FeeCodingVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 收费赋码Service接口
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface FeeCodingService {
    /**
     * 保存或更新收费赋码
     *
     * @param feeCoding 收费赋码对象
     * @return String 收费赋码ID
     * <AUTHOR>
     */
    String saveOrUpdateFeeCoding(FeeCoding feeCoding);

    /**
     * 删除收费赋码
     *
     * @param ids void 收费赋码ID
     * <AUTHOR>
     */
    void deleteFeeCoding(List<String> ids);

    /**
     * 查询收费赋码详情
     *
     * @param id
     * @return FeeCoding
     * <AUTHOR>
     */
    FeeCoding findById(String id);

    /**
     * 分页查询收费赋码
     *
     * @param feeCodingVo
     * @return PageInfo<FeeCoding>
     * <AUTHOR>
     */
    PageInfo<FeeCoding> findPageByQuery(FeeCodingVo feeCodingVo);

    /**
     * 按条件导出查询收费赋码
     *
     * @param feeCodingVo
     * @return PageInfo<FeeCoding>
     * <AUTHOR>
     */
    List<FeeCoding> findByQuery(FeeCodingVo feeCodingVo);

    /**
     * 导入收费赋码
     *
     * @param file
     * @param cover 是否覆盖 1 覆盖 0 不覆盖
     * @return
     * <AUTHOR>
     */
    void importFeeCodingAsync(MultipartFile file, Integer cover);
}
