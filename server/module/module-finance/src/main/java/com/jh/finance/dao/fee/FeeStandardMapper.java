package com.jh.finance.dao.fee;

import com.jh.common.mybatis.BaseInfoMapper;
import com.jh.finance.bean.fee.FeeStandard;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 收费信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
public interface FeeStandardMapper extends BaseInfoMapper<FeeStandard> {

    /**
     * 根据机构代码查询检验检测服务项目（标准类）
     *
     * @param orgCode 机构代码
     * @return 标准类收费信息列表
     */
    @Select("SELECT * FROM fee_standard WHERE ORG_CODE = #{orgCode} AND YN = 1 ORDER BY CREATE_TIME DESC")
    List<FeeStandard> findStandardByOrgCode(@Param("orgCode") String orgCode);

}
