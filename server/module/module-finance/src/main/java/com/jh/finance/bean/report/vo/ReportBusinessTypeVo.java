package com.jh.finance.bean.report.vo;

import com.jh.finance.bean.report.ReportBusinessType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 业务类型表Vo
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Schema(description = "ReportBusinessTypeVo")
@Data
public class ReportBusinessTypeVo extends ReportBusinessType {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;

    @Schema(description = "是否只查询顶级")
    private Boolean onlyParent;

    @Schema(description = "是否包含子级")
    private Boolean includeChildren;
} 