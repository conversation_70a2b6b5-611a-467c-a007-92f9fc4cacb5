package com.jh.finance.bean.fee;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.jh.common.bean.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

/**
 * 减费惠企改革-在线反馈对象 fee_reduction_feedback
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Table(name = "fee_reduction_feedback")
@Schema(description = "减费惠企改革-在线反馈")
@Data
public class FeeReductionFeedback extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "USER_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "用户ID")
    private String userId;
    @Column(name = "ENTERPRISE_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "企业名称")
    private String enterpriseName;
    @Column(name = "DISTRICT_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "区县名称")
    private String districtName;
    @Column(name = "POLICY_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "政策名称")
    private String policyName;
    @Column(name = "INDUSTRY_CATEGORY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "行业门类")
    private String industryCategory;
    @Column(name = "REDUCTION_AMOUNT")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @Schema(description = "本次减免费用(元)")
    private BigDecimal reductionAmount;
    @Column(name = "ENJOY_TIME")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @Schema(description = "享受时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date enjoyTime;
    @Column(name = "STATUS")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "状态：0-未反馈，1-已反馈")
    private Integer status;

}
