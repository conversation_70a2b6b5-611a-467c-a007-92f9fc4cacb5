package com.jh.finance.dao.report;


import com.jh.common.mybatis.BaseInfoMapper;
import com.jh.finance.bean.report.ReportReduce;
import com.jh.finance.bean.report.vo.ReportReduceVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 减负降本数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Mapper
public interface ReportReduceMapper extends BaseInfoMapper<ReportReduce> {

    /**
     * 根据用户角色和区域查询数据
     *
     * @param vo 查询参数
     * @return 减负降本数据列表
     */
    List<ReportReduce> query(ReportReduceVo vo);

    /**
     * 查询代办
     *
     * @param vo 查询参数
     * @return 减负降本数据列表
     */
    List<ReportReduce> queryToDo(ReportReduceVo vo);

    /**
     * 根据用户ID和提交状态查询数据
     *
     * @param userId 用户ID
     * @param isCommit 提交状态
     * @return 减负降本数据列表
     */
    List<ReportReduce> findByUserIdAndCommitStatus(@Param("userId") String userId, @Param("isCommit") Integer isCommit);
}
