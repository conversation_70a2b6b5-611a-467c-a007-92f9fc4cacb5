package com.jh.finance.bean.report;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.jh.common.bean.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

/**
 * 业务类型政策对象 report_business_type_policy
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Table(name = "report_business_type_policy")
@Schema(description = "业务类型政策")
public class ReportBusinessTypePolicy extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "BUSINESS_TYPE_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "业务类型ID")
    private String businessTypeId;
    @Column(name = "TYPE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "类型")
    private String type;
    @Column(name = "POLICY_CONTENT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "减负政策内容")
    private String policyContent;
    @Column(name = "FILE_NO")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "文件号")
    private String fileNo;
    @Column(name = "EXECUTION_TIME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "执行时间")
    private String executionTime;
    @Column(name = "SORT")
    @ColumnType(jdbcType = JdbcType.BIGINT)
    @Schema(description = "排序")
    private Long sort;
    @Column(name = "CONNECT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "连接")
    private String connect;
    @Column(name = "NUM")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "数量")
    private String num;
    @Column(name = "START_TIME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "开始时间")
    private String startTime;
    @Column(name = "POLICY_TYPE")
    @ColumnType(jdbcType = JdbcType.BIGINT)
    @Schema(description = "政策类型")
    private Long policyType;
    @Column(name = "ORG_ID")
    @ColumnType(jdbcType = JdbcType.BIGINT)
    @Schema(description = "组织ID")
    private Long orgId;

    /**
     * SET 业务类型ID
     *
     * @param businessTypeId
     */
    public void setBusinessTypeId(String businessTypeId) {
        this.businessTypeId = businessTypeId == null ? null : businessTypeId;
    }

    /**
     * GET 业务类型ID
     *
     * @return businessTypeId
     */
    public String getBusinessTypeId() {
        return businessTypeId;
    }

    /**
     * SET 类型
     *
     * @param type
     */
    public void setType(String type) {
        this.type = type == null ? null : type;
    }

    /**
     * GET 类型
     *
     * @return type
     */
    public String getType() {
        return type;
    }

    /**
     * SET 减负政策内容
     *
     * @param policyContent
     */
    public void setPolicyContent(String policyContent) {
        this.policyContent = policyContent == null ? null : policyContent;
    }

    /**
     * GET 减负政策内容
     *
     * @return policyContent
     */
    public String getPolicyContent() {
        return policyContent;
    }

    /**
     * SET 文件号
     *
     * @param fileNo
     */
    public void setFileNo(String fileNo) {
        this.fileNo = fileNo == null ? null : fileNo;
    }

    /**
     * GET 文件号
     *
     * @return fileNo
     */
    public String getFileNo() {
        return fileNo;
    }

    /**
     * SET 执行时间
     *
     * @param executionTime
     */
    public void setExecutionTime(String executionTime) {
        this.executionTime = executionTime == null ? null : executionTime;
    }

    /**
     * GET 执行时间
     *
     * @return executionTime
     */
    public String getExecutionTime() {
        return executionTime;
    }

    /**
     * SET 排序
     *
     * @param sort
     */
    public void setSort(Long sort) {
        this.sort = sort;
    }

    /**
     * GET 排序
     *
     * @return sort
     */
    public Long getSort() {
        return sort;
    }

    /**
     * SET 连接
     *
     * @param connect
     */
    public void setConnect(String connect) {
        this.connect = connect == null ? null : connect;
    }

    /**
     * GET 连接
     *
     * @return connect
     */
    public String getConnect() {
        return connect;
    }

    /**
     * SET 数量
     *
     * @param num
     */
    public void setNum(String num) {
        this.num = num == null ? null : num;
    }

    /**
     * GET 数量
     *
     * @return num
     */
    public String getNum() {
        return num;
    }

    /**
     * SET 开始时间
     *
     * @param startTime
     */
    public void setStartTime(String startTime) {
        this.startTime = startTime == null ? null : startTime;
    }

    /**
     * GET 开始时间
     *
     * @return startTime
     */
    public String getStartTime() {
        return startTime;
    }

    /**
     * SET 政策类型
     *
     * @param policyType
     */
    public void setPolicyType(Long policyType) {
        this.policyType = policyType;
    }

    /**
     * GET 政策类型
     *
     * @return policyType
     */
    public Long getPolicyType() {
        return policyType;
    }

    /**
     * SET 组织ID
     *
     * @param orgId
     */
    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    /**
     * GET 组织ID
     *
     * @return orgId
     */
    public Long getOrgId() {
        return orgId;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }
}
