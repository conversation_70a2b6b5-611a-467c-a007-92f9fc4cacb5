package com.jh.finance.bean.report.vo;

import com.jh.finance.bean.report.ReportBusinessTypePolicy;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 业务类型政策Vo
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Schema(description = "ReportBusinessTypePolicyVo")
@Data
public class ReportBusinessTypePolicyVo extends ReportBusinessTypePolicy {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;

    private String orderColumn = "CREATE_TIME";
    private String orderValue = "DESC";

    @Schema(description = "按类型分组的政策数据")
    private List<Map<String, Object>> groupedPolicies;


}