package com.jh.finance.bean.basic.vo;


import com.jh.finance.bean.basic.BasicUserInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 用户平台信息Vo
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Schema(description = "BasicUserInfoVo")
@Data
public class BasicUserInfoVo extends BasicUserInfo {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;

}