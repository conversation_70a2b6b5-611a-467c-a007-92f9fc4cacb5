package com.jh.finance.service.report.impl;

import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.common.bean.LoginUser;
import com.jh.common.util.AppUserUtil;
import com.jh.common.util.io.FileConvertUtils;
import com.jh.common.util.io.FileUtil;
import com.jh.common.util.poi.EasyExcelUtils;
import com.jh.constant.enums.ImportStatusEnum;
import com.jh.finance.bean.report.ReportBesinessData;
import com.jh.finance.bean.report.ReportBesinessDataPolicy;
import com.jh.finance.bean.report.vo.ReportBesinessDataPolicyVo;
import com.jh.finance.dao.report.ReportBesinessDataMapper;
import com.jh.finance.dao.report.ReportBesinessDataPolicyMapper;
import com.jh.finance.service.report.ReportBesinessDataPolicyService;
import com.jh.utils.ImportService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.CollectionUtils;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.concurrent.CompletableFuture;

import java.util.List;
import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;


import com.jh.common.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;
import static com.github.pagehelper.page.PageMethod.orderBy;
import org.springframework.web.multipart.MultipartFile;

/**
 *  收费统计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Service
@Transactional(readOnly = true)
public class ReportBesinessDataPolicyServiceImpl extends BaseServiceImpl<ReportBesinessDataPolicyMapper, ReportBesinessDataPolicy> implements ReportBesinessDataPolicyService {
	
	private static final Logger logger = LoggerFactory.getLogger(ReportBesinessDataPolicyServiceImpl.class);
    @Autowired
    private ReportBesinessDataPolicyMapper reportBesinessDataPolicyMapper;

	@Autowired
	private ReportBesinessDataMapper reportBesinessDataMapper;

	@Value("${file.temp.path}")
	private String tempPath;

	@Override
	@Transactional(readOnly = false)
	/**
	 * 保存或更新收费统计
	 *@param reportBesinessDataPolicy 收费统计对象
	 *@return String 收费统计ID
	 *<AUTHOR>
	 */
	public String saveOrUpdateReportBesinessDataPolicy(ReportBesinessDataPolicy reportBesinessDataPolicy) {
		if(reportBesinessDataPolicy==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(reportBesinessDataPolicy.getId())){
			//新增
			reportBesinessDataPolicy.setId(UUIDUtils.getUUID());
			reportBesinessDataPolicyMapper.insertSelective(reportBesinessDataPolicy);
		}else{
			//TODO 做判断后方能执行修改 一般判断是否是自己的数据
			//避免页面传入修改
			reportBesinessDataPolicy.setYn(null);
			reportBesinessDataPolicyMapper.updateByPrimaryKeySelective(reportBesinessDataPolicy);
		}
		return reportBesinessDataPolicy.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 * 删除收费统计
	 *@param ids void 收费统计ID
	 *<AUTHOR>
	 */
	public void deleteReportBesinessDataPolicy(List<String> ids) {
		for(String id:ids){
			//TODO 做判断后方能执行删除 一般判断是否是自己的数据
			ReportBesinessDataPolicy reportBesinessDataPolicy=reportBesinessDataPolicyMapper.selectByPrimaryKey(id);
			if(reportBesinessDataPolicy==null){
				throw new ServiceException("非法请求");
			}
			//逻辑删除
			ReportBesinessDataPolicy temreportBesinessDataPolicy=new ReportBesinessDataPolicy();
			temreportBesinessDataPolicy.setYn(CommonConstant.FLAG_NO);
			temreportBesinessDataPolicy.setId(reportBesinessDataPolicy.getId());
			reportBesinessDataPolicyMapper.updateByPrimaryKeySelective(temreportBesinessDataPolicy);
		}
	}

	/**
	 * 分页查询收费统计
	 *@param reportBesinessDataPolicyVo
	 *@return PageInfo<ReportBesinessDataPolicy>
	 *<AUTHOR>
	 */
	@Override
	public PageInfo<ReportBesinessDataPolicy> findPageByQuery(ReportBesinessDataPolicyVo reportBesinessDataPolicyVo) {
		// TODO 做判断后方能执行查询 一般判断是否是自己的数据
		PageHelper.startPage(reportBesinessDataPolicyVo.getPageNum(),reportBesinessDataPolicyVo.getPageSize());
		orderBy(reportBesinessDataPolicyVo.getOrderColumn() + " " + reportBesinessDataPolicyVo.getOrderValue());
		Example example=getExample(reportBesinessDataPolicyVo);
		List<ReportBesinessDataPolicy> reportBesinessDataPolicyList=reportBesinessDataPolicyMapper.selectByExample(example);
		return new PageInfo<ReportBesinessDataPolicy>(reportBesinessDataPolicyList);
	}
	private Example getExample(ReportBesinessDataPolicyVo reportBesinessDataPolicyVo){
		Example example=new Example(ReportBesinessDataPolicy.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(reportBesinessDataPolicyVo.getName())){
		//	criteria.andEqualTo(reportBesinessDataPolicyVo.getName());
		//}
		example.orderBy("updateTime").desc();
		return example;
	}

}
