package com.jh.finance.service.report.impl;


import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.common.bean.LoginUser;
import com.jh.common.exception.ServiceException;
import com.jh.common.util.AppUserUtil;
import com.jh.common.util.io.FileConvertUtils;
import com.jh.common.util.io.FileUtil;
import com.jh.common.util.poi.EasyExcelUtils;
import com.jh.constant.FlowConstant;
import com.jh.constant.enums.ImportStatusEnum;
import com.jh.finance.bean.report.ReportAdvice;
import com.jh.finance.bean.report.ReportArea;
import com.jh.finance.bean.report.ReportEconomicsType;
import com.jh.finance.bean.report.ReportReduce;
import com.jh.finance.bean.report.vo.ReportReduceExcel;
import com.jh.finance.bean.report.vo.ReportReduceVo;
import com.jh.finance.dao.report.ReportAdviceMapper;
import com.jh.finance.dao.report.ReportAreaMapper;
import com.jh.finance.dao.report.ReportEconomicsTypeMapper;
import com.jh.finance.dao.report.ReportReduceMapper;
import com.jh.finance.enums.FeeReportStatusEnum;
import com.jh.finance.service.report.ReportReduceService;
import com.jh.finance.constant.RoleConstant;
import com.jh.common.bean.SysRole;
import com.jh.common.bean.SysUser;
import com.jh.sys.dao.SysUserMapper;
import com.jh.utils.ImportService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;


import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;

import com.jh.common.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import static com.github.pagehelper.page.PageMethod.orderBy;
import static com.jh.common.controller.BaseController.getUserRoleList;

import org.springframework.web.multipart.MultipartFile;

/**
 * 减负降本数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Service
@Transactional(readOnly = true)
public class ReportReduceServiceImpl extends BaseServiceImpl<ReportReduceMapper, ReportReduce> implements ReportReduceService {

    private static final Logger logger = LoggerFactory.getLogger(ReportReduceServiceImpl.class);
    @Autowired
    private ReportReduceMapper reportReduceMapper;

    @Autowired
    private ImportService importService;

    @Value("${file.temp.path}")
    private String tempPath;

    @Autowired
    private ReportAreaMapper reportAreaMapper;

    @Autowired
    private ReportEconomicsTypeMapper reportEconomicsTypeMapper;

    @Autowired
    private ReportAdviceMapper reportAdviceMapper;

    @Autowired
    private SysUserMapper sysUserMapper;


    @Override
    @Transactional(readOnly = false)
    /**
     * 保存或更新减负降本数据
     *@param reportReduce 减负降本数据对象
     *@return String 减负降本数据ID
     *<AUTHOR>
     */
    public String saveOrUpdateReportReduce(ReportReduce reportReduce) {
        if (reportReduce == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(reportReduce.getId())) {
            //新增
            reportReduce.setId(UUIDUtils.getUUID());
            reportReduceMapper.insertSelective(reportReduce);
        } else {
            //TODO 做判断后方能执行修改 一般判断是否是自己的数据
            //避免页面传入修改
            reportReduce.setYn(null);
            reportReduceMapper.updateByPrimaryKeySelective(reportReduce);
        }
        return reportReduce.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     * 删除减负降本数据
     *@param ids void 减负降本数据ID
     *<AUTHOR>
     */
    public void deleteReportReduce(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除 一般判断是否是自己的数据
            ReportReduce reportReduce = reportReduceMapper.selectByPrimaryKey(id);
            if (reportReduce == null) {
                throw new ServiceException("非法请求");
            }

            //逻辑删除
            ReportReduce temreportReduce = new ReportReduce();
            temreportReduce.setYn(CommonConstant.FLAG_NO);
            temreportReduce.setId(reportReduce.getId());
            reportReduceMapper.updateByPrimaryKeySelective(temreportReduce);
        }
    }

    /**
     * 查询减负降本数据详情
     *
     * @param id
     * @return ReportReduce
     * <AUTHOR>
     */
    @Override
    public ReportReduce findById(String id) {
        // TODO 做判断后方能反回数据 一般判断是否是自己的数据
        return reportReduceMapper.selectByPrimaryKey(id);
    }


    /**
     * 分页查询减负降本数据
     *
     * @param reportReduceVo
     * @return PageInfo<ReportReduce>
     * <AUTHOR>
     */
    @Override
    public PageInfo<ReportReduce> findPageByQuery(ReportReduceVo reportReduceVo) {
        // 分页设置
        PageHelper.startPage(reportReduceVo.getPageNum(), reportReduceVo.getPageSize());

        if (reportReduceVo.getIsCommit() == null) {
            throw new ServiceException("缺少IsCommit参数");
        }

        // 获取当前用户ID
        String userId = AppUserUtil.getCurrentUserId();
        reportReduceVo.setUserId(userId);

        // 获取当前用户角色列表
        List<SysRole> roleList = getUserRoleList();

        // 判断用户角色
        boolean hasOrgRole = false;
        boolean hasRegionRole = false;
        boolean hasAdminRole = false;
        boolean hasFinanceRole = false;
        boolean hasScienceRole = false;

        // 检查用户是否有机构角色或地市角色
        if (roleList != null && !roleList.isEmpty()) {
            for (SysRole role : roleList) {
                String roleCode = role.getRoleCode();
                if (RoleConstant.ROLE_ORG.equals(roleCode)) {
                    hasOrgRole = true;
                } else if (RoleConstant.ROLE_REGION.equals(roleCode)) {
                    hasRegionRole = true;
                } else if (RoleConstant.ROLE_ADMIN.equals(roleCode) || RoleConstant.ROLE_SUPERADMIN.equals(roleCode)) {
                    hasAdminRole = true;
                } else if (RoleConstant.ROLE_FINANCE.equals(roleCode)) {
                    hasFinanceRole = true;
                } else if (RoleConstant.ROLE_SCIENCE.equals(roleCode)) {
                    hasScienceRole = true;
                }
            }
        }

        // 设置角色标志
        reportReduceVo.setHasOrgRole(hasOrgRole);
        reportReduceVo.setHasRegionRole(hasRegionRole);
        reportReduceVo.setHasAdminRole(hasAdminRole);
        reportReduceVo.setHasFinanceRole(hasFinanceRole);
        reportReduceVo.setHasScienceRole(hasScienceRole);

        // 如果是地市角色、财务处或科技处角色，获取行政区划代码
        if (hasRegionRole || hasFinanceRole || hasScienceRole) {
            SysUser user = new SysUser();
            user.setId(userId);
            SysUser currentUser = sysUserMapper.selectOne(user);
            if (currentUser != null && currentUser.getAreaCode() != null) {
                String areaCode = currentUser.getAreaCode();
                // 如果是以"00"结尾的地市代码，去掉末尾的"00"
                if (areaCode.endsWith("00")) {
                    areaCode = areaCode.substring(0, areaCode.length() - 2);
                }
                reportReduceVo.setAreaCode(areaCode);
            }
        }

        List<ReportReduce> reportReduceList = reportReduceMapper.query(reportReduceVo);
        return new PageInfo<ReportReduce>(reportReduceList);
    }

    private Example getExample(ReportReduceVo reportReduceVo) {
        Example example = new Example(ReportReduce.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        //if(!StringUtils.isEmpty(reportReduceVo.getName())){
        //	criteria.andEqualTo(reportReduceVo.getName());
        //}
        example.orderBy("updateTime").desc();
        return example;
    }

    /**
     * 按条件导出查询减负降本数据
     *
     * @param reportReduceVo
     * @return PageInfo<ReportReduce>
     * <AUTHOR>
     */
    @Override
    public List<ReportReduce> findByQuery(ReportReduceVo reportReduceVo) {
        orderBy(reportReduceVo.getOrderColumn() + " " + reportReduceVo.getOrderValue());
        Example example = getExample(reportReduceVo);
        return reportReduceMapper.selectByExample(example);
    }

    /**
     * 导入减负降本数据
     *
     * @param file
     * @param cover 是否覆盖 1 覆盖 0 不覆盖
     * @return
     * <AUTHOR>
     */
    @Override
    @Transactional(readOnly = false)
    public void importReportReduceAsync(MultipartFile file, Integer cover) {
        // 为后续数据处理线程设置用户信息
        Object user = AppUserUtil.getLoginAppUser();
        LoginUser appLoginUser = (LoginUser) user;

//        // 先保存用户ID和用户名，避免在异步线程中出现空指针
        String userId = appLoginUser.getId();
        String userName = appLoginUser.getUsername();
        String userNickname = appLoginUser.getNickname();

        importService.notification(appLoginUser, "已创建减负降本数据导入任务，完成后会通知", ExportRespAnnotation.SocketMessageTypeEnum.NORMAL, "减负降本数据", null);

        // 开始时间
        Date startTime = new Date();

        // 验证excel
        importService.verifyExcel(file);
        List<ReportReduceExcel> list;

        // 读取数据
        try (InputStream inputStream = file.getInputStream()) {
            list = (List<ReportReduceExcel>) EasyExcelUtils.readExcel(ReportReduceExcel.class, inputStream);
            if (CollectionUtils.isEmpty(list)) {
                throw new ServiceException("模板文件为空或者异常，请使用提供的模板进行导入");
            }
        } catch (IOException e) {
            throw new ServiceException("请检查填写内容");
        }

        // 复制file文件
        String pathForSave = tempPath + "/" + UUIDUtils.getUUID() + "&&" + file.getOriginalFilename();
        File copyFile = FileUtil.copyFile(file, pathForSave);
        MultipartFile copyMultipartFile = FileConvertUtils.createFileItem(copyFile);

        // 异步处理数据
        CompletableFuture.runAsync(() -> {
            try {
                // 加载地区数据
                List<ReportArea> areaList = reportAreaMapper.selectAll();
                Map<String, ReportArea> cityCodeMap = areaList.stream()
                        .filter(area -> area.getLevel() == 1) // 地市级别
                        .collect(Collectors.toMap(
                                ReportArea::getCode,
                                area -> area,
                                // 处理重复键的情况，保留第一个遇到的值
                                (existing, replacement) -> existing
                        ));

                Map<String, ReportArea> countyCodeMap = areaList.stream()
                        .filter(area -> area.getLevel() == 2) // 区县级别
                        .collect(Collectors.toMap(
                                ReportArea::getCode,
                                area -> area,
                                // 处理重复键的情况，保留第一个遇到的值
                                (existing, replacement) -> existing
                        ));

                // 加载行业类型数据
                List<ReportEconomicsType> industryTypeList = reportEconomicsTypeMapper.selectAll();
                Map<String, ReportEconomicsType> categoryCodeMap = industryTypeList.stream()
                        .filter(type -> type.getClasslevel() == 1) // 行业门类
                        .collect(Collectors.toMap(
                                ReportEconomicsType::getCode,
                                type -> type,
                                // 处理重复键的情况，保留第一个遇到的值
                                (existing, replacement) -> existing
                        ));

                Map<String, ReportEconomicsType> classCodeMap = industryTypeList.stream()
                        .filter(type -> type.getClasslevel() == 2) // 行业大类
                        .collect(Collectors.toMap(
                                ReportEconomicsType::getCode,
                                type -> type,
                                // 处理重复键的情况，保留第一个遇到的值
                                (existing, replacement) -> existing
                        ));

                // 根据cover参数决定数据处理策略
                Set<String> existingUsccSet = new HashSet<>();
                Map<String, ReportReduce> existingDataMap = new HashMap<>();

                // 查询当前用户已存在的数据
                Example example = new Example(ReportReduce.class);
                example.createCriteria()
                        .andEqualTo("userId", userId);
                List<ReportReduce> existingData = reportReduceMapper.selectByExample(example);

                if (cover == 1) {
                    // 覆盖模式：记录已存在的数据，后续根据统一社会信用代码进行覆盖
                    existingDataMap = existingData.stream()
                            .filter(data -> data.getUscc() != null)
                            .collect(Collectors.toMap(
                                    ReportReduce::getUscc,
                                    data -> data,
                                    (existing, replacement) -> existing
                            ));
                } else {
                    // 不覆盖模式：记录已存在的统一社会信用代码，用于重复检查
                    existingUsccSet = existingData.stream()
                            .map(ReportReduce::getUscc)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
                }

                List<ReportReduce> insertList = new ArrayList<>();
                int totalRows = list.size();
                int validRows = 0;
                int errorRows = 0;
                logger.info("开始处理Excel数据，总行数: {}", totalRows);

                // 数据处理
                for (ReportReduceExcel item : list) {
                    // 检查是否为空行
                    if (isEmptyRow(item)) {
                        continue;
                    }

                    StringBuilder errorMsg = new StringBuilder();

                    // 必填字段验证
                    if (StringUtils.isEmpty(item.getCompanyName())) {
                        errorMsg.append("企业名称不能为空\n");
                    }

                    if (StringUtils.isEmpty(item.getUscc())) {
                        errorMsg.append("统一社会信用代码不能为空\n");
                    } else {
                        // 统一社会信用代码格式验证
                        String usccPattern = "^([0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}|[1-9]\\d{14})$";
                        if (!Pattern.matches(usccPattern, item.getUscc())) {
                            errorMsg.append("统一社会信用代码格式错误\n");
                        }
                    }

                    // 地市code验证
                    if (StringUtils.isEmpty(item.getCityCode())) {
                        errorMsg.append("地市code不能为空\n");
                    } else if (item.getCityCode().length() != 6) {
                        errorMsg.append("地市code格式不对\n");
                    } else if (!cityCodeMap.containsKey(item.getCityCode())) {
                        errorMsg.append("数据字典中没有此地市code\n");
                    } else if (!cityCodeMap.get(item.getCityCode()).getName().equals(item.getCityName())) {
                        errorMsg.append("地市code对应的地市名称错误\n");
                    }

                    // 区县code验证
                    if (StringUtils.isEmpty(item.getCountyCode())) {
                        errorMsg.append("区县code不能为空\n");
                    } else if (item.getCountyCode().length() != 6) {
                        errorMsg.append("区县code格式不对\n");
                    } else if (!countyCodeMap.containsKey(item.getCountyCode())) {
                        errorMsg.append("数据字典中没有此区县code\n");
                    } else if (!item.getCityCode().substring(0, 4).equals(item.getCountyCode().substring(0, 4))) {
                        errorMsg.append("该地市code下无此区县code\n");
                    } else if (!countyCodeMap.get(item.getCountyCode()).getName().equals(item.getCountyName())) {
                        errorMsg.append("区县code对应区县名称错误\n");
                    }

                    // 行业门类验证
                    if (StringUtils.isEmpty(item.getIndustryCategoryCode())) {
                        errorMsg.append("行业门类code不能为空\n");
                    } else if (!categoryCodeMap.containsKey(item.getIndustryCategoryCode())) {
                        errorMsg.append("数据字典中没有此行业门类code\n");
                    } else if (!categoryCodeMap.get(item.getIndustryCategoryCode()).getName().equals(item.getIndustryCategoryName())) {
                        errorMsg.append("行业门类code对应的行业门类名称不对\n");
                    }

                    // 行业大类验证
                    String fullClassCode = item.getIndustryCategoryCode() + item.getIndustryClassCode();
                    if (StringUtils.isEmpty(item.getIndustryClassCode())) {
                        errorMsg.append("行业大类code不能为空\n");
                    } else if (!classCodeMap.containsKey(fullClassCode)) {
                        errorMsg.append("数据字典中没有此行业大类code\n");
                    } else if (!classCodeMap.get(fullClassCode).getName().equals(item.getIndustryClassName())) {
                        errorMsg.append("行业大类code对应的行业大类名称不对\n");
                    }

                    // 是否符合政策验证
                    if (item.getIsPolicyCompliant() == null || (!"0".equals(item.getIsPolicyCompliant()) && !"1".equals(item.getIsPolicyCompliant()))) {
                        errorMsg.append("是否符合政策格式错误\n");
                    }

                    // 政策名称验证
                    if (StringUtils.isEmpty(item.getPolicyName())) {
                        errorMsg.append("政策名称不能为空\n");
                    } else if (!("以非盈利方式开放实验室".equals(item.getPolicyName()) || "减半收取餐饮住宿企业检验检测费".equals(item.getPolicyName()))) {
                        errorMsg.append("政策名称错误\n");
                    } else if ("以非盈利方式开放实验室".equals(item.getPolicyName())) {
                        if (!"010900104104102".equals(item.getPolicyId())) {
                            errorMsg.append("该政策名称对应的政策ID错误\n");
                        }
                        if (!"09".equals(item.getPolicyTypeId())) {
                            errorMsg.append("该政策名称对应的政策类型id错误\n");
                        }
                    } else if ("减半收取餐饮住宿企业检验检测费".equals(item.getPolicyName())) {
                        errorMsg.append("请通过接口上传数据，减半收取餐饮住宿企业检验检测费不能直接导入\n");
                    }

                    Integer productCount = null;
                    if (StringUtils.isEmpty(item.getProductCount())) {
                        errorMsg.append("数量不能为空\n");
                    } else {
                        productCount = Integer.valueOf(item.getProductCount());
                        // 数量格式验证
                        if (productCount <= 0 || productCount > 9) {
                            errorMsg.append("数量输入错误，请输入0到9位的整数\n");
                        }
                    }

                    // 政策类型验证
                    if (StringUtils.isEmpty(item.getPolicyType()) || !"降其他成本政策".equals(item.getPolicyType())) {
                        errorMsg.append("政策类型错误\n");
                    }

                    // 享受时间验证
                    if (item.getEnjoyTime() == null) {
                        errorMsg.append("享受时间格式不正确\n");
                    }

                    // 联系人手机验证
                    if (StringUtils.hasText(item.getContactPhone())) {
                        String phonePattern = "^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$";
                        if (!Pattern.matches(phonePattern, item.getContactPhone())) {
                            errorMsg.append("联系人手机格式错误\n");
                        }
                    }

                    // 减免费用验证
                    if (item.getReduceAmount() == null) {
                        errorMsg.append("本次减免费用（元）不能为空\n");
                    } else {
                        String moneyPattern = "^(([1-9]{1}\\d*)|(0{1}))(\\.(\\d){1,2})?$";
                        String moneyStr = item.getReduceAmount().toString();
                        if (!Pattern.matches(moneyPattern, moneyStr) || moneyStr.length() > 10) {
                            errorMsg.append("本次减免费用（元）格式错误，最多保留两位小数,长度小于10位\n");
                        }
                    }

                    // 设置导入结果
                    if (errorMsg.length() > 0) {
                        item.setImportSituation(ImportStatusEnum.ERROR.getName() + errorMsg.toString());
                        errorRows++;
                        logger.debug("数据验证失败，企业名称: {}, 错误信息: {}", item.getCompanyName(), errorMsg.toString());
                        continue;
                    }

                    // 根据覆盖模式处理重复数据
                    if (cover == 0) {
                        // 不覆盖模式：检查重复数据
                        if (existingUsccSet.contains(item.getUscc())) {
                            item.setImportSituation("数据已存在，统一社会信用代码: " + item.getUscc());
                            errorRows++;
                            logger.debug("数据已存在，企业名称: {}, 统一社会信用代码: {}", item.getCompanyName(), item.getUscc());
                            continue;
                        }
                    } else {
                        // 覆盖模式：如果数据已存在，先删除旧数据
                        if (existingDataMap.containsKey(item.getUscc())) {
                            ReportReduce existingRecord = existingDataMap.get(item.getUscc());
                            reportReduceMapper.deleteByPrimaryKey(existingRecord.getId());
                            logger.debug("覆盖模式：删除已存在数据，企业名称: {}, 统一社会信用代码: {}", existingRecord.getCompanyName(), item.getUscc());
                        }
                    }

                    validRows++;

                    // 数据转换并保存
                    ReportReduce reportReduce = new ReportReduce();
                    reportReduce.setId(UUIDUtils.getUUID());
                    reportReduce.setCompanyName(item.getCompanyName());
                    reportReduce.setUscc(item.getUscc());
                    reportReduce.setCountyCode(item.getCountyCode());
                    reportReduce.setCountyName(item.getCountyName());
                    reportReduce.setCityCode(item.getCityCode());
                    reportReduce.setCityName(item.getCityName());
                    reportReduce.setProductName(item.getProductName());
                    reportReduce.setProductCount(productCount);
                    reportReduce.setIndustryClassCode(item.getIndustryClassCode());
                    reportReduce.setIndustryClassName(item.getIndustryClassName());
                    reportReduce.setIndustryCategoryCode(item.getIndustryCategoryCode());
                    reportReduce.setIndustryCategoryName(item.getIndustryCategoryName());
                    // 将String类型的isPolicyCompliant转换为Integer类型
                    reportReduce.setIsPolicyCompliant(item.getIsPolicyCompliant() != null ? Integer.parseInt(item.getIsPolicyCompliant()) : null);
                    reportReduce.setPolicyName(item.getPolicyName());
                    reportReduce.setPolicyId(item.getPolicyId());
                    reportReduce.setPolicyTypeId(item.getPolicyTypeId());
                    reportReduce.setPolicyType(item.getPolicyType());
                    reportReduce.setEnjoyTime(item.getEnjoyTime());
                    reportReduce.setReduceAmount(item.getReduceAmount());
                    reportReduce.setContactPhone(item.getContactPhone());

                    // 设置审核状态和提交状态
                    reportReduce.setIsCommit(0); // 未提交
                    reportReduce.setFlowName(FlowConstant.REPORT_FEE_REDUCE);
                    reportReduce.setFlowKey(FlowConstant.REPORT_FEE_REDUCE_KEY);
                    reportReduce.setActNodeKey(FeeReportStatusEnum.DTC.getCode());
                    reportReduce.setActNodeName(FeeReportStatusEnum.DTC.getValue());


                    // 设置基础字段
                    reportReduce.setCreateTime(new Date());
                    reportReduce.setCreateUser(userId);
                    reportReduce.setCreateUserNickname(userNickname);
                    reportReduce.setUserId(userId);
                    reportReduce.setYn(CommonConstant.FLAG_YES);

                    insertList.add(reportReduce);
                    item.setImportSituation(ImportStatusEnum.IMPORT_SUCCESS.getName());
                }

                logger.info("数据处理完成 - 总行数: {}, 有效行数: {}, 错误行数: {}, 准备插入: {}条",
                        totalRows, validRows, errorRows, insertList.size());

                // 保存数据
                logger.info("开始保存数据，共{}条记录", insertList.size());
                if (!CollectionUtils.isEmpty(insertList)) {
                    int successCount = 0;
                    for (ReportReduce reduce : insertList) {
                        try {
                            reportReduceMapper.insertSelective(reduce);
                            successCount++;
                            logger.debug("成功插入记录，ID: {}", reduce.getId());

                            // 添加审批流程记录
                            ReportAdvice advice = new ReportAdvice();
                            advice.setId(UUIDUtils.getUUID());
                            advice.setApplyId(reduce.getId());
                            advice.setFlowName(FlowConstant.REPORT_FEE_REDUCE);
                            advice.setStage(FeeReportStatusEnum.DTC.getCode());
                            advice.setOperate("保存");
                            advice.setAdvice("保存");
                            advice.setYn(CommonConstant.FLAG_YES);
                            reportAdviceMapper.insertSelective(advice);
                        } catch (Exception e) {
                            logger.error("插入记录失败，企业名称: {}, 错误: {}", reduce.getCompanyName(), e.getMessage());
                            throw e; // 重新抛出异常，触发事务回滚
                        }
                    }
                    logger.info("数据保存完成，成功插入{}条记录", successCount);
                } else {
                    logger.warn("没有有效数据需要插入");
                }

                // 生成导入结果文件
                String fileUrl = importService.faultDataAsync(list, ReportReduceExcel.class, "导入结果.xls",
                        "减负降本数据", "减负降本数据", startTime, copyMultipartFile, appLoginUser);
                importService.notification(appLoginUser, "减负降本数据-导入结果.xls", ExportRespAnnotation.SocketMessageTypeEnum.DOWN, file.getOriginalFilename() + "导入成功", fileUrl);
            } catch (Exception e) {
                logger.error("导入减负降本数据异常", e);
                importService.notification(appLoginUser, e.getMessage(), ExportRespAnnotation.SocketMessageTypeEnum.NORMAL, "减负降本数据导入【出现异常】", null);
            } finally {
                try {
                    Files.deleteIfExists(Paths.get(pathForSave));
                } catch (Exception e) {
                    logger.error("删除文件失败", e);
                }
            }
        });
    }

    /**
     * 判断行是否为空
     *
     * @param item Excel行数据
     * @return 是否为空行
     */
    private boolean isEmptyRow(ReportReduceExcel item) {
        return StringUtils.isEmpty(item.getCompanyName()) &&
                StringUtils.isEmpty(item.getUscc()) &&
                StringUtils.isEmpty(item.getCountyCode()) &&
                StringUtils.isEmpty(item.getCountyName()) &&
                StringUtils.isEmpty(item.getCityCode()) &&
                StringUtils.isEmpty(item.getCityName()) &&
                StringUtils.isEmpty(item.getProductName()) &&
                item.getProductCount() == null &&
                StringUtils.isEmpty(item.getIndustryClassCode()) &&
                StringUtils.isEmpty(item.getIndustryClassName()) &&
                StringUtils.isEmpty(item.getIndustryCategoryCode()) &&
                StringUtils.isEmpty(item.getIndustryCategoryName()) &&
                StringUtils.isEmpty(item.getIsPolicyCompliant()) &&
                StringUtils.isEmpty(item.getPolicyName()) &&
                StringUtils.isEmpty(item.getPolicyId()) &&
                StringUtils.isEmpty(item.getPolicyTypeId()) &&
                StringUtils.isEmpty(item.getPolicyType()) &&
                item.getEnjoyTime() == null &&
                item.getReduceAmount() == null &&
                StringUtils.isEmpty(item.getContactPhone());
    }

    /**
     * 提交减负降本数据
     * 提交当前用户所有未提交状态的数据
     */
    @Transactional(readOnly = false)
    @Override
    public void submitReportReduce() {
        // 获取当前用户ID
        String userId = AppUserUtil.getCurrentUserId();
        if (StringUtils.isEmpty(userId)) {
            throw new ServiceException("参数异常");
        }

        // 查询当前用户所有未提交的数据
        List<ReportReduce> unsubmittedList = reportReduceMapper.findByUserIdAndCommitStatus(userId, CommonConstant.FLAG_NO);

        if (CollectionUtils.isEmpty(unsubmittedList)) {
            throw new ServiceException("没有可提交的数据");
        }

        int count = 0;
        for (ReportReduce reportReduce : unsubmittedList) {
            // 设置提交状态
            reportReduce.setIsCommit(CommonConstant.FLAG_YES); // 已提交
            // 待审核状态
            reportReduce.setActNodeKey(FeeReportStatusEnum.DCL.getCode());
            reportReduce.setActNodeName(FeeReportStatusEnum.DCL.getValue());

            reportReduceMapper.updateByPrimaryKeySelective(reportReduce);

            // 添加审批流程记录
            ReportAdvice advice = new ReportAdvice();
            advice.setId(UUIDUtils.getUUID());
            advice.setApplyId(reportReduce.getId());
            advice.setFlowName(FlowConstant.REPORT_FEE_REDUCE);
            advice.setStage(FeeReportStatusEnum.DCL.getCode());
            advice.setOperate("提交");
            advice.setAdvice("提交");
            advice.setYn(CommonConstant.FLAG_YES);
            reportAdviceMapper.insertSelective(advice);

            count++;
        }

        logger.info("用户[{}]成功提交了{}条减负降本数据", userId, count);
    }

    /**
     * 提交指定的减负降本数据
     *
     * @param id 减负降本数据ID
     * <AUTHOR>
     */
    @Transactional(readOnly = false)
    @Override
    public void submitReportReduceById(String id) {
        // 参数校验
        if (StringUtils.isEmpty(id)) {
            throw new ServiceException("数据ID不能为空");
        }

        // 获取当前用户ID
        String userId = AppUserUtil.getCurrentUserId();
        if (StringUtils.isEmpty(userId)) {
            throw new ServiceException("参数异常");
        }

        // 查询指定的数据
        ReportReduce reportReduce = reportReduceMapper.selectByPrimaryKey(id);
        if (reportReduce == null) {
            throw new ServiceException("数据不存在");
        }

        // 检查数据是否属于当前用户
        if (!userId.equals(reportReduce.getUserId())) {
            throw new ServiceException("无权限操作此数据");
        }

        // 检查数据是否已提交
        if (CommonConstant.FLAG_YES.equals(reportReduce.getIsCommit())) {
            throw new ServiceException("数据已提交，无需重复提交");
        }

        // 设置提交状态
        reportReduce.setIsCommit(CommonConstant.FLAG_YES); // 已提交
        // 待审核状态
        reportReduce.setActNodeKey(FeeReportStatusEnum.DCL.getCode());
        reportReduce.setActNodeName(FeeReportStatusEnum.DCL.getValue());

        reportReduceMapper.updateByPrimaryKeySelective(reportReduce);

        // 添加审批流程记录
        ReportAdvice advice = new ReportAdvice();
        advice.setId(UUIDUtils.getUUID());
        advice.setApplyId(reportReduce.getId());
        advice.setFlowName(FlowConstant.REPORT_FEE_REDUCE);
        advice.setStage(FeeReportStatusEnum.DCL.getCode());
        advice.setOperate("提交");
        advice.setAdvice("提交");
        advice.setYn(CommonConstant.FLAG_YES);
        reportAdviceMapper.insertSelective(advice);

        logger.info("用户[{}]成功提交了减负降本数据[{}]", userId, id);
    }

    /**
     * 审核减负降本数据
     *
     * @param vo 包含审核状态和审核意见的VO对象
     * <AUTHOR>
     */
    @Transactional(readOnly = false)
    @Override
    public void auditReportReduce(ReportReduceVo vo) {
        // 参数校验
        if (StringUtils.isEmpty(vo.getId())) {
            throw new ServiceException("数据ID不能为空");
        }
        if (vo.getAuditStatus() == null) {
            throw new ServiceException("审核状态不能为空");
        }
        if (vo.getAuditStatus() != 0 && vo.getAuditStatus() != 1) {
            throw new ServiceException("审核状态只能为0(驳回)或1(通过)");
        }

        // 获取当前用户信息
        String userId = AppUserUtil.getCurrentUserId();
        if (StringUtils.isEmpty(userId)) {
            throw new ServiceException("用户信息异常");
        }

        // 查询要审核的数据
        ReportReduce reportReduce = reportReduceMapper.selectByPrimaryKey(vo.getId());
        if (reportReduce == null) {
            throw new ServiceException("数据不存在");
        }

        // 检查数据状态，只有待审核状态的数据才能审核
        if (!FeeReportStatusEnum.DCL.getCode().equals(reportReduce.getActNodeKey())) {
            throw new ServiceException("当前数据状态不允许审核");
        }

        String nextNodeKey = vo.getAuditStatus() == 1 ? FeeReportStatusEnum.YCL.getCode() : FeeReportStatusEnum.DTC.getCode();
        String nextNodeName = vo.getAuditStatus() == 1 ? FeeReportStatusEnum.YCL.getValue() : FeeReportStatusEnum.DTC.getValue();
        String operateDesc = vo.getAuditStatus() == 1 ? "通过" : "驳回";

        reportReduce.setActNodeKey(nextNodeKey);
        reportReduce.setActNodeName(nextNodeName);

        if (vo.getAuditStatus() == 0) {
            // 驳回时重置提交状态为未提交
            reportReduce.setIsCommit(CommonConstant.FLAG_NO);
        }

        // 更新数据
        reportReduceMapper.updateByPrimaryKeySelective(reportReduce);

        // 添加审批流程记录
        ReportAdvice advice = new ReportAdvice();
        advice.setId(UUIDUtils.getUUID());
        advice.setApplyId(reportReduce.getId());
        advice.setFlowName(FlowConstant.REPORT_FEE_REDUCE);
        advice.setStage(nextNodeKey);
        advice.setOperate(operateDesc);
        advice.setAdvice(vo.getAdvice());
        advice.setYn(CommonConstant.FLAG_YES);
        reportAdviceMapper.insertSelective(advice);

    }

}
