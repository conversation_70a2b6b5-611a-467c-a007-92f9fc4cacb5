package com.jh.finance.service.fee;

import java.util.List;

import com.github.pagehelper.PageInfo;

import com.jh.finance.bean.fee.FeeReductionHalve;
import com.jh.finance.bean.fee.vo.FeeReductionHalveVo;
import com.jh.finance.bean.fee.vo.FeeReductionHalveDataPushRequest;

/**
 * 减半收取Service接口
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
public interface FeeReductionHalveService {
    /**
     * 保存或更新减半收取
     *
     * @param feeReductionHalve 减半收取对象
     * @return String 减半收取ID
     * <AUTHOR>
     */
    String saveOrUpdateFeeReductionHalve(FeeReductionHalve feeReductionHalve);

    /**
     * 删除减半收取
     *
     * @param ids void 减半收取ID
     * <AUTHOR>
     */
    void deleteFeeReductionHalve(List<String> ids);

    /**
     * 查询减半收取详情
     *
     * @param id
     * @return FeeReductionHalve
     * <AUTHOR>
     */
    FeeReductionHalve findById(String id);

    /**
     * 分页查询减半收取
     *
     * @param feeReductionHalveVo
     * @return PageInfo<FeeReductionHalve>
     * <AUTHOR>
     */
    PageInfo<FeeReductionHalve> findPageByQuery(FeeReductionHalveVo feeReductionHalveVo);

    /**
     * 按条件导出查询减半收取
     *
     * @param feeReductionHalveVo
     * @return PageInfo<FeeReductionHalve>
     * <AUTHOR>
     */
    List<FeeReductionHalve> findByQuery(FeeReductionHalveVo feeReductionHalveVo);

    /**
     * 处理外部推送的减半收取数据
     *
     * @param request 推送数据对象
     * @return String 处理结果消息
     * <AUTHOR>
     */
    String processPushData(FeeReductionHalveDataPushRequest request);


}
