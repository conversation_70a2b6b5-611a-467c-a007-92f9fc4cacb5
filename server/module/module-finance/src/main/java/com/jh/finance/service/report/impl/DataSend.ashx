<%@ WebHandler Language="C#" Class="DataSend" %>

using Finance.Framework.Helpers;
using System;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using Finance.Framework;
using Finance.Framework.Web;
using System.Web;
using Finance.Model.ZLJ;
using Finance.Model.Enum;
using Finance.Model.System;
using Finance.Service;
using System.Text;
using Finance.Model;
using Finance.Model.Report;
using System.Net;
using Finance.Model.Basic;
using System.Collections.Generic;

using System.Dynamic;
using System.Linq;
using System.Globalization;
using System.IO;
using System.Text.RegularExpressions;



/// <summary>
/// 获取减负降本机构数据
/// 创建人：宿智勇
/// 创建时间：2021年3月25日20:56:28
/// </summary>
public class DataSend : IHttpHandler
{
    NHibernateHelper DbUtil = new NHibernateHelper();

    public void ProcessRequest(HttpContext context)
    {
        context.Response.ContentType = "text/plain";
        dynamic Verifications = context.Request["Verification"] != null ? HttpUtility.UrlDecode(context.Request["Verification"].ToString()) : "";
        dynamic ReduceDatas = context.Request["ReduceData"] != null ? HttpUtility.UrlDecode(context.Request["ReduceData"].ToString()) : "";
        dynamic EnjoyDataSends = context.Request["EnjoyDataSend"] != null ? HttpUtility.UrlDecode(context.Request["EnjoyDataSend"].ToString()) : "";

        dynamic StandardData = context.Request["StandardData"] != null ? HttpUtility.UrlDecode(context.Request["StandardData"].ToString()) : "";

        string json = string.Empty;

        if (Verifications != "")
        {
            json = Verification(Verifications);
        }
        else if (ReduceDatas != "")
        {
            json = ReduceData(ReduceDatas);//3.	减费惠企数据推送
        }
        else if (EnjoyDataSends != "")
        {
            json = EnjoyDataSend(EnjoyDataSends);
        }
        else if (StandardData != "")
        {
            json = StandardDataSend(StandardData);
        }
        context.Response.Write(json);
        context.Response.End();
    }


    /// <summary>
    /// 2.	可享受政策单位数据推送
    /// </summary>
    /// <param name="obj"></param>
    /// <returns></returns>
    private string EnjoyDataSend(dynamic obj)
    {
        string resultMsg = "成功";
        bool resultStatus = true;
        TestOrg testOrg = new TestOrg();
        WriteLog("EnjoyDataSend入参：", obj);
        try
        {
            JObject jObject = JObject.Parse(obj.ToString());
            string Token = GetPar(jObject, "Token");
            testOrg = DbUtil.GetT<TestOrg>(r => r.Token == Token);
            if (testOrg != null)
            {
                if (GetLoginUserIdByToken(Token) != 0)
                {
                    string s = GetPar(jObject, "UniqueCode");
                    SendMessage send = DbUtil.GetT<SendMessage>(w => w.UniqueCode == GetPar(jObject, "UniqueCode") && w.OrgID == DbUtil.GetT<TestOrg>(r => r.Token == GetPar(jObject, "Token")).Id && !w.IsDel);
                    if (send == null)
                    {
                        send = new SendMessage();
                    }
                    if (string.IsNullOrEmpty(GetPar(jObject, "UniqueCode")))
                    {
                        resultStatus = false;
                        resultMsg += "唯一标识不能为空\n";
                    }

                    if (string.IsNullOrEmpty(GetPar(jObject, "Uscc")))
                    {
                        resultStatus = false;
                        resultMsg += "统一社会信用代码不能为空\n";
                    }
                    else if (!string.IsNullOrEmpty(GetPar(jObject, "Uscc")))
                    {
                        Regex regUscc = new Regex(@"^([0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}|[1-9]\d{14})$");
                        //字母
                        if (!regUscc.IsMatch(GetPar(jObject, "Uscc")))
                        {
                            resultMsg += "统一社会信用代码格式错误";
                            resultStatus = false;
                        }
                        else
                        {
                            send.Uscc = GetPar(jObject, "Uscc");
                        }
                    }

                    if (string.IsNullOrEmpty(GetPar(jObject, "Company")))
                    {
                        resultStatus = false;
                        resultMsg += "企业名称不能为空\n";
                    }
                    else
                    {
                        send.Company = GetPar(jObject, "Company");
                    }

                    if (string.IsNullOrEmpty(GetPar(jObject, "LinkMan")))
                    {
                        resultStatus = false;
                        resultMsg += "联系人不能为空\n";
                    }
                    else
                    {
                        send.LinkMan = GetPar(jObject, "LinkMan");
                    }

                    Regex regTel = new Regex(@"^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$");
                    if (string.IsNullOrEmpty(GetPar(jObject, "Tel")))
                    {
                        resultStatus = false;
                        resultMsg += "联系人手机不能为空\n";
                    }
                    else if (!string.IsNullOrEmpty(GetPar(jObject, "Tel")) && !regTel.IsMatch(GetPar(jObject, "Tel")))
                    {
                        resultStatus = false;
                        resultMsg += "联系人手机格式错误\n";
                    }
                    else
                    {
                        send.Tel = GetPar(jObject, "Tel");
                    }
                    if (string.IsNullOrEmpty(GetPar(jObject, "Productname")) && string.IsNullOrEmpty(GetPar(jObject, "ProductName")))
                    {
                        resultStatus = false;
                        resultMsg += "设备名称不能为空\n";
                    }
                    else
                    {
                        send.ProductName = GetPar(jObject, "Productname");
                    }
                    Regex regNum = new Regex(@"^[+]{0,1}(\d+)$");
                    if (!string.IsNullOrEmpty(GetPar(jObject, "ProductCount")) && (!regNum.IsMatch(GetPar(jObject, "ProductCount")) || GetPar(jObject, "ProductCount").Length > 9))
                    {
                        resultStatus = false;
                        resultMsg += "设备数量数据格式错误，请输入整数";
                    }
                    else
                    {
                        send.ProcuctCount = GetPar(jObject, "ProductCount").ToIntEx();
                    }
                    if (GetPar(jObject, "PolicyID") == "010900104104102")
                    {
                        send.PolicyID = GetPar(jObject, "PolicyID");
                    }
                    else if (GetPar(jObject, "PolicyID") == "010700104104102")
                    {
                        send.PolicyID = GetPar(jObject, "PolicyID");
                    }
                    else
                    {
                        resultStatus = false;
                        resultMsg += "库中无此政策ID";
                    }
                    send.UniqueCode = GetPar(jObject, "UniqueCode");
                    send.Token = GetPar(jObject, "Token");
                    send.InDate = DateTime.Now;
                    send.OrgID = testOrg.Id;
                    send.IsSended = 0;
                    send.IsDel = false;
                    send.Random = send.Id.ToString() + Random4().ToString();
                    if (resultStatus == true)
                    {
                        SaveLog<SendMessage>(send, LogType.Audit, "可享受政策单位数据推送", "推送单位id:" + testOrg.Id);
                    }
                }
                else
                {
                    resultMsg = "Token过期";
                    resultStatus = false;
                }
            }
            else
            {
                resultMsg = "用户信息验证不通过";
                resultStatus = false;
            }

        }
        catch (Exception ex)
        {
            WriteLog("EnjoyDataSend错误信息：", ex);
            resultMsg = ex.ToString();
            resultStatus = false;
        }
        finally
        {
            int OrgID = 0;
            if (testOrg != null)
            {
                OrgID = testOrg.Id;
            }
            RequestLog(resultStatus, "Verification", resultMsg, "可享受政策单位数据推送", obj.ToString(), OrgID);
        }
        JObject jOb = new JObject();
        jOb.Add("Success", resultStatus);
        if (resultStatus == false)
        {

            resultMsg = resultMsg.Replace("成功", "失败，");
        }
        jOb.Add("Msg", resultMsg);
        return JsonConvert.SerializeObject(jOb);

    }

    /// <summary>
    /// 1.	身份验证
    /// </summary>
    /// <param name="obj"></param>
    /// <returns></returns>
    private string Verification(dynamic obj)
    {
        string resultMsg = "成功";
        bool resultStatus = true;
        string Token = string.Empty;
        TestOrg testOrg = new TestOrg();
        WriteLog("Verification入参：", obj);

        try
        {
            JObject jObject = JObject.Parse(obj.ToString()); ;
            string AppId = GetPar(jObject, "AppId");
            string AppKey = GetPar(jObject, "AppKey");
            testOrg = DbUtil.GetT<TestOrg>(r => r.AppId == AppId && r.AppKey == AppKey);
            if (testOrg != null)
            {
                Token = CreateLoginUserToken(AppId, AppKey);
                testOrg.Token = Token;
                SaveLog<TestOrg>(testOrg, LogType.Audit, "机构信息验证", "Token为：" + Token);
            }
            else
            {
                resultMsg = "用户信息验证不通过";
                resultStatus = false;
            }
        }
        catch (Exception ex)
        {
            WriteLog("Verification错误信息：", ex);

            resultMsg = ex.ToString();
            resultStatus = false;
        }
        finally
        {
            int OrgID = 0;
            if (testOrg != null)
            {
                OrgID = testOrg.Id;
            }
            RequestLog(resultStatus, "Verification", resultMsg, "机构信息验证", obj.ToString(), OrgID);
        }
        JObject jOb = new JObject();
        jOb.Add("Success", resultStatus);
        if (resultStatus == false)
        {
            resultMsg = resultMsg.Replace("成功", "失败，");
        }
        jOb.Add("Msg", resultMsg);
        jOb.Add("Token", Token);
        return JsonConvert.SerializeObject(jOb);
    }

    /// <summary>
    /// 3.	减费惠企数据推送
    /// 修改：1.数据修改时，判断当前是否已审核，如已审核无法修改，未审核数据可修改  2.请求，出错加日志
    /// 修改：季李刚 2021-09-29
    /// </summary>
    /// <param name="obj"></param>
    /// <returns></returns>
    private string ReduceData(dynamic obj)
    {
        string resultMsg = "成功";
        bool resultStatus = true;
        string errMsg = string.Empty;
        TestOrg testOrg = new TestOrg();
        WriteLog("ReduceData入参：", obj);

        try
        {
            JObject jObject = JObject.Parse(obj.ToString());
            string Token = GetPar(jObject, "Token");
            testOrg = DbUtil.GetT<TestOrg>(r => r.Token == Token);
            if (testOrg != null)
            {
                if (GetLoginUserIdByToken(Token) != 0)
                {
                    FlowReport flowReport = new FlowReport();
                    InspectData inspect = new InspectData();
                    InspectData inspectData = DbUtil.GetT<InspectData>(w => w.UniqueCode == GetPar(jObject, "UniqueCode"));
                    if (inspectData != null && inspectData.FlowReport != null)
                    {
                        inspect = inspectData;
                        flowReport = inspect.FlowReport;
                        if (flowReport.Audit == 1 && flowReport.IsPass == true)
                        {
                            resultStatus = false;
                            resultMsg += "该数据已经审核，无法更改";
                            flowReport.IsDel = false;
                        }
                    }
                    inspect.FlowReport = flowReport;
                    flowReport.InspectData = inspect;
                    flowReport.FlowType = 4;
                    flowReport.IsPass = true;
                    flowReport.Audit = 0;
                    flowReport.InputTime = DateTime.Now;
                    flowReport.Year = DateTime.Now.Year;

                    inspect.IsImport = 3;
                    if (string.IsNullOrEmpty(GetPar(jObject, "UniqueCode")))
                    {
                        resultStatus = false;
                        resultMsg += "唯一标识不能为空\n";
                    }

                    if (string.IsNullOrEmpty(GetPar(jObject, "Company")))
                    {
                        resultStatus = false;
                        resultMsg += "企业名称不能为空\n";
                    }
                    else
                    {
                        inspect.Company = GetPar(jObject, "Company");
                    }
                    if (string.IsNullOrEmpty(GetPar(jObject, "Uscc")))
                    {
                        resultStatus = false;
                        resultMsg += "统一社会信用代码不能为空\n";
                    }
                    else if (!string.IsNullOrEmpty(GetPar(jObject, "Uscc")))
                    {
                        Regex regUscc = new Regex(@"^([0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}|[1-9]\d{14})$");
                        //字母
                        if (!regUscc.IsMatch(GetPar(jObject, "Uscc")))
                        {
                            resultMsg += "统一社会信用代码格式错误";
                            resultStatus = false;
                        }
                        else
                        {
                            inspect.Uscc = GetPar(jObject, "Uscc");
                        }
                    }

                    if (!string.IsNullOrEmpty(GetPar(jObject, "OrgCode")))
                    {
                        inspect.OrgCode = GetPar(jObject, "OrgCode");
                    }


                    ZLJArea zLJArea = DbUtil.GetT<ZLJArea>(w => w.AreaCode == GetPar(jObject, "CountryCode"));
                    if (zLJArea != null)
                    {
                        inspect.CountryCode = GetPar(jObject, "CountryCode");
                        inspect.CountryName = zLJArea.AreaName;
                        inspect.CityCode = GetPar(jObject, "CountryCode").Substring(0, 4) + "00" +
                            "" +
                            "" +
                            "" +
                            "" +
                            "" +
                            "" +
                            "" +
                            "000000";
                        ZLJArea zLJ = DbUtil.GetT<ZLJArea>(w => w.AreaCode == inspect.CityCode);
                        inspect.CityName = zLJ.AreaName;
                    }
                    else
                    {
                        resultMsg += "库中无此区县code";
                        resultStatus = false;
                    }
                    if (string.IsNullOrEmpty(GetPar(jObject, "ProductName")) && string.IsNullOrEmpty(GetPar(jObject, "Productname")))
                    {
                        resultStatus = false;
                        resultMsg += "产品或设备名称不能为空\n";
                    }
                    else if (!string.IsNullOrEmpty(GetPar(jObject, "ProductName")))
                    {
                        inspect.ProductName = GetPar(jObject, "ProductName");
                    }
                    else
                    {
                        inspect.ProductName = GetPar(jObject, "Productname");
                    }
                    Regex regNum = new Regex(@"^[+]{0,1}(\d+)$");
                    if (!string.IsNullOrEmpty(GetPar(jObject, "ProductCount")) && (!regNum.IsMatch(GetPar(jObject, "ProductCount")) || GetPar(jObject, "ProductCount").Length > 9))
                    {
                        resultStatus = false;
                        resultMsg += "数据格式错误，请输入0到9位的整数";
                    }
                    else
                    {
                        string d = GetPar(jObject, "ProductCount");
                        inspect.Num = GetPar(jObject, "ProductCount").ToIntEx();
                    }

                    inspect.IsFlowPolicy = true;
                    DateTime date;
                    if (!string.IsNullOrEmpty(GetPar(jObject, "EnjoyTime")) && !DateTime.TryParse(GetPar(jObject, "EnjoyTime"), out date))
                    {
                        resultStatus = false;
                        resultMsg += "享受时间格式不正确\n";
                    }
                    else
                    {
                        inspect.EnjoyTime = GetPar(jObject, "EnjoyTime").ToDateTime();
                        inspect.Year = GetPar(jObject, "EnjoyTime").ToDateTime().Year;
                        inspect.Mon = GetPar(jObject, "EnjoyTime").ToDateTime().Month;
                        inspect.YearMonth = Convert.ToDateTime(GetPar(jObject, "EnjoyTime")).ToString("yyyy-MM");
                    }
                    Regex regTel = new Regex(@"^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$");
                    if (!string.IsNullOrEmpty(GetPar(jObject, "Tel")) && !regTel.IsMatch(GetPar(jObject, "Tel")))
                    {
                        resultStatus = false;
                        resultMsg += "联系人手机格式错误\n";
                    }
                    else
                    {
                        inspect.Tel = GetPar(jObject, "Tel");
                    }

                    EconomicsType IndustryCategoryCode = DbUtil.GetT<EconomicsType>(w => w.Code == GetPar(jObject, "IndustryCategoryCode") && w.Classlevel == 1);
                    if (IndustryCategoryCode != null)
                    {
                        inspect.IndustryCategoryCode = GetPar(jObject, "IndustryCategoryCode");
                        inspect.IndustryCategory = IndustryCategoryCode.Name;
                    }
                    else
                    {
                        resultStatus = false;
                        resultMsg += "库中无此行业门类";
                    }
                    EconomicsType IndustryCode = DbUtil.GetT<EconomicsType>(w => w.Code == GetPar(jObject, "IndustryCategoryCode") + GetPar(jObject, "IndustryCode") && w.Classlevel == 2);
                    if (IndustryCode != null)
                    {
                        inspect.IndustryCode = GetPar(jObject, "IndustryCode");
                        inspect.IndustryType = IndustryCode.Name;
                    }
                    else
                    {
                        resultStatus = false;
                        resultMsg += "库中无此行业大类";
                    }
                    if (GetPar(jObject, "PolicyID") == "010900104104102")
                    {
                        inspect.PolicyID = GetPar(jObject, "PolicyID");
                        inspect.PolicyTypeID = "09";
                        inspect.PolicyName = "以非盈利方式开放实验室";
                        inspect.PolicyType = "降其他成本政策";
                    }
                    else if (GetPar(jObject, "PolicyID") == "010700104104102")
                    {
                        inspect.PolicyID = GetPar(jObject, "PolicyID");
                        inspect.PolicyTypeID = "07";
                        inspect.PolicyName = "减半收取餐饮住宿企业检验检测费";
                        inspect.PolicyType = "降其他成本政策";
                    }
                    else
                    {
                        resultStatus = false;
                        resultMsg += "库中无此政策ID";
                    }
                    Regex regMoney = new Regex(@"^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$");
                    if (!string.IsNullOrEmpty(GetPar(jObject, "Money")) && (!regMoney.IsMatch(GetPar(jObject, "Money")) || GetPar(jObject, "Money").Length > 10))
                    {
                        resultStatus = false;
                        resultMsg += "本次减免费用（元）格式错误，最多保留两位小数,长度小于10位\n";
                    }
                    else
                    {
                        inspect.Money = GetPar(jObject, "Money").ToDecimal();
                    }
                    inspect.UniqueCode = GetPar(jObject, "UniqueCode");
                    inspect.Status = 1;
                    inspect.OrgID = flowReport.OrgId = testOrg.Id;
                    inspect.IsDel = flowReport.IsDel = false;
                    inspect.InputTime = DateTime.Now;
                    inspect.Random = inspect.Id.ToString() + Random4().ToString();
                    if (resultStatus == true)
                    {
                        int OrgID = 0;
                        if (testOrg != null)
                        {
                            OrgID = testOrg.Id;
                        }


                        bool isSaveSuccess = SaveLog<FlowReport>(flowReport, LogType.Audit, "减费惠企数据推送", "推送机构id:" + OrgID);
                        if (isSaveSuccess)
                        {
                            //1.发送评价短信
                            //2.添加InspectData_Error 表，供一键督察，列表页查询用
                            AddInspectDataError(inspect);
                        }

                    }
                }
                else
                {
                    resultMsg = "Token过期";
                    resultStatus = false;
                }
            }
            else
            {
                resultMsg = "用户信息验证不通过";
                resultStatus = false;
            }
        }
        catch (Exception ex)
        {
            WriteLog("ReduceData错误信息：", ex);

            errMsg = ex.ToString();
            resultMsg = "失败";
            resultStatus = false;
        }
        finally
        {
            int OrgID = 0;
            if (testOrg != null)
            {
                OrgID = testOrg.Id;
            }
            RequestLog(resultStatus, "Verification", resultMsg, "减费惠企数据推送", obj.ToString(), OrgID);
        }
        JObject jOb = new JObject();
        jOb.Add("Success", resultStatus);
        if (resultStatus == false)
        {
            resultMsg = resultMsg.Replace("成功", "失败，");
        }
        jOb.Add("Msg", resultMsg);
        return JsonConvert.SerializeObject(jOb);
    }


    /// <summary>
    /// 通过减负数据上报接口，接收到数据后，发送一个评价短信
    /// </summary>
    /// <param name="inspect"></param>
    private bool SendSMS(InspectData inspect)
    {
        bool result = false;
        if (inspect == null) return result;
        if (inspect.Id == 0) return result;
        if (inspect.EnjoyTime == null || inspect.EnjoyTime == DateTime.MinValue) return result;
        if (string.IsNullOrEmpty(inspect.Random)) return result;
        if (string.IsNullOrEmpty(inspect.Tel)) return result;


        string smsContent = string.Format("您于{0}享受\"减半收取餐饮住宿企业检验检测费用\"惠企政策，请通过以下链接对本次服务进行评价 http://qfjf.zjamr.zj.gov.cn/SJ/PJ.aspx?i={1}&r={2}"
            , inspect.EnjoyTime.GetValueOrDefault().ToString("yyyy-MM-dd HH:mm:ss"), inspect.Id, inspect.Random);

        try
        {
            // 用 inspect.Tel 调用短信接口
            WebReference.MsgSendService webClient = new WebReference.MsgSendService();
            WebReference.MsgSendParameter msg = new WebReference.MsgSendParameter();
            msg.MsgContent = smsContent;
            msg.PhoneNumber = new string[] { inspect.Tel };
            msg.ReceiveUser = inspect.Company;
            msg.SysName = "浙江省市场监管收费信息平台";
            msg.AreaCode = inspect.CityCode;
            webClient.SendSmsCloud(msg);
            result = true;//发送成功
        }
        catch (Exception)
        {
            //throw;
        }


        return result;

    }

    /// <summary>
    /// 1.发送评价短信
    /// 2.添加InspectData_Error 表，供一键督察，列表页查询用
    /// 创建：季李刚 2022-01-06
    /// </summary>
    /// <param name="inspect"></param>
    private void AddInspectDataError(InspectData inspect)
    {
        bool isSendSMS = SendSMS(inspect);//发送评价短信


        InspectData inspectData = null;
        //inspectData = DbUtil.GetT<InspectData>(p=>p.Id==inspect.Id);
        //由于inspect.Id 没有所以要用以下方法认为查出的数据就新插入的数据
        inspectData = DbUtil.GetT<InspectData>(p => p.IsDel == false && p.Uscc == inspect.Uscc && p.InputTime == inspect.InputTime && p.EnjoyTime == inspect.EnjoyTime
                                                   && p.PolicyID == inspect.PolicyID);
        if (inspectData != null)//新插入的 InspectData
        {
            //发送评价短信
            if (isSendSMS)
            {
                inspectData.IsSended = 1;
                inspectData.SendDate = System.DateTime.Now;
                SaveLog<InspectData>(inspectData, LogType.Audit, "减费惠企数据推送，发送短信", "InspectDataID:" + inspectData.Id);
            }


            //string a = inspectData.Uscc;
            string errorStr = string.Empty;
            //if (inspectData.Uscc.Substring(0, 1) != "9")
            //{
            //    errorStr += "信用代码不是9开头，非企业数据！";
            //}
            decimal? monny = inspectData.Money;
            int? num = inspectData.Num;

            if (monny == 0)
            {
                errorStr += "减费数量为0！";
            }
            if (num == 0)
            {
                errorStr += "设备数量为0！";
            }

            if (monny != null && monny != 0 && num != null && num != 0 && (monny / num) < 175 && inspectData.ProductName.Contains("电梯"))
            {
                errorStr += "单台设备金额小余175！";
            }
            if (inspectData.IndustryCategory != "住宿和餐饮业")
            {
                errorStr += "行业门类非住宿和餐饮业！";
            }
            if (inspectData.EnjoyTime > inspectData.InputTime)
            {
                errorStr += "享受时间大于上报时间！";
            }
            if (inspectData.EnjoyTime < (inspectData.InputTime.GetValueOrDefault().AddDays(-30)))
            {
                errorStr += "享受政策时间<上报时间-30天！";
            }

            InspectDataError errorData = DbUtil.GetT<InspectDataError>(p => p.InspectDataID == inspectData.Id);
            if (!string.IsNullOrEmpty(errorStr))//有错误信息
            {
                InspectDataError errorInfo = new InspectDataError();
                errorInfo.InspectDataID = inspectData.Id;
                errorInfo.FlowID = inspectData.FlowReport.Id;
                errorInfo.ErrorInfo = errorStr;

                if (errorData != null)//数据库中已有数据，
                {
                    errorData.ErrorInfo = errorStr;
                    errorInfo = errorData;
                }
                SaveLog<InspectDataError>(errorInfo, LogType.Audit, "减费惠企数据推送，一键督察)", "InspectDataID:" + inspectData.Id);
            }
            else
            {
                if (errorData != null)//数据库中已有数据,没有错误，做删除操作
                {
                    SaveLog<InspectDataError>(errorData, LogType.Physical, "减费惠企数据推送，一键督察", "InspectDataID:" + inspectData.Id);
                }

            }
        }

    }





    private string GetPar(JObject json, string par)
    {
        return json[par] != null ? json[par].ToString().Trim() : "";
    }

    /// <summary>
    /// 接口日志
    /// </summary>
    /// <param name="isSuccess"></param>
    /// <param name="webService"></param>
    /// <param name="dataDesc"></param>
    /// <param name="Name"></param>
    /// <param name="operDesc"></param>
    protected void RequestLog(bool isSuccess, string webService, string dataDesc, string Name, string operDesc, int OrgID)
    {
        WebServiceLog webServiceLog = new WebServiceLog();
        webServiceLog.IsSuccess = isSuccess;
        webServiceLog.DataDesc = dataDesc;
        webServiceLog.Name = Name;
        webServiceLog.IP = HttpContext.Current.Request.UserHostAddress;
        webServiceLog.DataDesc = operDesc;
        webServiceLog.UserId = OrgID;
        webServiceLog.InputTime = DateTime.Now;
        SaveLog<WebServiceLog>(webServiceLog, LogType.Add, "", "");
    }

    /// <summary>
    /// 创建用户Token
    /// </summary>
    /// <param name="usercode"></param>
    /// <returns></returns>
    public static string CreateLoginUserToken(string userid, string userpwd)
    {

        string ranpwd = RandomPassword();
        string userip = IpUtils.GetCurrentIp().PadLeft(16, ' ');
        userid = userid.PadLeft(16, ' ');
        userpwd = userpwd.PadLeft(40, ' ');
        string datetime = DateTime.Now.ToString("yyyyMMddHHmmssfff");
        string tokenstr = "FANGDAZJ" + userip + userid + userpwd + datetime + ranpwd;

        //string token = HttpUtility.UrlEncode(DesUtils.ToDesEncrypt(tokenstr));
        string token = HttpUtility.UrlEncode(Base62Helper.base62encode(tokenstr));

        //在这里存下ranpwd
        return token;
    }

    /// <summary>
    /// 根据token获得userid(登录认证通过后才可以用)
    /// </summary>
    /// <param name="token"></param>
    /// <returns></returns>
    public static int GetLoginUserIdByToken(string token)
    {
        try
        {
            string tokenstr = Base62Helper.base62decode(token);
            string tokenherd = tokenstr.Substring(0, 8);
            if (tokenherd != "FANGDAZJ")
                return 0;
            string s = tokenstr.Substring(82, 17);
            DateTime tokentime = DateTime.ParseExact(tokenstr.Substring(82, 17), "yyyyMMddHHmmssfff", CultureInfo.CurrentCulture);

            if ((DateTime.Now - tokentime).TotalSeconds > 86400)
                return 0;

            string userid = tokenstr.Substring(24, 16).Trim();
            if (!string.IsNullOrEmpty(userid))
                return 1;
            else
                return 0;
        }
        catch (Exception)
        {
            return 0;
        }
    }
    #region Base62Helper
    /// <summary>
    /// 加密方法
    /// </summary>
    public class Base62Helper
    {
        private static string base62EncodeChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";//编码后的字符集 

        private static int[] base62DecodeChars = new int[] { -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, -1, -1, -1, -1, -1, -1, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, -1, -1, -1, -1, -1 };//对应ASICC字符的位置 

        public static string base62encode(string str)
        { //加密 
            string Out = "";
            int i = 0, len = str.Length;
            char c1, c2, c3;
            while (i < len)
            {
                c1 = Convert.ToChar(str[i++] & 0xff);
                if (i == len)
                {
                    Out += base62EncodeChars[c1 >> 2];
                    Out += base62EncodeChars[(c1 & 0x3) << 4];
                    //Out += "==";
                    Out += "XX";
                    break;
                }
                c2 = str[i++];
                if (i == len)
                {
                    Out += base62EncodeChars[c1 >> 2];
                    Out += base62EncodeChars[((c1 & 0x3) << 4) | ((c2 & 0xF0) >> 4)];
                    Out += base62EncodeChars[(c2 & 0xF) << 2];
                    //Out += "=";
                    Out += "X";
                    break;
                }
                c3 = str[i++];
                Out += base62EncodeChars[c1 >> 2];
                Out += base62EncodeChars[((c1 & 0x3) << 4) | ((c2 & 0xF0) >> 4)];
                Out += base62EncodeChars[((c2 & 0xF) << 2) | ((c3 & 0xC0) >> 6)];
                Out += base62EncodeChars[c3 & 0x3F];
            }
            return Out;
        }
        public string utf16to8(string str)
        {
            string Out = "";
            int i, len;
            char c;//char为16位Unicode字符,范围0~0xffff,感谢vczh提醒 
            len = str.Length;
            for (i = 0; i < len; i++)
            {//根据字符的不同范围分别转化 
                c = str[i];
                if ((c >= 0x0001) && (c <= 0x007F))
                {
                    Out += str[i];
                }
                else if (c > 0x07FF)
                {
                    Out += (char)(0xE0 | ((c >> 12) & 0x0F));
                    Out += (char)(0x80 | ((c >> 6) & 0x3F));
                    Out += (char)(0x80 | ((c >> 0) & 0x3F));
                }
                else
                {
                    Out += (char)(0xC0 | ((c >> 6) & 0x1F));
                    Out += (char)(0x80 | ((c >> 0) & 0x3F));
                }
            }
            return Out;
        }

        public static string base62decode(string str)
        {//解密 
            int c1, c2, c3, c4;
            int i, len;
            string Out;
            len = str.Length;
            i = 0; Out = "";
            while (i < len)
            {
                do
                {
                    c1 = base62DecodeChars[str[i++] & 0xff];
                } while (i < len && c1 == -1);
                if (c1 == -1) break;
                do
                {
                    c2 = base62DecodeChars[str[i++] & 0xff];
                } while (i < len && c2 == -1);
                if (c2 == -1) break;
                Out += (char)((c1 << 2) | ((c2 & 0x30) >> 4));
                do
                {
                    c3 = str[i++] & 0xff;
                    if (c3 == 61) return Out;
                    c3 = base62DecodeChars[c3];
                } while (i < len && c3 == -1);
                if (c3 == -1) break;
                Out += (char)(((c2 & 0XF) << 4) | ((c3 & 0x3C) >> 2));
                do
                {
                    c4 = str[i++] & 0xff;
                    if (c4 == 61) return Out;
                    c4 = base62DecodeChars[c4];
                } while (i < len && c4 == -1);
                if (c4 == -1) break;
                Out += (char)(((c3 & 0x03) << 6) | c4);
            }
            return Out;
        }

        public string utf8to16(string str)
        {
            string Out = "";
            int i, len;
            char c, char2, char3;
            len = str.Length;
            i = 0; while (i < len)
            {
                c = str[i++];
                switch (c >> 4)
                {
                    case 0:
                    case 1:
                    case 2:
                    case 3:
                    case 4:
                    case 5:
                    case 6:
                    case 7: Out += str[i - 1]; break;
                    case 12:
                    case 13:
                        char2 = str[i++];
                        Out += (char)(((c & 0x1F) << 6) | (char2 & 0x3F)); break;
                    case 14:
                        char2 = str[i++];
                        char3 = str[i++];
                        Out += (char)(((c & 0x0F) << 12) | ((char2 & 0x3F) << 6) | ((char3 & 0x3F) << 0)); break;
                }
            }
            return Out;
        }

    }
    #endregion

    public sealed class IpUtils
    {
        /// <summary>
        /// Ip地址获取
        /// </summary>
        /// <returns></returns>
        public static string GetLocalIpV4()
        {
            return Dns.GetHostEntry(Dns.GetHostName()).AddressList.FirstOrDefault<IPAddress>(r => r.AddressFamily.ToString().Equals("InterNetwork")).ToString();
        }

        public static bool IsIP(string ip)
        {
            return System.Text.RegularExpressions.Regex.IsMatch(ip, @"^((2[0-4]\d|25[0-5]|[01]?\d\d?)\.){3}(2[0-4]\d|25[0-5]|[01]?\d\d?)$");

        }

        public static string GetCurrentIp()
        {
            string result = String.Empty;
            result = HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"];
            if (result != null && result != String.Empty)
            {
                //可能有代理
                if (result.IndexOf(".") == -1) //没有"."肯定是非IPv4格式
                    result = null;
                else
                {
                    if (result.IndexOf(",") != -1)
                    {
                        //有","，估计多个代理。取第一个不是内网的IP。
                        result = result.Replace(" ", "").Replace("\"", "");
                        string[] temparyip = result.Split(",;".ToCharArray());
                        for (int i = 0; i < temparyip.Length; i++)
                        {
                            if (IsIP(temparyip[i])
                                    && temparyip[i].Substring(0, 3) != "10."
                                    && temparyip[i].Substring(0, 7) != "192.168"
                                    && temparyip[i].Substring(0, 7) != "172.16.")
                            {
                                return temparyip[i]; //找到不是内网的地址
                            }
                        }
                    }
                    else if (IsIP(result)) //代理即是IP格式
                        return result;
                    else
                    {
                        try
                        {
                            result = result.Split(':')[0]; //代理中的内容 非IP，取IP
                            if (IsIP(result))
                                return result;
                            else
                                result = null;
                        }
                        catch
                        {
                            result = null;
                        }
                    }
                }
            }

            string IpAddress = (HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"] != null && HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"] != String.Empty) ? HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"] : HttpContext.Current.Request.ServerVariables["REMOTE_ADDR"];

            if (null == result || result == String.Empty)
                result = HttpContext.Current.Request.ServerVariables["REMOTE_ADDR"];

            if (result == null || result == String.Empty)
                result = HttpContext.Current.Request.UserHostAddress;

            return result;
        }


    }

    /// <summary>
    /// 随机生成6位数字密码
    /// </summary>
    /// <returns></returns>
    public static string RandomPassword()
    {
        Random rd = new Random();
        string str = "0123456789";
        string result = "";
        for (int i = 0; i < 6; i++)
        {
            result += str[rd.Next(str.Length)];
        }
        return result;
    }

    /// <summary>
    /// 随机生成4位数字
    /// </summary>
    /// <returns></returns>
    public static string Random4()
    {
        Random rd = new Random();
        string str = "0123456789";
        string result = "";
        for (int i = 0; i < 4; i++)
        {
            result += str[rd.Next(str.Length)];
        }
        return result;
    }

    /// <summary>
    ///保存日志
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="t">登录用户</param>
    /// <param name="type">类型</param>
    /// <param name="module">操作模块</param>
    /// <param name="details">详细操作</param>
    /// <returns></returns>
    protected bool SaveLog<T>(T t, LogType type, string module, string details) where T : EntityBase
    {
        return LogService.SaveLog<T>(DbUtil, t, type, module, details);
    }

    public bool IsReusable
    {
        get
        {
            return false;
        }
    }
    /// <summary>
    /// 写入日志
    /// 创建人：季李刚
    /// 创建时间：2021年9月15日
    /// </summary>
    /// <param name="error"></param>
    private void WriteLog(string fuc, object error)
    {
        ErrLogs.Log(fuc, System.Web.Hosting.HostingEnvironment.MapPath(string.Format("~/Logs/{0}ErrLog.txt", DateTime.Now.ToString("yyyyMMdd"))), "", Convert.ToString(error));
    }



    /// <summary>
    /// 标准类数据同步
    /// </summary>
    /// <param name="obj"></param>
    /// <returns></returns>
    private string StandardDataSend(dynamic obj)
    {
        string resultMsg = "成功";
        bool resultStatus = true;
        TestOrg testOrg = new TestOrg();
        WriteLog("StandardDataSend：", obj);
        try
        {
            JObject jObject = JObject.Parse(obj.ToString());
            string Token = GetPar(jObject, "Token");
            testOrg = DbUtil.GetT<TestOrg>(r => r.Token == Token);
            if (testOrg != null)
            {
                if (GetLoginUserIdByToken(Token) != 0)
                {
                    string dataVal = GetPar(jObject, "Data");

                    dataVal = dataVal.Replace("%2B", "+");

                    JArray array = (JArray)JsonConvert.DeserializeObject(dataVal);
                    List<string> list = new List<string>();
                    for (int i = 0; i < array.Count; i++)
                    {
                        string OrgCode = array[i]["OrgCode"] == null ? "" : array[i]["OrgCode"].ToString().Replace("'", "");
                        string ChargeCode = array[i]["ChargeCode"] == null ? "" : array[i]["ChargeCode"].ToString().Replace("'", "");
                        string ChargeType = array[i]["ChargeType"] == null ? "" : array[i]["ChargeType"].ToString().Replace("'", "");
                        string Level1 = array[i]["Level1"] == null ? "" : array[i]["Level1"].ToString().Replace("'", "");
                        string Level2 = array[i]["Level2"] == null ? "" : array[i]["Level2"].ToString().Replace("'", "");
                        string Level3 = array[i]["Level3"] == null ? "" : array[i]["Level3"].ToString().Replace("'", "");
                        string Level4 = array[i]["Level4"] == null ? "" : array[i]["Level4"].ToString().Replace("'", "");
                        string Item = array[i]["Item"] == null ? "" : array[i]["Item"].ToString().Replace("'", "");
                        string StandardNo = array[i]["StandardNo"] == null ? "" : array[i]["StandardNo"].ToString().Replace("'", "");
                        string Unit = array[i]["Unit"] == null ? "" : array[i]["Unit"].ToString().Replace("'", "");
                        string Money = array[i]["Money"] == null ? "0" : (array[i]["Money"].ToString() == "" ? "0" : array[i]["Money"].ToString());
                        string Remark = array[i]["Remark"] == null ? "" : array[i]["Remark"].ToString().Replace("'", "");
                        string BusinessType = array[i]["BusinessType"] == null ? "" : (array[i]["BusinessType"].ToString() == "" ? "" : array[i]["BusinessType"].ToString());
                        string DataType = array[i]["DataType"] == null ? "0" : (array[i]["DataType"].ToString() == "" ? "0" : array[i]["DataType"].ToString());
                        string Money2 = array[i]["Money2"] == null ? "" : array[i]["Money2"].ToString();

                        string IsUpdate = array[i]["IsUpdate"].ToString();

                        string sql = "";

                        if (string.IsNullOrEmpty(OrgCode))
                        {
                            resultMsg = "单位统一社会信用代码不能为空";
                            resultStatus = false;
                        }
                        else if (string.IsNullOrEmpty(ChargeCode))
                        {
                            resultMsg = "收费编码不能为空";
                            resultStatus = false;
                        }
                        else
                        {
                            if (IsUpdate == "1")
                            {
                                sql = string.Format(@"update Charge_Standard set OrgCode='{0}',ChargeType='{1}',Level1='{2}',Level2='{3}',Level3='{4}',Level4='{5}',Item='{6}',StandardNo='{7}' 
                                                ,Unit='{8}',Money='{9}',Remark='{10}',InputTime='{11}',BusinessType='{12}',DataType='{13}',Money2='{14}' where ChargeCode = '{15}' and OrgCode='{16}';",
                                                      OrgCode, ChargeType, Level1, Level2, Level3, Level4, Item, StandardNo, Unit, Money, Remark, DateTime.Now, BusinessType, DataType, Money2, ChargeCode, OrgCode);
                            }
                            else if (IsUpdate == "2")
                            {
                                sql = string.Format(@"insert into Charge_Standard(OrgCode,ChargeCode,ChargeType,Level1,Level2,Level3,Level4,Item,StandardNo,Unit,Money,Remark,InputTime,BusinessType,DataType,Money2) 
                                        values('{0}','{1}','{2}','{3}','{4}','{5}','{6}','{7}','{8}','{9}','{10}','{11}','{12}','{13}','{14}','{15}');",
                                            OrgCode, ChargeCode, ChargeType, Level1, Level2, Level3, Level4, Item, StandardNo, Unit, Money, Remark, DateTime.Now, BusinessType, DataType, Money2);
                            }
                            else if (IsUpdate == "3")
                            {
                                sql = string.Format("delete from Charge_Standard where OrgCode='{0}' and ChargeCode='{1}';", OrgCode, ChargeCode);
                            }

                            if (!string.IsNullOrEmpty(sql))
                            {
                                list.Add(sql);
                            }
                        }
                    }
                    if (list.Count > 0)
                    {
                        resultStatus = MsSqlUtil.ExecuteSqlTran(list);
                    }
                }
                else
                {
                    resultMsg = "Token过期";
                    resultStatus = false;
                }
            }
            else
            {
                resultMsg = "用户信息验证不通过";
                resultStatus = false;
            }
        }
        catch (Exception ex)
        {
            WriteLog("StandardDataSend错误信息：", ex);
            resultMsg = ex.ToString();
            resultStatus = false;
        }
        finally
        {
            int OrgID = 0;
            if (testOrg != null)
            {
                OrgID = testOrg.Id;
            }
            RequestLog(resultStatus, "Verification", resultMsg, "标准类数据推送", obj.ToString(), OrgID);
        }
        JObject jOb = new JObject();
        jOb.Add("Success", resultStatus);
        if (resultStatus == false)
        {
            resultMsg = resultMsg.Replace("成功", "失败，");
        }
        jOb.Add("Msg", resultMsg);
        return JsonConvert.SerializeObject(jOb);

    }

}