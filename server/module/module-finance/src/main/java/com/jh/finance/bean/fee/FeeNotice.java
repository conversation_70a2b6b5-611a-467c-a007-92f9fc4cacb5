package com.jh.finance.bean.fee;

import java.util.Date;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.jh.finance.config.StringFWBXssDeserializer2;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.jh.common.bean.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

/**
 * 收费公示对象 fee_notice
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Table(name = "fee_notice")
@Schema(description = "收费公示")
@Data
public class FeeNotice extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "TITLE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "标题")
    private String title;

    @Column(name = "CONTENT")
    @ColumnType(jdbcType = JdbcType.LONGVARCHAR)
    @Schema(description = "内容（富文本）")
    @JsonDeserialize(using = StringFWBXssDeserializer2.class)
    private String content;

    @Column(name = "IS_PUBLISH")
    @ColumnType(jdbcType = JdbcType.TINYINT)
    @Schema(description = "是否发布（0保存 1发布）")

    private Integer isPublish;
    
    @Column(name = "PUBLISH_DATE")
    @ColumnType(jdbcType = JdbcType.DATE)
    @Schema(description = "发布日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @JSONField(format = "yyyy-MM-dd")
    private Date publishDate;
    
    @Column(name = "USER_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "用户ID")
    private String userId;

    @Column(name = "ORG_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "机构CODE")
    private String oegCode;
}