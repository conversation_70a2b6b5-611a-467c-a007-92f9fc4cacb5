package com.jh.finance.service.report;

import java.util.List;

import com.github.pagehelper.PageInfo;

import com.jh.finance.bean.report.ReportDocument;
import com.jh.finance.bean.report.vo.ReportDocumentVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件管理Service接口
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
public interface ReportDocumentService {
    /**
     * 保存或更新文件管理
     *
     * @param reportDocument 文件管理对象
     * @return String 文件管理ID
     * <AUTHOR>
     */
    String saveOrUpdateReportDocument(ReportDocument reportDocument);

    /**
     * 删除文件管理
     *
     * @param ids void 文件管理ID
     * <AUTHOR>
     */
    void deleteReportDocument(List<String> ids);


    /**
     * 分页查询文件管理
     *
     * @param reportDocumentVo
     * @return PageInfo<ReportDocument>
     * <AUTHOR>
     */
    PageInfo<ReportDocument> findPageByQuery(ReportDocumentVo reportDocumentVo);
	
	/**
	 * 文件管理上报
	 *
	 * @param vo 包含上报参数的VO对象
	 */
	void documentReport(ReportDocumentVo vo);
	
	/**
     * 文件管理审核
     *
     * @param vo 包含审核参数的VO对象
     */
    void documentAudit(ReportDocumentVo vo);

    /**
     * 根据文件管理ID查询附件列表
     *
     * @param documentId 文件管理ID
     * @return 附件列表
     */
    java.util.List<com.jh.finance.bean.report.ReportDocumentFile> getFilesByDocumentId(String documentId);

    /**
     * 查询文件管理详情（包含文件列表）
     *
     * @param id 文件管理ID
     * @return 文件管理详情VO
     */
    ReportDocumentVo findDetailById(String id);
}
