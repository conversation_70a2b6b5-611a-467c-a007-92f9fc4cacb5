package com.jh.finance.service.report;

import java.util.List;
import com.github.pagehelper.PageInfo;
import com.jh.finance.bean.report.ReportBasiness;
import com.jh.finance.bean.report.vo.ReportBasinessVo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 收费统计表Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface ReportBasinessService {
	/**
	 * 保存或更新收费统计表
	 *@param reportBasiness 收费统计表对象
	 *@return String 收费统计表ID
	 *<AUTHOR>
	 */
	String saveOrUpdateReportBasiness(ReportBasiness reportBasiness);
	
	/**
	 * 删除收费统计表
	 *@param ids void 收费统计表ID
	 *<AUTHOR>
	 */
	void deleteReportBasiness(List<String> ids);

	/**
	 * 查询收费统计表详情
	 *@param id
	 *@return ReportBasiness
	 *<AUTHOR>
	 */
	ReportBasiness findById(String id);

	/**
	 * 分页查询收费统计表
	 *@param reportBasinessVo
	 *@return PageInfo<ReportBasiness>
	 *<AUTHOR>
	 */
	PageInfo<ReportBasiness> findPageByQuery(ReportBasinessVo reportBasinessVo);

} 