package com.jh.finance.bean.fee.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.alibaba.excel.annotation.ExcelProperty;

import java.io.Serializable;

/**
 * 减费惠企改革-在线反馈对象 fee_reduction_feedback
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public class FeeReductionFeedbackExcel implements Serializable {
    private static final long serialVersionUID = 1L;
    @ExcelProperty(value = "用户ID", index = 0)
    private String userId;
    @ExcelProperty(value = "企业名称", index = 1)
    private String enterpriseName;
    @ExcelProperty(value = "区县名称", index = 2)
    private String districtName;
    @ExcelProperty(value = "政策名称", index = 3)
    private String policyName;
    @ExcelProperty(value = "行业门类", index = 4)
    private String industryCategory;
    @ExcelProperty(value = "本次减免费用(元)", index = 5)
    private BigDecimal reductionAmount;
    @ExcelProperty(value = "享受时间", index = 6)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date enjoyTime;
    @ExcelProperty(value = "状态：0-未反馈，1-已反馈", index = 7)
    private Integer status;
    /**
     * 导入情况
     */
    @ExcelProperty(value = "导入情况", index = 8)
    private String importSituation;

    public String getImportSituation() {
        return importSituation;
    }

    public void setImportSituation(String importSituation) {
        this.importSituation = importSituation;
    }

    /**
     * SET 用户ID
     *
     * @param userId
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId;
    }

    /**
     * GET 用户ID
     *
     * @return userId
     */
    public String getUserId() {
        return userId;
    }

    /**
     * SET 企业名称
     *
     * @param enterpriseName
     */
    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName == null ? null : enterpriseName;
    }

    /**
     * GET 企业名称
     *
     * @return enterpriseName
     */
    public String getEnterpriseName() {
        return enterpriseName;
    }

    /**
     * SET 区县名称
     *
     * @param districtName
     */
    public void setDistrictName(String districtName) {
        this.districtName = districtName == null ? null : districtName;
    }

    /**
     * GET 区县名称
     *
     * @return districtName
     */
    public String getDistrictName() {
        return districtName;
    }

    /**
     * SET 政策名称
     *
     * @param policyName
     */
    public void setPolicyName(String policyName) {
        this.policyName = policyName == null ? null : policyName;
    }

    /**
     * GET 政策名称
     *
     * @return policyName
     */
    public String getPolicyName() {
        return policyName;
    }

    /**
     * SET 行业门类
     *
     * @param industryCategory
     */
    public void setIndustryCategory(String industryCategory) {
        this.industryCategory = industryCategory == null ? null : industryCategory;
    }

    /**
     * GET 行业门类
     *
     * @return industryCategory
     */
    public String getIndustryCategory() {
        return industryCategory;
    }

    /**
     * SET 本次减免费用(元)
     *
     * @param reductionAmount
     */
    public void setReductionAmount(BigDecimal reductionAmount) {
        this.reductionAmount = reductionAmount;
    }

    /**
     * GET 本次减免费用(元)
     *
     * @return reductionAmount
     */
    public BigDecimal getReductionAmount() {
        return reductionAmount;
    }

    /**
     * SET 享受时间
     *
     * @param enjoyTime
     */
    public void setEnjoyTime(Date enjoyTime) {
        this.enjoyTime = enjoyTime;
    }

    /**
     * GET 享受时间
     *
     * @return enjoyTime
     */
    public Date getEnjoyTime() {
        return enjoyTime;
    }

    /**
     * SET 状态：0-未反馈，1-已反馈
     *
     * @param status
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * GET 状态：0-未反馈，1-已反馈
     *
     * @return status
     */
    public Integer getStatus() {
        return status;
    }
}
