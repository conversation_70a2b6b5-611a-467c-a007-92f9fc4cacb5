package com.jh.finance.service.report.impl;

import com.jh.finance.bean.report.ReportAdvice;
import com.jh.finance.bean.report.vo.ReportAdviceVo;
import com.jh.finance.dao.report.ReportAdviceMapper;
import com.jh.finance.service.report.ReportAdviceService;
import com.jh.utils.ImportService;

import java.util.List;
import java.util.ArrayList;
import java.util.Date;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;
import com.jh.common.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;
import static com.github.pagehelper.page.PageMethod.orderBy;
import org.springframework.web.multipart.MultipartFile;

/**
 * 数据上报流程-审批意见表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-10
 */
@Service
@Transactional(readOnly = true)
public class ReportAdviceServiceImpl extends BaseServiceImpl<ReportAdviceMapper, ReportAdvice> implements ReportAdviceService {
	
	private static final Logger logger = LoggerFactory.getLogger(ReportAdviceServiceImpl.class);
    @Autowired
    private ReportAdviceMapper reportAdviceMapper;

    @Autowired
	private ImportService importService;

	@Override
	@Transactional(readOnly = false)
	/**
	 * 保存或更新数据上报流程-审批意见表
	 *@param reportAdvice 数据上报流程-审批意见表对象
	 *@return String 数据上报流程-审批意见表ID
	 *<AUTHOR>
	 */
	public String saveOrUpdateReportAdvice(ReportAdvice reportAdvice) {
		if(reportAdvice==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(reportAdvice.getId())){
			//新增
			reportAdvice.setId(UUIDUtils.getUUID());
			reportAdvice.setCreateTime(new Date());
			reportAdvice.setUpdateTime(new Date());
			reportAdvice.setYn(CommonConstant.FLAG_YES);
			reportAdviceMapper.insertSelective(reportAdvice);
		}else{
			//TODO 做判断后方能执行修改 一般判断是否是自己的数据
			//避免页面传入修改
			reportAdvice.setUpdateTime(new Date());
			reportAdviceMapper.updateByPrimaryKeySelective(reportAdvice);
		}
		return reportAdvice.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 * 删除数据上报流程-审批意见表
	 *@param ids void 数据上报流程-审批意见表ID
	 *<AUTHOR>
	 */
	public void deleteReportAdvice(List<String> ids) {
		for(String id:ids){
			//TODO 做判断后方能执行删除 一般判断是否是自己的数据
			ReportAdvice reportAdvice=reportAdviceMapper.selectByPrimaryKey(id);
			if(reportAdvice==null){
				throw new ServiceException("非法请求");
			}
			//逻辑删除
			ReportAdvice tempReportAdvice=new ReportAdvice();
			tempReportAdvice.setYn(CommonConstant.FLAG_NO);
			tempReportAdvice.setId(reportAdvice.getId());
			reportAdviceMapper.updateByPrimaryKeySelective(tempReportAdvice);
		}
	}

	/**
	 * 查询数据上报流程-审批意见表详情
	 *@param id
	 *@return ReportAdvice
	 *<AUTHOR>
	 */
    @Override
	public ReportAdvice findById(String id) {
	    // TODO 做判断后方能反回数据 一般判断是否是自己的数据
		return reportAdviceMapper.selectByPrimaryKey(id);
	}

	/**
	 * 分页查询数据上报流程-审批意见表
	 *@param reportAdviceVo
	 *@return PageInfo<ReportAdvice>
	 *<AUTHOR>
	 */
	@Override
	public PageInfo<ReportAdvice> findPageByQuery(ReportAdviceVo reportAdviceVo) {
		// TODO 做判断后方能执行查询 一般判断是否是自己的数据
		PageHelper.startPage(reportAdviceVo.getPageNum(),reportAdviceVo.getPageSize());
		Example example=getExample(reportAdviceVo);
		List<ReportAdvice> reportAdviceList=reportAdviceMapper.selectByExample(example);
		return new PageInfo<ReportAdvice>(reportAdviceList);
	}
	
	private Example getExample(ReportAdviceVo reportAdviceVo){
		Example example=new Example(ReportAdvice.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		if(!StringUtils.isEmpty(reportAdviceVo.getApplyId())){
			criteria.andEqualTo("applyId", reportAdviceVo.getApplyId());
		}
		if(!StringUtils.isEmpty(reportAdviceVo.getStage())){
			criteria.andEqualTo("stage", reportAdviceVo.getStage());
		}
		example.orderBy("createTime").desc();
		return example;
	}

	/**
     * 根据申请ID查询审批意见
     * @param applyId 申请ID
     * @return List<ReportAdvice>
     */
    @Override
    public List<ReportAdvice> findByApplyId(String applyId) {
        if(StringUtils.isEmpty(applyId)) {
            return new ArrayList<>();
        }
        return reportAdviceMapper.findByApplyId(applyId);
    }
    
    /**
     * 根据申请ID和节点查询审批意见
     * @param applyId 申请ID
     * @param stage 节点
     * @return ReportAdvice
     */
    @Override
    public ReportAdvice findByApplyIdAndStage(String applyId, String stage) {
        if(StringUtils.isEmpty(applyId) || StringUtils.isEmpty(stage)) {
            return null;
        }
        return reportAdviceMapper.findByApplyIdAndStage(applyId, stage);
    }
} 