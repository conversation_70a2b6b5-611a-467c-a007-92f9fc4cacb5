package com.jh.finance.dao.fee;


import com.jh.common.mybatis.BaseInfoMapper;
import com.jh.finance.bean.fee.FeeNonStandard;
import org.apache.ibatis.annotations.Select;
import java.util.List;

/**
 * 非标类项目Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface FeeNonStandardMapper extends BaseInfoMapper<FeeNonStandard> {

    /**
     * 根据机构代码查询检验检测服务项目（非标类）
     *
     * @param orgCode 机构代码
     * @return List<FeeNonStandard> 非标类收费信息列表
     * <AUTHOR>
     */
    @Select("SELECT * FROM fee_non_standard WHERE ORG_CODE = #{orgCode} AND YN = 1 ORDER BY CREATE_TIME DESC")
    List<FeeNonStandard> findNonStandardByOrgCode(String orgCode);

}
