package com.jh.finance.service.report.impl;

import com.jh.finance.bean.report.ReportBusinessType;
import com.jh.finance.bean.report.vo.ReportBusinessTypeVo;
import com.jh.finance.dao.report.ReportBusinessTypeMapper;
import com.jh.finance.service.report.ReportBusinessTypeService;
import com.jh.utils.ImportService;

import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;
import com.jh.common.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;
import static com.github.pagehelper.page.PageMethod.orderBy;

/**
 * 业务类型表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@Service
@Transactional(readOnly = true)
public class ReportBusinessTypeServiceImpl extends BaseServiceImpl<ReportBusinessTypeMapper, ReportBusinessType> implements ReportBusinessTypeService {
	
	private static final Logger logger = LoggerFactory.getLogger(ReportBusinessTypeServiceImpl.class);
    @Autowired
    private ReportBusinessTypeMapper reportBusinessTypeMapper;

    @Autowired
	private ImportService importService;

	@Override
	@Transactional(readOnly = false)
	/**
	 * 保存或更新业务类型表
	 *@param reportBusinessType 业务类型表对象
	 *@return String 业务类型表ID
	 *<AUTHOR>
	 */
	public String saveOrUpdateReportBusinessType(ReportBusinessType reportBusinessType) {
		if(reportBusinessType==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(reportBusinessType.getId())){
			//新增
			reportBusinessType.setId(UUIDUtils.getUUID());
			reportBusinessType.setCreateTime(new Date());
			reportBusinessType.setUpdateTime(new Date());
			reportBusinessType.setYn(CommonConstant.FLAG_YES);
			reportBusinessTypeMapper.insertSelective(reportBusinessType);
		}else{
			//TODO 做判断后方能执行修改 一般判断是否是自己的数据
			//避免页面传入修改
			reportBusinessType.setUpdateTime(new Date());
			reportBusinessTypeMapper.updateByPrimaryKeySelective(reportBusinessType);
		}
		return reportBusinessType.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 * 删除业务类型表
	 *@param ids void 业务类型表ID
	 *<AUTHOR>
	 */
	public void deleteReportBusinessType(List<String> ids) {
		for(String id:ids){
			//TODO 做判断后方能执行删除 一般判断是否是自己的数据
			ReportBusinessType reportBusinessType=reportBusinessTypeMapper.selectByPrimaryKey(id);
			if(reportBusinessType==null){
				throw new ServiceException("非法请求");
			}
			//逻辑删除
			ReportBusinessType tempReportBusinessType=new ReportBusinessType();
			tempReportBusinessType.setYn(CommonConstant.FLAG_NO);
			tempReportBusinessType.setId(reportBusinessType.getId());
			reportBusinessTypeMapper.updateByPrimaryKeySelective(tempReportBusinessType);
		}
	}

	/**
	 * 查询业务类型表详情
	 *@param id
	 *@return ReportBusinessType
	 *<AUTHOR>
	 */
    @Override
	public ReportBusinessType findById(String id) {
	    // TODO 做判断后方能反回数据 一般判断是否是自己的数据
		return reportBusinessTypeMapper.selectByPrimaryKey(id);
	}

	/**
	 * 分页查询业务类型表
	 *@param reportBusinessTypeVo
	 *@return PageInfo<ReportBusinessType>
	 *<AUTHOR>
	 */
	@Override
	public PageInfo<ReportBusinessType> findPageByQuery(ReportBusinessTypeVo reportBusinessTypeVo) {
		// TODO 做判断后方能执行查询 一般判断是否是自己的数据
		PageHelper.startPage(reportBusinessTypeVo.getPageNum(),reportBusinessTypeVo.getPageSize());
		orderBy(reportBusinessTypeVo.getOrderColumn() + " " + reportBusinessTypeVo.getOrderValue());
		Example example=getExample(reportBusinessTypeVo);
		List<ReportBusinessType> reportBusinessTypeList=reportBusinessTypeMapper.selectByExample(example);
		return new PageInfo<ReportBusinessType>(reportBusinessTypeList);
	}
	
	private Example getExample(ReportBusinessTypeVo reportBusinessTypeVo){
		Example example=new Example(ReportBusinessType.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		if(!StringUtils.isEmpty(reportBusinessTypeVo.getName())){
			criteria.andLike("name", "%" + reportBusinessTypeVo.getName() + "%");
		}
		if(reportBusinessTypeVo.getParentId() != null){
			criteria.andEqualTo("parentId", reportBusinessTypeVo.getParentId());
		}
		if(Boolean.TRUE.equals(reportBusinessTypeVo.getOnlyParent())){
			criteria.andIsNull("parentId").orEqualTo("parentId", 0);
		}

		example.orderBy("createTime").desc();
		return example;
	}

	/**
     * 查询顶级业务类型
     * @return List<ReportBusinessType>
     */
    @Override
    public List<ReportBusinessType> findParentTypes() {
        return reportBusinessTypeMapper.findParentTypes();
    }
    
    /**
     * 根据父ID查询子业务类型
     * @param parentId 父ID
     * @return List<ReportBusinessType>
     */
    @Override
    public List<ReportBusinessType> findByParentId(Integer parentId) {
        if(parentId == null) {
            return new ArrayList<>();
        }
        return reportBusinessTypeMapper.findByParentId(parentId);
    }
    
    /**
     * 根据组织ID查询业务类型
     * @param orgId 组织ID
     * @return List<ReportBusinessType>
     */
    @Override
    public List<ReportBusinessType> findByOrgId(Integer orgId) {
        if(orgId == null) {
            return new ArrayList<>();
        }
        return reportBusinessTypeMapper.findByOrgId(orgId);
    }
    
    /**
     * 查询业务类型树形结构
     * @return List<ReportBusinessType>
     */
    @Override
    public List<ReportBusinessType> findTypeTree() {
        // 获取所有业务类型
        List<ReportBusinessType> allTypes = reportBusinessTypeMapper.findTypeTree();
        
        // 按父ID分组
        Map<Integer, List<ReportBusinessType>> parentMap = allTypes.stream()
                .filter(type -> type.getParentId() != null)
                .collect(Collectors.groupingBy(ReportBusinessType::getParentId));
        
        // 获取顶级类型
        List<ReportBusinessType> parentTypes = allTypes.stream()
                .filter(type -> type.getParentId() == null || type.getParentId() == 0)
                .collect(Collectors.toList());
        
        return parentTypes;
    }
} 