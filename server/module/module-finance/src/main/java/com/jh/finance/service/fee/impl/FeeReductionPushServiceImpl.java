package com.jh.finance.service.fee.impl;

import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.common.bean.LoginUser;
import com.jh.common.util.AppUserUtil;
import com.jh.common.util.io.FileConvertUtils;
import com.jh.common.util.io.FileUtil;
import com.jh.common.util.poi.EasyExcelUtils;
import com.jh.constant.enums.ImportStatusEnum;
import com.jh.finance.bean.fee.FeeReductionPush;
import com.jh.finance.bean.fee.vo.FeeReductionPushExcel;
import com.jh.finance.bean.fee.vo.FeeReductionPushVo;
import com.jh.finance.dao.fee.FeeReductionPushMapper;
import com.jh.finance.service.fee.FeeReductionPushService;
import com.jh.utils.ImportService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.concurrent.CompletableFuture;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;


import com.jh.common.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import static com.github.pagehelper.page.PageMethod.orderBy;

import org.springframework.web.multipart.MultipartFile;

/**
 * 减费惠企改革-政策推送Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Service
@Transactional(readOnly = true)
public class FeeReductionPushServiceImpl extends BaseServiceImpl<FeeReductionPushMapper, FeeReductionPush> implements FeeReductionPushService {

    private static final Logger logger = LoggerFactory.getLogger(FeeReductionPushServiceImpl.class);
    @Autowired
    private FeeReductionPushMapper feeReductionPushMapper;

    @Autowired
    private ImportService importService;

    @Value("${file.temp.path}")
    private String tempPath;

    @Override
    @Transactional(readOnly = false)
    /**
     * 保存或更新减费惠企改革-政策推送
     *@param feeReductionPush 减费惠企改革-政策推送对象
     *@return String 减费惠企改革-政策推送ID
     *<AUTHOR>
     */
    public String saveOrUpdateFeeReductionPush(FeeReductionPush feeReductionPush) {
        if (feeReductionPush == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(feeReductionPush.getId())) {
            //新增
            feeReductionPush.setId(UUIDUtils.getUUID());
            feeReductionPushMapper.insertSelective(feeReductionPush);
        } else {
            //TODO 做判断后方能执行修改 一般判断是否是自己的数据
            //避免页面传入修改
            feeReductionPush.setYn(null);
            feeReductionPushMapper.updateByPrimaryKeySelective(feeReductionPush);
        }
        return feeReductionPush.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     * 删除减费惠企改革-政策推送
     *@param ids void 减费惠企改革-政策推送ID
     *<AUTHOR>
     */
    public void deleteFeeReductionPush(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除 一般判断是否是自己的数据
            FeeReductionPush feeReductionPush = feeReductionPushMapper.selectByPrimaryKey(id);
            if (feeReductionPush == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            FeeReductionPush temfeeReductionPush = new FeeReductionPush();
            temfeeReductionPush.setYn(CommonConstant.FLAG_NO);
            temfeeReductionPush.setId(feeReductionPush.getId());
            feeReductionPushMapper.updateByPrimaryKeySelective(temfeeReductionPush);
        }
    }

    /**
     * 查询减费惠企改革-政策推送详情
     *
     * @param id
     * @return FeeReductionPush
     * <AUTHOR>
     */
    @Override
    public FeeReductionPush findById(String id) {
        // TODO 做判断后方能反回数据 一般判断是否是自己的数据
        return feeReductionPushMapper.selectByPrimaryKey(id);
    }


    /**
     * 分页查询减费惠企改革-政策推送
     *
     * @param feeReductionPushVo
     * @return PageInfo<FeeReductionPush>
     * <AUTHOR>
     */
    @Override
    public PageInfo<FeeReductionPush> findPageByQuery(FeeReductionPushVo feeReductionPushVo) {
        // TODO 做判断后方能执行查询 一般判断是否是自己的数据
        PageHelper.startPage(feeReductionPushVo.getPageNum(), feeReductionPushVo.getPageSize());
        orderBy(feeReductionPushVo.getOrderColumn() + " " + feeReductionPushVo.getOrderValue());
        Example example = getExample(feeReductionPushVo);
        List<FeeReductionPush> feeReductionPushList = feeReductionPushMapper.selectByExample(example);
        return new PageInfo<FeeReductionPush>(feeReductionPushList);
    }

    private Example getExample(FeeReductionPushVo feeReductionPushVo) {
        Example example = new Example(FeeReductionPush.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        
        // 企业名称模糊查询
        if (!StringUtils.isEmpty(feeReductionPushVo.getEnterpriseName())) {
            criteria.andLike("enterpriseName", "%" + feeReductionPushVo.getEnterpriseName() + "%");
        }
        
        // 推送状态查询（1是 0否）
        if (feeReductionPushVo.getIsPush() != null) {
            criteria.andEqualTo("isPush", feeReductionPushVo.getIsPush());
        }
        
        example.orderBy("createTime").desc();
        return example;
    }

    /**
     * 按条件导出查询减费惠企改革-政策推送
     *
     * @param feeReductionPushVo
     * @return PageInfo<FeeReductionPush>
     * <AUTHOR>
     */
    @Override
    public List<FeeReductionPush> findByQuery(FeeReductionPushVo feeReductionPushVo) {
        orderBy(feeReductionPushVo.getOrderColumn() + " " + feeReductionPushVo.getOrderValue());
        Example example = getExample(feeReductionPushVo);
        return feeReductionPushMapper.selectByExample(example);
    }

    /**
     * 导入减费惠企改革-政策推送
     *
     * @param file
     * @param cover 是否覆盖 1 覆盖 0 不覆盖
     * @return
     * <AUTHOR>
     */
    @Override
    public void importFeeReductionPushAsync(MultipartFile file, Integer cover) {
        //为后续数据处理线程设置用户信息
        Object user = AppUserUtil.getLoginAppUser();
        LoginUser appLoginUser = (LoginUser) user;
        importService.notification(appLoginUser, "已创建减费惠企改革-政策推送导入任务，完成后会通知", ExportRespAnnotation.SocketMessageTypeEnum.NORMAL, "减费惠企改革-政策推送", null);
        // 开始时间
        Date startTime = new Date();
        // 验证excel
        importService.verifyExcel(file);
        List<FeeReductionPushExcel> list;
        // 读取数据
        try (InputStream inputStream = file.getInputStream()) {
            list = (List<FeeReductionPushExcel>) EasyExcelUtils.readExcel(FeeReductionPushExcel.class, inputStream);
            if (CollectionUtils.isEmpty(list)) {
                throw new ServiceException("模板文件为空或者异常，请使用提供的模板进行导入");
            }
        } catch (IOException e) {
            throw new ServiceException("请检查填写内容，如：日期格式");
        }
        // 复制file 文件
        String pathForSave = tempPath + "/" + UUIDUtils.getUUID() + "&&" + file.getOriginalFilename();
        File copyFile = FileUtil.copyFile(file, pathForSave);
        MultipartFile copyMultipartFile = FileConvertUtils.createFileItem(copyFile);
        //这里写数据处理逻辑
        CompletableFuture.runAsync(() -> {
            try {
                // 设置用户信息 避免异步处理数据时丢失用户信息
                UsernamePasswordAuthenticationToken authentication =
                        new UsernamePasswordAuthenticationToken(appLoginUser, null, appLoginUser.getAuthorities());
                SecurityContextHolder.getContext().setAuthentication(authentication);
                List<FeeReductionPush> insertList = new ArrayList<>();
                //数据处理
                for (FeeReductionPushExcel item : list) {
                    //这里对数据进行校验 TODO
                    if (StringUtils.isEmpty(item.getPushTime())) {
                        item.setImportSituation(ImportStatusEnum.ERROR.getName() + "编码为空");
                        continue;
                    }

                    FeeReductionPush feeReductionPush = new FeeReductionPush();
                    BeanUtils.copyProperties(item, feeReductionPush);
                    feeReductionPush.setId(UUIDUtils.getUUID());
                    feeReductionPush.setYn(CommonConstant.FLAG_YES);
                    insertList.add(feeReductionPush);
                    item.setImportSituation(ImportStatusEnum.IMPORT_SUCCESS.getName());
                }
                //保存数据
                if (!CollectionUtils.isEmpty(insertList)) {
                    //这里处理是否覆盖 TODO
                    feeReductionPushMapper.insertList(insertList);
                }
                String fileUrl = importService.faultDataAsync(list, FeeReductionPushExcel.class, "导入结果.xls",
                        "减费惠企改革-政策推送", "减费惠企改革-政策推送", startTime, copyMultipartFile, appLoginUser);
                importService.notification(appLoginUser, "减费惠企改革-政策推送-导入结果.xls", ExportRespAnnotation.SocketMessageTypeEnum.DOWN, file.getOriginalFilename() + "导入成功", fileUrl);
            } catch (Exception e) {
                importService.notification(appLoginUser, e.getMessage(), ExportRespAnnotation.SocketMessageTypeEnum.NORMAL, "减费惠企改革-政策推送导入【出现异常】", null);
            } finally {
                try {
                    Files.deleteIfExists(Paths.get(pathForSave));
                } catch (Exception e) {
                    logger.error("删除文件失败", e);
                }
            }
        });
    }
}
