package com.jh.finance.bean.report;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.jh.common.bean.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

/**
 * 数据上报-计划管理对象 report_plan
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Table(name = "report_plan")
@Schema(description = "数据上报-计划管理")
@Data
public class ReportPlan extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "CITY_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "地市代码")
    private String cityCode;

    @Column(name = "CITY_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "地市名称")
    private String cityName;

    @Column(name = "POLICY_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "政策ID")
    private String policyId;

    @Column(name = "POLICY_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "政策名称")
    private String policyName;

    @Column(name = "PLAN_TIME")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @Schema(description = "计划时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date planTime;

    @Column(name = "TARGET_AMOUNT")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @Schema(description = "目标金额(万元)")
    private BigDecimal targetAmount;

}