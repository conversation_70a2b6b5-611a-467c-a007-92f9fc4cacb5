package com.jh.finance.service.report;

import java.util.List;

import com.github.pagehelper.PageInfo;

import com.jh.finance.bean.report.ReportPlan;
import com.jh.finance.bean.report.vo.ReportPlanVo;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 数据上报-计划管理Service接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface ReportPlanService {
    /**
     * 保存或更新计划管理数据
     *
     * @param reportPlan 计划管理数据对象
     * @return String 计划管理数据ID
     * <AUTHOR>
     */
    String saveOrUpdateReportPlan(ReportPlan reportPlan);

    /**
     * 删除计划管理数据
     *
     * @param ids void 计划管理数据ID
     * <AUTHOR>
     */
    void deleteReportPlan(List<String> ids);

    /**
     * 查询计划管理数据详情
     *
     * @param id
     * @return ReportPlan
     * <AUTHOR>
     */
    ReportPlan findById(String id);

    /**
     * 分页查询计划管理数据
     *
     * @param reportPlanVo
     * @return PageInfo<ReportPlan>
     * <AUTHOR>
     */
    PageInfo<ReportPlan> findPageByQuery(ReportPlanVo reportPlanVo);

}