package com.jh.finance.controller.fee;

import java.util.List;

import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.finance.bean.fee.FeeReductionPush;
import com.jh.finance.bean.fee.vo.FeeReductionPushVo;
import com.jh.finance.service.fee.FeeReductionPushService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.jh.common.bean.RestApiResponse;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.controller.BaseController;

import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

/**
 * 减费惠企改革-政策推送Controller
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@RestController
@RequestMapping("/fee/push")
@Tag(name = "减费惠企改革-政策推送")
public class FeeReductionPushController extends BaseController {
    @Autowired
    private FeeReductionPushService feeReductionPushService;

    private static final String PER_PREFIX = "btn:fee:push:";

    /**
     * 新增减费惠企改革-政策推送
     *
     * @param feeReductionPush 减费惠企改革-政策推送数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @Operation(summary = "新增减费惠企改革-政策推送")
    @SystemLogAnnotation(type = "减费惠企改革-政策推送", value = "新增减费惠企改革-政策推送")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveFeeReductionPush(@RequestBody FeeReductionPush feeReductionPush) {
        String id = feeReductionPushService.saveOrUpdateFeeReductionPush(feeReductionPush);
        return RestApiResponse.ok(id);
    }

    /**
     * 修改减费惠企改革-政策推送
     *
     * @param feeReductionPush 减费惠企改革-政策推送数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @Operation(summary = "修改减费惠企改革-政策推送")
    @SystemLogAnnotation(type = "减费惠企改革-政策推送", value = "修改减费惠企改革-政策推送")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateFeeReductionPush(@RequestBody FeeReductionPush feeReductionPush) {
        String id = feeReductionPushService.saveOrUpdateFeeReductionPush(feeReductionPush);
        return RestApiResponse.ok(id);
    }

    /**
     * 批量删除减费惠企改革-政策推送(判断 关联数据是否可以删除)
     *
     * @param ids
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @Operation(summary = "批量删除减费惠企改革-政策推送")
    @SystemLogAnnotation(type = "减费惠企改革-政策推送", value = "批量删除减费惠企改革-政策推送")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteFeeReductionPush(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        feeReductionPushService.deleteFeeReductionPush(ids);
        return RestApiResponse.ok();
    }

    /**
     * 查询减费惠企改革-政策推送详情
     *
     * @param id
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findById")
    @Operation(summary = "查询减费惠企改革-政策推送详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        FeeReductionPush feeReductionPush = feeReductionPushService.findById(id);
        return RestApiResponse.ok(feeReductionPush);
    }

    /**
     * 分页查询减费惠企改革-政策推送
     *
     * @param feeReductionPushVo 减费惠企改革-政策推送 查询条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/findPageByQuery")
    @Operation(summary = "分页查询减费惠企改革-政策推送")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody FeeReductionPushVo feeReductionPushVo) {
        PageInfo<FeeReductionPush> feeReductionPush = feeReductionPushService.findPageByQuery(feeReductionPushVo);
        return RestApiResponse.ok(feeReductionPush);
    }

    @PostMapping("/excel")
    @Operation(summary = "导出减费惠企改革-政策推送")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "excel')")
    // 这里的模块名称和子模块名称，请根据实际情况填写
    @ExportRespAnnotation(modType = "减费惠企改革-政策推送", sonMod = "减费惠企改革-政策推送", key = "jh_push_Table", isNotice = true, fileName = "减费惠企改革-政策推送")
    public RestApiResponse<?> excel(@RequestBody FeeReductionPushVo feeReductionPushVo) {
        List<FeeReductionPush> feeReductionPushList = feeReductionPushService.findByQuery(feeReductionPushVo);
        //这里对数据进行转换处理，如没使用字典的或关联其它表的数据。如在查询中已处理请忽略
        return RestApiResponse.ok(feeReductionPushList);
    }

    @PostMapping("/import")
    @Operation(summary = "导入减费惠企改革-政策推送")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "import')")
    public RestApiResponse<?> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "cover") Integer cover) {
        feeReductionPushService.importFeeReductionPushAsync(file, cover);
        return RestApiResponse.ok();
    }
}
