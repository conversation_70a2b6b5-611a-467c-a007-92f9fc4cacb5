<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.finance.dao.report.ReportBasinessMapper">

    <!-- 根据主表ID查询收费统计数据 -->
    <select id="findByDataId" resultType="com.jh.finance.bean.report.ReportBasiness">
        SELECT *
        FROM report_basiness
        WHERE DATA_ID = #{dataId}
          AND YN = 1
        ORDER BY BUSINESST_TYPE_ID ASC
    </select>

</mapper> 