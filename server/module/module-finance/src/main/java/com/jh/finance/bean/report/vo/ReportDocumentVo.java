package com.jh.finance.bean.report.vo;

import com.jh.finance.bean.report.ReportAdvice;
import com.jh.finance.bean.report.ReportDocument;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;


/**
 * 文件管理Vo
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@Schema(description = "ReportDocumentVo")
@Data
public class ReportDocumentVo extends ReportDocument {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;

    /** 是否有机构角色 */
    private Boolean hasOrgRole;

    /** 是否有管理员角色 */
    private Boolean hasAdminRole;

    /** 是否有财务处角色 */
    private Boolean hasFinanceRole;

    /** 用户行政区划代码 */
    private String areaCode;

    /**
     * 审核状态 1:通过 0:驳回
     */
    @Schema(description = "审核状态 1:通过 0:驳回")
    private Integer auditStatus;

    /**
     * 审核意见
     */
    @Schema(description = "审核意见")
    private String advice;

    /**
     * 文件列表
     */
    @Schema(description = "文件列表")
    private List<FileInfo> fileList;

    /**
     * 文件信息内部类
     */
    @Data
    public static class FileInfo {
        private String fileName;
        private String fileUrl;
        private String filePath;
        private String url;
        private String name;
        private Long uid;
        private String status;
    }

    private List<ReportAdvice> reportAdviceList;

}