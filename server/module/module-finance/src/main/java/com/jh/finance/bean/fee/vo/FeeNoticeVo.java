package com.jh.finance.bean.fee.vo;

import com.jh.finance.bean.fee.FeeNotice;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

@Data
public class FeeNoticeVo extends FeeNotice {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;

    @Schema(description = "发布日期开始（格式：yyyy-MM-dd）")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @JSONField(format = "yyyy-MM-dd")
    private Date publishDateStart; // 发布日期开始

    @Schema(description = "发布日期结束（格式：yyyy-MM-dd）")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @JSONField(format = "yyyy-MM-dd")
    private Date publishDateEnd;   // 发布日期结束


}