package com.jh.finance.controller.home;

import com.jh.common.bean.RestApiResponse;
import com.jh.common.util.AppUserUtil;
import com.jh.finance.bean.home.HomePageVo;
import com.jh.finance.bean.home.HomeVo;
import com.jh.finance.service.home.HomePageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Controller
@RequestMapping("/homePage")
//@Api(value = "首页数据查询", tags = "首页数据查询")
public class HomePageController {

    @Autowired
    private HomePageService homePageService;

    @GetMapping(value = "/getHomePageMatter")
    @ResponseBody
//    @ApiOperation(value = "查询代办", notes = "查询代办")
    public RestApiResponse<List<HomePageVo>> getHomePageMatter() {
        HomeVo vo = new HomeVo();
        vo.setOperUserId(AppUserUtil.getCurrentUserId());
        vo.setOperUserName(AppUserUtil.getCurrentRealName());
        List<HomePageVo> list = homePageService.getHomePageMatter(vo);
        return RestApiResponse.ok(list);
    }
}
