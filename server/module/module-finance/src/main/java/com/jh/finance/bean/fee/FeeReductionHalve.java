package com.jh.finance.bean.fee;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.jh.common.bean.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

/**
 * 减半收取对象 fee_reduction_halve
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Table(name = "fee_reduction_halve")
@Schema(description = "减半收取")
@Data
public class FeeReductionHalve extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "TYPE")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "类型")
    private Integer type;
    @Column(name = "CONTRACT_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "合同ID")
    private String contractId;
    @Column(name = "COMPANY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "企业名称")
    @JsonProperty("company")
    private String company;
    @Column(name = "USCC")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "统一社会信用代码")
    @JsonProperty("uscc")
    private String uscc;
    @Column(name = "COUNTRY_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "区县代码")
    @JsonProperty("countryCode")
    private String countryCode;
    @Column(name = "COUNTRY_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "区县名称")
    private String countryName;
    @Column(name = "CITY_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "城市代码")
    private String cityCode;
    @Column(name = "CITY_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "城市名称")
    private String cityName;
    @Column(name = "INDUSTRY_TYPE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "行业类型")
    private String industryType;
    @Column(name = "INDUSTRY_CATEGORY_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "行业门类代码")
    @JsonProperty("industryCategoryCode")
    private String industryCategoryCode;
    @Column(name = "INDUSTRY_CATEGORY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "行业门类")
    private String industryCategory;
    @Column(name = "INDUSTRY_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "行业代码")
    @JsonProperty("industryCode")
    private String industryCode;
    @Column(name = "IS_FLOW_POLICY")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "是否流程政策")
    private Integer isFlowPolicy;
    @Column(name = "POLICY_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "政策名称")
    private String policyName;
    @Column(name = "POLICY_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "政策ID")
    @JsonProperty("policyID")
    private String policyId;
    @Column(name = "POLICY_TYPE_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "政策类型ID")
    private String policyTypeId;
    @Column(name = "ENJOY_TIME")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @Schema(description = "享受时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonProperty("enjoyTime")
    private Date enjoyTime;
    @Column(name = "IS_HANDLED")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "是否处理")
    private Integer isHandled;
    @Column(name = "URL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "链接地址")
    private String url;
    @Column(name = "STATUS")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "状态")
    private Integer status;
    @Column(name = "YEAR_MONTH_STR")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "年月")
    private String yearMonthStr;
    @Column(name = "MONEY")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @Schema(description = "金额")
    @JsonProperty("money")
    private BigDecimal money;
    @Column(name = "USER_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "用户ID")
    @JsonProperty("userId")
    private String userId;
    @Column(name = "ORG_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "机构ID")
    private String orgId;

    @Column(name = "ORDER_DATE")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @Schema(description = "订单日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date orderDate;
    @Column(name = "INPUT_TIME")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @Schema(description = "录入时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date inputTime;

    @Column(name = "MONTH_NUM")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "月份")
    private String monthNum;
    @Column(name = "YEAR_NUM")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "年份")
    private String yearNum;
    @Column(name = "PRODUCT_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "产品名称")
    @JsonProperty("productName")
    private String productName;
    @Column(name = "NUM")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "数量")
    @JsonProperty("productCount")
    private Integer num;
    @Column(name = "POLICY_TYPE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "政策类型")
    private String policyType;
    @Column(name = "IS_IMPORT")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "是否导入")
    private Integer isImport;
    @Column(name = "TEL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "联系电话")
    @JsonProperty("tel")
    private String tel;
    @Column(name = "EVALUATION")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "评价")
    private String evaluation;
    @Column(name = "CONTENT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "内容")
    private String content;
    @Column(name = "LINK_MAN")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "联系人")
    private String linkMan;
    @Column(name = "UNIQUE_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "唯一标识")
    @JsonProperty("uniqueCode")
    private String uniqueCode;
    @Column(name = "RANDOM")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "随机数")
    private String random;
    @Column(name = "IS_SENDED")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "是否发送")
    private Integer isSended;
    @Column(name = "SEND_DATE")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @Schema(description = "发送日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date sendDate;
    @Column(name = "ORG_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "机构代码")
    @JsonProperty("orgCode")
    private String orgCode;


}
