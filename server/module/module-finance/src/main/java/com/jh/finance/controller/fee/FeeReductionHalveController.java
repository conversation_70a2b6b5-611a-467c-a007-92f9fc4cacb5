package com.jh.finance.controller.fee;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.controller.BaseController;
import com.jh.finance.bean.fee.FeeReductionHalve;
import com.jh.finance.bean.fee.vo.FeeReductionHalveVo;
import com.jh.finance.bean.fee.vo.FeeReductionHalveDataPushRequest;
import com.jh.finance.service.fee.FeeReductionHalveService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 减半收取Controller
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@RestController
@RequestMapping("/fee/halve")
@Tag(name = "减半收取")
public class FeeReductionHalveController extends BaseController {
    @Autowired
    private FeeReductionHalveService feeReductionHalveService;

    private static final String PER_PREFIX = "btn:fee:halve:";

    /**
     * 新增减半收取
     *
     * @param feeReductionHalve 减半收取数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @Operation(summary = "新增减半收取")
    @SystemLogAnnotation(type = "减半收取", value = "新增减半收取")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveFeeReductionHalve(@RequestBody FeeReductionHalve feeReductionHalve) {
        String id = feeReductionHalveService.saveOrUpdateFeeReductionHalve(feeReductionHalve);
        return RestApiResponse.ok(id);
    }

    /**
     * 修改减半收取
     *
     * @param feeReductionHalve 减半收取数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @Operation(summary = "修改减半收取")
    @SystemLogAnnotation(type = "减半收取", value = "修改减半收取")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateFeeReductionHalve(@RequestBody FeeReductionHalve feeReductionHalve) {
        String id = feeReductionHalveService.saveOrUpdateFeeReductionHalve(feeReductionHalve);
        return RestApiResponse.ok(id);
    }

    /**
     * 批量删除减半收取(判断 关联数据是否可以删除)
     *
     * @param ids
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @Operation(summary = "批量删除减半收取")
    @SystemLogAnnotation(type = "减半收取", value = "批量删除减半收取")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteFeeReductionHalve(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        feeReductionHalveService.deleteFeeReductionHalve(ids);
        return RestApiResponse.ok();
    }

    /**
     * 查询减半收取详情
     *
     * @param id
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findById")
    @Operation(summary = "查询减半收取详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        FeeReductionHalve feeReductionHalve = feeReductionHalveService.findById(id);
        return RestApiResponse.ok(feeReductionHalve);
    }

    /**
     * 分页查询减半收取
     *
     * @param feeReductionHalveVo 减半收取 查询条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/findPageByQuery")
    @Operation(summary = "分页查询减半收取")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody FeeReductionHalveVo feeReductionHalveVo) {
        PageInfo<FeeReductionHalve> feeReductionHalve = feeReductionHalveService.findPageByQuery(feeReductionHalveVo);
        return RestApiResponse.ok(feeReductionHalve);
    }

    /**
     * 接收外部推送的减半收取数据
     *
     * @param request 推送数据对象
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/receivePushData")
    @Operation(summary = "接收外部推送的减半收取数据")
    @SystemLogAnnotation(type = "减半收取", value = "接收外部推送数据")
    public RestApiResponse<?> receivePushData(@RequestBody FeeReductionHalveDataPushRequest request) {
        String result = feeReductionHalveService.processPushData(request);
        return RestApiResponse.ok(result);
    }


}
