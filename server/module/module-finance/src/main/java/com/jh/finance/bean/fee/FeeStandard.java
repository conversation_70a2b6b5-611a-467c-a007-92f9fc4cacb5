package com.jh.finance.bean.fee;

import java.math.BigDecimal;

import lombok.Data;
import com.jh.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

/**
 * 收费信息对象 fee_standard
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Table(name = "fee_standard")
@Schema(description = "收费信息")
@Data
public class FeeStandard extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "ORG_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "机构代码")
    private String orgCode;
    @Column(name = "CHARGE_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "收费代码")
    private String chargeCode;
    @Column(name = "CHARGE_TYPE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "收费类型")
    private String chargeType;
    @Column(name = "LEVEL1")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "一级分类")
    private String level1;
    @Column(name = "LEVEL2")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "二级分类")
    private String level2;
    @Column(name = "LEVEL3")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "三级分类")
    private String level3;
    @Column(name = "LEVEL4")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "四级分类")
    private String level4;
    @Column(name = "ITEM")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "项目")
    private String item;
    @Column(name = "STANDARD_NO")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "标准编号")
    private String standardNo;
    @Column(name = "UNIT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "单位")
    private String unit;
    @Column(name = "MONEY")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @Schema(description = "金额")
    private BigDecimal money;
    @Column(name = "REMARK")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "备注")
    private String remark;
    @Column(name = "BUSINESS_TYPE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "业务类型")
    private String businessType;
    @Column(name = "DATA_TYPE")
    @ColumnType(jdbcType = JdbcType.BIGINT)
    @Schema(description = "数据类型")
    private Long dataType;
    @Column(name = "MONEY2")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "金额2")
    private String money2;
}
