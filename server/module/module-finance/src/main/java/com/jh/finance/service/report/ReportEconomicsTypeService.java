package com.jh.finance.service.report;

import java.util.List;

import com.github.pagehelper.PageInfo;

import com.jh.finance.bean.report.ReportEconomicsType;
import com.jh.finance.bean.report.vo.ReportEconomicsTypeVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 行业类型Service接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface ReportEconomicsTypeService {
    /**
     * 保存或更新行业类型
     *
     * @param reportEconomicsType 行业类型对象
     * @return String 行业类型ID
     * <AUTHOR>
     */
    String saveOrUpdateReportEconomicsType(ReportEconomicsType reportEconomicsType);

    /**
     * 删除行业类型
     *
     * @param ids void 行业类型ID
     * <AUTHOR>
     */
    void deleteReportEconomicsType(List<String> ids);

    /**
     * 查询行业类型详情
     *
     * @param id
     * @return ReportEconomicsType
     * <AUTHOR>
     */
    ReportEconomicsType findById(String id);

    /**
     * 分页查询行业类型
     *
     * @param reportEconomicsTypeVo
     * @return PageInfo<ReportEconomicsType>
     * <AUTHOR>
     */
    PageInfo<ReportEconomicsType> findPageByQuery(ReportEconomicsTypeVo reportEconomicsTypeVo);

    /**
     * 按条件导出查询行业类型
     *
     * @param reportEconomicsTypeVo
     * @return PageInfo<ReportEconomicsType>
     * <AUTHOR>
     */
    List<ReportEconomicsType> findByQuery(ReportEconomicsTypeVo reportEconomicsTypeVo);


}
