package com.jh.finance.bean.report;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.jh.common.bean.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

/**
 * 业务类型表对象 report_business_type
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Table(name = "report_business_type")
@Schema(description = "业务类型表")
@Data
public class ReportBusinessType extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "名称")
    private String name;

    @Column(name = "PARENT_ID")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "父ID")
    private Integer parentId;

    @Column(name = "REMARK")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "备注")
    private String remark;

} 