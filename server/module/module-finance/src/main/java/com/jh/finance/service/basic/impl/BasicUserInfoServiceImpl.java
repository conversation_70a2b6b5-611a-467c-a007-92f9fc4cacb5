package com.jh.finance.service.basic.impl;

import com.jh.finance.bean.basic.BasicUserInfo;
import com.jh.finance.bean.basic.vo.BasicUserInfoVo;
import com.jh.finance.bean.basic.vo.BasicUserInfoReq;
import com.jh.finance.dao.basic.BasicUserInfoMapper;
import com.jh.finance.service.basic.BasicUserInfoService;

import java.util.Date;
import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;
import com.jh.common.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import static com.github.pagehelper.page.PageMethod.orderBy;

import com.jh.common.bean.SysUser;
import com.jh.common.bean.LoginUser;
import com.jh.sys.dao.SysUserMapper;
import com.jh.sys.config.SecurityHandlerConfig;
import com.jh.sys.service.impl.UserDetailsServiceImpl;
import com.alibaba.fastjson2.JSONObject;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.BeanUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户平台信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Service
@Transactional(readOnly = true)
public class BasicUserInfoServiceImpl extends BaseServiceImpl<BasicUserInfoMapper, BasicUserInfo> implements BasicUserInfoService {

    private static final Logger logger = LoggerFactory.getLogger(BasicUserInfoServiceImpl.class);
    @Autowired
    private BasicUserInfoMapper basicUserInfoMapper;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SecurityHandlerConfig securityHandlerConfig;
    @Autowired
    private UserDetailsServiceImpl userDetailsService;


    @Override
    @Transactional(readOnly = false)
    /**
     * 保存或更新用户平台信息
     *@param basicUserInfo 用户平台信息对象
     *@return String 用户平台信息ID
     *<AUTHOR>
     */
    public String saveOrUpdateBasicUserInfo(BasicUserInfo basicUserInfo) {
        if (basicUserInfo == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(basicUserInfo.getId())) {
            //新增
            basicUserInfo.setId(UUIDUtils.getUUID());
            basicUserInfoMapper.insertSelective(basicUserInfo);
        } else {
            //TODO 做判断后方能执行修改 一般判断是否是自己的数据
            //避免页面传入修改
            basicUserInfo.setYn(null);
            basicUserInfoMapper.updateByPrimaryKeySelective(basicUserInfo);
        }
        return basicUserInfo.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     * 删除用户平台信息
     *@param ids void 用户平台信息ID
     *<AUTHOR>
     */
    public void deleteBasicUserInfo(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除 一般判断是否是自己的数据
            BasicUserInfo basicUserInfo = basicUserInfoMapper.selectByPrimaryKey(id);
            if (basicUserInfo == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            BasicUserInfo tembasicUserInfo = new BasicUserInfo();
            tembasicUserInfo.setYn(CommonConstant.FLAG_NO);
            tembasicUserInfo.setId(basicUserInfo.getId());
            basicUserInfoMapper.updateByPrimaryKeySelective(tembasicUserInfo);
        }
    }

    /**
     * 查询用户平台信息详情
     *
     * @param id
     * @return BasicUserInfo
     * <AUTHOR>
     */
    @Override
    public BasicUserInfo findById(String id) {
        // TODO 做判断后方能反回数据 一般判断是否是自己的数据
        return basicUserInfoMapper.selectByPrimaryKey(id);
    }


    /**
     * 分页查询用户平台信息
     *
     * @param basicUserInfoVo
     * @return PageInfo<BasicUserInfo>
     * <AUTHOR>
     */
    @Override
    public PageInfo<BasicUserInfo> findPageByQuery(BasicUserInfoVo basicUserInfoVo) {
        // TODO 做判断后方能执行查询 一般判断是否是自己的数据
        PageHelper.startPage(basicUserInfoVo.getPageNum(), basicUserInfoVo.getPageSize());
        orderBy(basicUserInfoVo.getOrderColumn() + " " + basicUserInfoVo.getOrderValue());
        Example example = getExample(basicUserInfoVo);
        List<BasicUserInfo> basicUserInfoList = basicUserInfoMapper.selectByExample(example);
        return new PageInfo<BasicUserInfo>(basicUserInfoList);
    }

    private Example getExample(BasicUserInfoVo basicUserInfoVo) {
        Example example = new Example(BasicUserInfo.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        //if(!StringUtils.isEmpty(basicUserInfoVo.getName())){
        //	criteria.andEqualTo(basicUserInfoVo.getName());
        //}
        example.orderBy("updateTime").desc();
        return example;
    }

    /**
     * 按条件导出查询用户平台信息
     *
     * @param basicUserInfoVo
     * @return PageInfo<BasicUserInfo>
     * <AUTHOR>
     */
    @Override
    public List<BasicUserInfo> findByQuery(BasicUserInfoVo basicUserInfoVo) {
        orderBy(basicUserInfoVo.getOrderColumn() + " " + basicUserInfoVo.getOrderValue());
        Example example = getExample(basicUserInfoVo);
        return basicUserInfoMapper.selectByExample(example);
    }

    /**
     * 验证用户平台APPID和APPKEY
     *
     * @param appId    平台ID
     * @param appKey   平台KEY
     * @param response HTTP响应对象
     * @return void 如果验证成功，直接返回token到response；如果失败，抛出异常
     * <AUTHOR>
     */
    @Transactional(readOnly = false)
    public BasicUserInfoReq validateAppIdAndKey(String appId, String appKey, HttpServletResponse response) {
        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(appKey)) {
            throw new ServiceException("APPID和APPKEY不能为空");
        }

        // 根据appId和appKey查询用户平台信息
        Example example = new Example(BasicUserInfo.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        criteria.andEqualTo("appId", appId);
        criteria.andEqualTo("appKey", appKey);

        BasicUserInfo info = basicUserInfoMapper.selectOneByExample(example);

        if (info == null) {
            throw new ServiceException("无效的APPID或APPKEY");
        }

        // 生成新的token（使用UUID + 时间戳）
        String token = UUIDUtils.getUUID().replace("-", "") + System.currentTimeMillis();

        // 设置过期时间为6小时后
        Date expireTime = new java.util.Date(System.currentTimeMillis() + 6 * 60 * 60 * 1000);

        // 更新token和过期时间
        BasicUserInfo updateInfo = new BasicUserInfo();
        updateInfo.setId(info.getId());
        updateInfo.setToken(token);
        updateInfo.setTokenExpireTime(expireTime);

        basicUserInfoMapper.updateByPrimaryKeySelective(updateInfo);

        logger.info("为用户生成新token: appId={}, token={}", appId, token);

        BasicUserInfoReq result = new BasicUserInfoReq();
        result.setToken(token);
        result.setAppId(appId);
        result.setAppKey(appKey);
        return result;
    }


    /**
     * 验证token的有效性并返回用户信息
     *
     * @param token 待验证的token
     * @return BasicUserInfo 如果token有效则返回用户信息，否则返回null
     * <AUTHOR>
     */
    @Override
    public SysUser validateTokenAndGetUser(String token) {
        if (StringUtils.isEmpty(token)) {
            return null;
        }

        // 根据token查询用户平台信息
        Example example = new Example(BasicUserInfo.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        criteria.andEqualTo("token", token);

        BasicUserInfo info = basicUserInfoMapper.selectOneByExample(example);

        if (info == null) {
            logger.warn("Token不存在: {}", token);
            return null;
        }

        // 检查token是否过期
        if (info.getTokenExpireTime() == null || info.getTokenExpireTime().before(new Date())) {
            logger.warn("Token已过期: {}", token);
            return null;
        }

        if (!StringUtils.isEmpty(info.getUserId())) {
            SysUser sysUser = sysUserMapper.selectByPrimaryKey(info.getUserId());
            if (sysUser == null) {
                logger.info("用户不存在: {}", token);
                return null;
            } else {
                logger.info("Token验证成功: {}", token);
                return sysUser;
            }
        }

        return null;
    }
}
