package com.jh.finance.dao.report;

import com.jh.common.mybatis.BaseInfoMapper;
import com.jh.finance.bean.report.ReportAdvice;
import com.jh.finance.bean.report.ReportBesinessData;
import com.jh.finance.bean.report.vo.ReportBesinessDataVo;
import com.jh.finance.bean.report.vo.ReportStatisticsVo;
import com.jh.finance.bean.report.vo.ReportStatisticsQueryVo;
import com.jh.finance.bean.report.vo.ReportSummaryVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 收费统计Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Mapper
public interface ReportBesinessDataMapper extends BaseInfoMapper<ReportBesinessData> {

    /**
     * 根据用户角色和区域查询数据
     *
     * @param vo 查询参数
     * @return 收费统计数据列表
     */
    List<ReportBesinessData> query(ReportBesinessDataVo vo);

    /**
     * 查询代办
     *
     * @param vo 查询参数
     * @return 收费统计数据列表
     */
    List<ReportBesinessData> queryToDo(ReportBesinessDataVo vo);

    /**
     * 查询用户指定年度已提交的季度数据
     *
     * @param userId 用户ID
     * @param year   年度
     * @return 已提交的季度数据列表
     */
    List<ReportBesinessData> findCommittedReportsByYear(@Param("userId") String userId, @Param("year") Integer year, @Param("typeId") Integer typeId, @Param("flowKey") String flowKey);

    /**
     * 查询用户指定年度指定月份的数据
     *
     * @param userId 用户ID
     * @param year   年度
     * @param month  月份
     * @return 特定月份的数据列表
     */
    List<ReportBesinessData> findReportsByYearAndMonth(@Param("userId") String userId, @Param("year") Integer year, @Param("month") Integer month, @Param("typeId") Integer typeId, @Param("flowKey") String flowKey);

    /**
     * 统计收费数据总计
     *
     * @param vo 查询参数
     * @return 统计结果
     */
    List<ReportStatisticsQueryVo> getDataTotal(ReportBesinessDataVo vo);

    /**
     * 统计减负降本数据总计
     *
     * @param vo 查询参数
     * @return 统计结果
     */
    List<ReportStatisticsQueryVo> getReduceDataTotal(ReportBesinessDataVo vo);

    /**
     * 查询省内各市的汇总数据
     *
     * @param vo 查询参数
     * @return 各市汇总数据
     */
    List<ReportSummaryVo> getReportSummaryByCity(ReportBesinessDataVo vo);

    /**
     * 查询具体市下各机构的详细数据
     *
     * @param vo 查询参数
     * @return 各机构详细数据
     */
    List<ReportSummaryVo> getReportSummaryByOrg(ReportBesinessDataVo vo);

}
