package com.jh.finance.bean.report.vo;


import com.jh.finance.bean.report.ReportEconomicsType;
import io.swagger.v3.oas.annotations.media.Schema;


/**
 * 行业类型Vo
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Schema(description = "ReportEconomicsTypeVo")
public class ReportEconomicsTypeVo extends ReportEconomicsType {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}