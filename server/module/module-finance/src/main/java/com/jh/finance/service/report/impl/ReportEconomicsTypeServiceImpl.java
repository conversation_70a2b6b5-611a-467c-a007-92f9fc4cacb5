package com.jh.finance.service.report.impl;


import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.common.bean.LoginUser;
import com.jh.common.util.AppUserUtil;
import com.jh.common.util.io.FileConvertUtils;
import com.jh.common.util.io.FileUtil;
import com.jh.common.util.poi.EasyExcelUtils;
import com.jh.constant.enums.ImportStatusEnum;
import com.jh.finance.bean.report.ReportEconomicsType;
import com.jh.finance.bean.report.vo.ReportEconomicsTypeVo;
import com.jh.finance.dao.report.ReportEconomicsTypeMapper;
import com.jh.finance.service.report.ReportEconomicsTypeService;
import com.jh.utils.ImportService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.concurrent.CompletableFuture;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;

import com.jh.common.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import static com.github.pagehelper.page.PageMethod.orderBy;

import org.springframework.web.multipart.MultipartFile;

/**
 * 行业类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Service
@Transactional(readOnly = true)
public class ReportEconomicsTypeServiceImpl extends BaseServiceImpl<ReportEconomicsTypeMapper, ReportEconomicsType> implements ReportEconomicsTypeService {

    private static final Logger logger = LoggerFactory.getLogger(ReportEconomicsTypeServiceImpl.class);
    @Autowired
    private ReportEconomicsTypeMapper reportEconomicsTypeMapper;

    @Autowired
    private ImportService importService;

    @Value("${file.temp.path}")
    private String tempPath;

    @Override
    @Transactional(readOnly = false)
    /**
     * 保存或更新行业类型
     *@param reportEconomicsType 行业类型对象
     *@return String 行业类型ID
     *<AUTHOR>
     */
    public String saveOrUpdateReportEconomicsType(ReportEconomicsType reportEconomicsType) {
        if (reportEconomicsType == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(reportEconomicsType.getId())) {
            //新增
            reportEconomicsType.setId(UUIDUtils.getUUID());
            reportEconomicsTypeMapper.insertSelective(reportEconomicsType);
        } else {
            //TODO 做判断后方能执行修改 一般判断是否是自己的数据
            //避免页面传入修改
            reportEconomicsType.setYn(null);
            reportEconomicsTypeMapper.updateByPrimaryKeySelective(reportEconomicsType);
        }
        return reportEconomicsType.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     * 删除行业类型
     *@param ids void 行业类型ID
     *<AUTHOR>
     */
    public void deleteReportEconomicsType(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除 一般判断是否是自己的数据
            ReportEconomicsType reportEconomicsType = reportEconomicsTypeMapper.selectByPrimaryKey(id);
            if (reportEconomicsType == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            ReportEconomicsType temreportEconomicsType = new ReportEconomicsType();
            temreportEconomicsType.setYn(CommonConstant.FLAG_NO);
            temreportEconomicsType.setId(reportEconomicsType.getId());
            reportEconomicsTypeMapper.updateByPrimaryKeySelective(temreportEconomicsType);
        }
    }

    /**
     * 查询行业类型详情
     *
     * @param id
     * @return ReportEconomicsType
     * <AUTHOR>
     */
    @Override
    public ReportEconomicsType findById(String id) {
        // TODO 做判断后方能反回数据 一般判断是否是自己的数据
        return reportEconomicsTypeMapper.selectByPrimaryKey(id);
    }


    /**
     * 分页查询行业类型
     *
     * @param reportEconomicsTypeVo
     * @return PageInfo<ReportEconomicsType>
     * <AUTHOR>
     */
    @Override
    public PageInfo<ReportEconomicsType> findPageByQuery(ReportEconomicsTypeVo reportEconomicsTypeVo) {
        // TODO 做判断后方能执行查询 一般判断是否是自己的数据
        PageHelper.startPage(reportEconomicsTypeVo.getPageNum(), reportEconomicsTypeVo.getPageSize());
        orderBy(reportEconomicsTypeVo.getOrderColumn() + " " + reportEconomicsTypeVo.getOrderValue());
        Example example = getExample(reportEconomicsTypeVo);
        List<ReportEconomicsType> reportEconomicsTypeList = reportEconomicsTypeMapper.selectByExample(example);
        return new PageInfo<ReportEconomicsType>(reportEconomicsTypeList);
    }

    private Example getExample(ReportEconomicsTypeVo reportEconomicsTypeVo) {
        Example example = new Example(ReportEconomicsType.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        //if(!StringUtils.isEmpty(reportEconomicsTypeVo.getName())){
        //	criteria.andEqualTo(reportEconomicsTypeVo.getName());
        //}
        example.orderBy("updateTime").desc();
        return example;
    }

    /**
     * 按条件导出查询行业类型
     *
     * @param reportEconomicsTypeVo
     * @return PageInfo<ReportEconomicsType>
     * <AUTHOR>
     */
    @Override
    public List<ReportEconomicsType> findByQuery(ReportEconomicsTypeVo reportEconomicsTypeVo) {
        orderBy(reportEconomicsTypeVo.getOrderColumn() + " " + reportEconomicsTypeVo.getOrderValue());
        Example example = getExample(reportEconomicsTypeVo);
        return reportEconomicsTypeMapper.selectByExample(example);
    }

}
