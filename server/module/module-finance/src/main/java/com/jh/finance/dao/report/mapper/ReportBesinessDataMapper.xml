<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.finance.dao.report.ReportBesinessDataMapper" >

    <!-- 根据用户角色和区域查询数据 -->
    <select id="query" parameterType="com.jh.finance.bean.report.vo.ReportBesinessDataVo" resultType="com.jh.finance.bean.report.ReportBesinessData">
        SELECT DISTINCT t.*
        FROM report_besiness_data t
        INNER JOIN sys_user u ON t.USER_ID = u.ID
        <where>
            t.YN = 1

            <!-- 管理员角色可以查看所有数据 -->
            <if test="hasAdminRole != true">
                <!-- 机构角色只能查看自己的数据 -->
                <if test="hasOrgRole == true and hasRegionRole != true and hasFinanceRole != true and hasScienceRole != true">
                    AND t.USER_ID = #{userId}
                </if>

                <!-- 地市角色可以查看下属区县的待审核和已处理数据 -->
                <if test="hasRegionRole == true and areaCode != null">
                    AND u.AREA_CODE LIKE CONCAT(#{areaCode}, '%')
                    AND (t.ACT_NODE_KEY = '10' OR t.ACT_NODE_KEY = '20')
                </if>

                <!-- 财务处和科技处可以查看下属区域的数据，但不能审核 -->
                <if test="(hasFinanceRole == true or hasScienceRole == true) and areaCode != null">
                    AND u.AREA_CODE LIKE CONCAT(#{areaCode}, '%')
                </if>
            </if>

            <!-- 按地区代码筛选 -->
            <if test="code != null and code != ''">
                <choose>
                    <when test="code.endsWith('00')">
                        AND u.AREA_CODE LIKE CONCAT(SUBSTRING(#{code}, 1, LENGTH(#{code}) - 2), '%')
                    </when>
                    <otherwise>
                        AND u.AREA_CODE = #{code}
                    </otherwise>
                </choose>
            </if>

            <!-- 按收费类型查询 -->
            <if test="typeId != null and typeId != ''">
                AND t.TYPE_ID = #{typeId}
            </if>

            <!-- 按年份查询 -->
            <if test="year != null and year != ''">
                AND t.YEAR = #{year}
            </if>

            <!-- 按年份范围查询 -->
            <if test="yearStart != null and yearStart != ''">
                AND t.YEAR &gt;= #{yearStart}
            </if>
            <if test="yearEnd != null and yearEnd != ''">
                AND t.YEAR &lt;= #{yearEnd}
            </if>

            <!-- 按季度查询 -->
            <if test="quarter != null and quarter != ''">
                AND t.QUARTER = #{quarter}
            </if>

            <!-- 按月份查询 -->
            <if test="month != null and month != ''">
                AND t.MONTH = #{month}
            </if>

            <!-- 按流程名称查询 -->
            <if test="flowName != null and flowName != ''">
                AND t.FLOW_NAME = #{flowName}
            </if>

            AND t.FLOW_KEY = #{flowKey}

        </where>
        ORDER BY t.CREATE_TIME DESC
    </select>
    
    <!-- 查询用户指定年度已提交的季度数据 -->
    <select id="findCommittedReportsByYear" resultType="com.jh.finance.bean.report.ReportBesinessData">
        SELECT *
        FROM report_besiness_data
        WHERE USER_ID = #{userId}
                  AND YEAR = #{year}
          AND YN = 1
          AND IS_COMMIT = 1
          AND TYPE_ID = #{typeId}
          AND FLOW_KEY = #{flowKey}
        ORDER BY QUARTER ASC
    </select>

    
    <!-- 查询用户指定年度指定月份的数据 -->
    <select id="findReportsByYearAndMonth" resultType="com.jh.finance.bean.report.ReportBesinessData">
        SELECT *
        FROM report_besiness_data
        WHERE USER_ID = #{userId}
                  AND YEAR = #{year}
                  AND MONTH = #{month}
          AND TYPE_ID = #{typeId}
          AND IS_COMMIT = 1
          AND YN = 1
          AND FLOW_KEY = #{flowKey}
        ORDER BY CREATE_TIME DESC
    </select>

    <!-- 统计收费数据总计 -->
    <select id="getDataTotal" parameterType="com.jh.finance.bean.report.vo.ReportBesinessDataVo" resultType="com.jh.finance.bean.report.vo.ReportStatisticsQueryVo">
        SELECT 
            ra.CODE as areaCode,
            ra.NAME as areaName,
            COALESCE(SUM(CASE WHEN rbt.NAME = '计量' THEN rb.MONEY END), 0) as metrology,
            COALESCE(SUM(CASE WHEN rbt.NAME = '特检' THEN rb.MONEY END), 0) as specialInspection,
            COALESCE(SUM(CASE WHEN rbt.NAME = '质检' THEN rb.MONEY END), 0) as qualityInspection,
            COALESCE(SUM(CASE WHEN rbt.NAME = '市场监管系统学（协）会' THEN rb.MONEY END), 0) as marketSupervisionAssociation,
            COALESCE(SUM(CASE WHEN rbt.NAME = '标准化' THEN rb.MONEY END), 0) as standardization,
            COALESCE(SUM(CASE WHEN rbt.NAME = '知识产权' THEN rb.MONEY END), 0) as intellectualProperty,
            COALESCE(SUM(CASE WHEN rbt.NAME = '市场导报社' THEN rb.MONEY END), 0) as marketReportSociety,
            COALESCE(SUM(CASE WHEN rbt.NAME = '广告监测' THEN rb.MONEY END), 0) as advertisingMonitoring,
            COALESCE(SUM(CASE WHEN rbt.NAME = '食品' THEN rb.MONEY END), 0) as food,
            COALESCE(SUM(CASE WHEN rbt.NAME = '药品' THEN rb.MONEY END), 0) as medicine
        FROM report_area ra
        LEFT JOIN sys_user u ON u.AREA_CODE = ra.CODE
        LEFT JOIN report_besiness_data rbd ON rbd.USER_ID = u.ID 
            AND rbd.YN = 1 
            AND rbd.ACT_NODE_KEY = '20' AND rbd.flow_key = #{flowKey}
            <if test="queryType == '1'">
                AND rbd.FLOW_KEY = 'REPORT_FEE'
            </if>
            <if test="queryType == '2'">
                AND rbd.FLOW_KEY = 'REPORT_FEE_POLICY'
            </if>
            <if test="year != null and year != ''">
                AND rbd.YEAR = #{year}
            </if>
            <if test="yearStart != null and yearStart != '' and quarterStart != null">
                AND (rbd.YEAR &gt; #{yearStart} OR (rbd.YEAR = #{yearStart} AND rbd.QUARTER &gt;= #{quarterStart}))
            </if>
            <if test="yearEnd != null and yearEnd != '' and quarterEnd != null">
                AND (rbd.YEAR &lt; #{yearEnd} OR (rbd.YEAR = #{yearEnd} AND rbd.QUARTER &lt;= #{quarterEnd}))
            </if>
            <if test="code != null and code != ''">
                <choose>
                    <when test="code.endsWith('00')">
                        AND ra.CODE LIKE CONCAT(SUBSTRING(#{code}, 1, LENGTH(#{code}) - 2), '%')
                    </when>
                    <otherwise>
                        AND ra.CODE = #{code}
                    </otherwise>
                </choose>
            </if>
            <if test="quarter != null and quarter != ''">
                AND rbd.QUARTER = #{quarter}
            </if>
            <if test="month != null and month != ''">
                AND rbd.MONTH = #{month}
            </if>
        LEFT JOIN report_basiness rb ON rb.DATA_ID = rbd.ID AND rb.YN = 1
        LEFT JOIN report_business_type rbt ON rbt.ID = rbd.TYPE_ID AND rbt.YN = 1 AND rbt.PARENT_ID = 0
        <where>
            ra.YN = 1
            <!-- 只查询浙江省内的数据 -->
            AND ra.CODE LIKE '33%'
            
            <!-- 根据用户角色控制数据范围 -->
            <if test="hasAdminRole == true or hasFinanceRole == true or hasScienceRole == true">
                <!-- 管理员、财务处、科技处可以查看全省数据，按市级显示 -->
                AND ra.LEVEL = 1
            </if>
            
            <if test="hasRegionRole == true and hasAdminRole != true and hasFinanceRole != true and hasScienceRole != true">
                <!-- 地市角色只能查看下属区县数据 -->
                AND ra.LEVEL = 2
                <if test="areaCode != null and areaCode != ''">
                    AND ra.CODE LIKE CONCAT(#{areaCode}, '%')
                </if>
            </if>
        </where>
        GROUP BY ra.CODE, ra.NAME
        ORDER BY ra.SORT, ra.CODE
    </select>

    <!-- 统计减负降本数据总计 -->
    <select id="getReduceDataTotal" parameterType="com.jh.finance.bean.report.vo.ReportBesinessDataVo" resultType="com.jh.finance.bean.report.vo.ReportStatisticsQueryVo">
        SELECT 
            ra.CODE as areaCode,
            ra.NAME as areaName,
            COALESCE(SUM(CASE WHEN MONTH(rr.ENJOY_TIME) = 1 THEN rr.REDUCE_AMOUNT END), 0) as january,
            COALESCE(SUM(CASE WHEN MONTH(rr.ENJOY_TIME) = 2 THEN rr.REDUCE_AMOUNT END), 0) as february,
            COALESCE(SUM(CASE WHEN MONTH(rr.ENJOY_TIME) = 3 THEN rr.REDUCE_AMOUNT END), 0) as march,
            COALESCE(SUM(CASE WHEN MONTH(rr.ENJOY_TIME) = 4 THEN rr.REDUCE_AMOUNT END), 0) as april,
            COALESCE(SUM(CASE WHEN MONTH(rr.ENJOY_TIME) = 5 THEN rr.REDUCE_AMOUNT END), 0) as may,
            COALESCE(SUM(CASE WHEN MONTH(rr.ENJOY_TIME) = 6 THEN rr.REDUCE_AMOUNT END), 0) as june,
            COALESCE(SUM(CASE WHEN MONTH(rr.ENJOY_TIME) = 7 THEN rr.REDUCE_AMOUNT END), 0) as july,
            COALESCE(SUM(CASE WHEN MONTH(rr.ENJOY_TIME) = 8 THEN rr.REDUCE_AMOUNT END), 0) as august,
            COALESCE(SUM(CASE WHEN MONTH(rr.ENJOY_TIME) = 9 THEN rr.REDUCE_AMOUNT END), 0) as september,
            COALESCE(SUM(CASE WHEN MONTH(rr.ENJOY_TIME) = 10 THEN rr.REDUCE_AMOUNT END), 0) as october,
            COALESCE(SUM(CASE WHEN MONTH(rr.ENJOY_TIME) = 11 THEN rr.REDUCE_AMOUNT END), 0) as november,
            COALESCE(SUM(CASE WHEN MONTH(rr.ENJOY_TIME) = 12 THEN rr.REDUCE_AMOUNT END), 0) as december
        FROM report_area ra
        LEFT JOIN sys_user u ON u.AREA_CODE = ra.CODE
        LEFT JOIN report_reduce rr ON rr.USER_ID = u.ID 
            AND rr.YN = 1
            <if test="year != null and year != ''">
                AND YEAR(rr.ENJOY_TIME) = #{year}
            </if>
            <if test="yearStart != null and yearStart != '' and quarterStart != null">
                AND (YEAR(rr.ENJOY_TIME) &gt; #{yearStart} OR (YEAR(rr.ENJOY_TIME) = #{yearStart} AND QUARTER(rr.ENJOY_TIME) &gt;= #{quarterStart}))
            </if>
            <if test="yearEnd != null and yearEnd != '' and quarterEnd != null">
                AND (YEAR(rr.ENJOY_TIME) &lt; #{yearEnd} OR (YEAR(rr.ENJOY_TIME) = #{yearEnd} AND QUARTER(rr.ENJOY_TIME) &lt;= #{quarterEnd}))
            </if>
            <if test="code != null and code != ''">
                <choose>
                    <when test="code.endsWith('00')">
                        AND ra.CODE LIKE CONCAT(SUBSTRING(#{code}, 1, LENGTH(#{code}) - 2), '%')
                    </when>
                    <otherwise>
                        AND ra.CODE = #{code}
                    </otherwise>
                </choose>
            </if>
            <if test="quarter != null and quarter != ''">
                AND QUARTER(rr.ENJOY_TIME) = #{quarter}
            </if>
            <if test="month != null and month != ''">
                AND MONTH(rr.ENJOY_TIME) = #{month}
            </if>
        <where>
            ra.YN = 1
            <!-- 只查询浙江省内的数据 -->
            AND ra.CODE LIKE '33%'
            
            <!-- 根据用户角色控制数据范围 -->
            <if test="hasAdminRole == true or hasFinanceRole == true or hasScienceRole == true">
                <!-- 管理员、财务处、科技处可以查看全省数据，按市级显示 -->
                AND ra.LEVEL = 1
            </if>
            
            <if test="hasRegionRole == true and hasAdminRole != true and hasFinanceRole != true and hasScienceRole != true">
                <!-- 地市角色只能查看下属区县数据 -->
                AND ra.LEVEL = 2
                <if test="areaCode != null and areaCode != ''">
                    AND ra.CODE LIKE CONCAT(#{areaCode}, '%')
                </if>
            </if>
        </where>
        GROUP BY ra.CODE, ra.NAME
        ORDER BY ra.SORT, ra.CODE
    </select>

    <!-- 查询省内各市的汇总数据 -->
    <select id="getReportSummaryByCity" parameterType="com.jh.finance.bean.report.vo.ReportBesinessDataVo" resultType="com.jh.finance.bean.report.vo.ReportSummaryVo">
        SELECT 
            ra.CODE as areaCode,
            ra.NAME as areaName,
            ra.SORT as sort,
            COALESCE(SUM(fee_data.total_fee), 0) as feeTotal,
            COALESCE(SUM(policy_data.total_policy), 0) as policyTotal
        FROM report_area ra
        LEFT JOIN (
            SELECT 
                CASE 
                    WHEN u.AREA_CODE = '330000' THEN '330000'
                    WHEN u.AREA_CODE LIKE '33%' AND LENGTH(u.AREA_CODE) >= 4 THEN CONCAT(SUBSTRING(u.AREA_CODE, 1, 4), '00')
                    ELSE u.AREA_CODE
                END as area_group,
                SUM(rb.MONEY) as total_fee
            FROM report_besiness_data rbd
            INNER JOIN sys_user u ON rbd.USER_ID = u.ID
            LEFT JOIN report_basiness rb ON rb.DATA_ID = rbd.ID AND rb.YN = 1
            WHERE rbd.YN = 1 
                AND rbd.ACT_NODE_KEY = '20'
                AND rbd.FLOW_KEY = 'REPORT_FEE'
                <if test="year != null and year != ''">
                    AND rbd.YEAR = #{year}
                </if>
                <if test="quarter != null and quarter != ''">
                    AND rbd.QUARTER = #{quarter}
                </if>
                <if test="month != null and month != ''">
                    AND rbd.MONTH = #{month}
                </if>
            GROUP BY area_group
        ) fee_data ON fee_data.area_group = ra.CODE
        LEFT JOIN (
            SELECT 
                CASE 
                    WHEN u.AREA_CODE = '330000' THEN '330000'
                    WHEN u.AREA_CODE LIKE '33%' AND LENGTH(u.AREA_CODE) >= 4 THEN CONCAT(SUBSTRING(u.AREA_CODE, 1, 4), '00')
                    ELSE u.AREA_CODE
                END as area_group,
                SUM(rb.MONEY) as total_policy
            FROM report_besiness_data rbd
            INNER JOIN sys_user u ON rbd.USER_ID = u.ID
            LEFT JOIN report_basiness rb ON rb.DATA_ID = rbd.ID AND rb.YN = 1
            WHERE rbd.YN = 1 
                AND rbd.ACT_NODE_KEY = '20'
                AND rbd.FLOW_KEY = 'REPORT_FEE_POLICY'
                <if test="year != null and year != ''">
                    AND rbd.YEAR = #{year}
                </if>
                <if test="quarter != null and quarter != ''">
                    AND rbd.QUARTER = #{quarter}
                </if>
                <if test="month != null and month != ''">
                    AND rbd.MONTH = #{month}
                </if>
            GROUP BY area_group
        ) policy_data ON policy_data.area_group = ra.CODE
        WHERE ra.YN = 1
            AND ra.CODE LIKE '33%'
            AND (ra.LEVEL = 0 OR ra.LEVEL = 1)
        GROUP BY ra.CODE, ra.NAME, ra.SORT
        ORDER BY ra.SORT, ra.CODE
    </select>

    <!-- 查询具体市下各机构的详细数据 -->
    <select id="getReportSummaryByOrg" parameterType="com.jh.finance.bean.report.vo.ReportBesinessDataVo" resultType="com.jh.finance.bean.report.vo.ReportSummaryVo">
        SELECT
            u.AREA_CODE as areaCode,
            ra.NAME as areaName,
            u.NICKNAME as orgName,
            u.ID as userId,
            COALESCE(SUM(CASE WHEN rbd.FLOW_KEY = 'REPORT_FEE' THEN rb.MONEY END), 0) as feeTotal,
            COALESCE(SUM(CASE WHEN rbd.FLOW_KEY = 'REPORT_FEE_POLICY' THEN rb.MONEY END), 0) as policyTotal
        FROM sys_user u
        LEFT JOIN report_area ra ON ra.CODE = u.AREA_CODE AND ra.YN = 1
        LEFT JOIN report_besiness_data rbd ON rbd.USER_ID = u.ID
            AND rbd.YN = 1
            AND rbd.ACT_NODE_KEY = '20'
            AND rbd.FLOW_KEY IN ('REPORT_FEE', 'REPORT_FEE_POLICY')
            <if test="year != null and year != ''">
                AND rbd.YEAR = #{year}
            </if>
            <if test="quarter != null and quarter != ''">
                AND rbd.QUARTER = #{quarter}
            </if>
            <if test="month != null and month != ''">
                AND rbd.MONTH = #{month}
            </if>
        LEFT JOIN report_basiness rb ON rb.DATA_ID = rbd.ID AND rb.YN = 1
        WHERE u.YN = 1
            AND u.NICKNAME IS NOT NULL
            AND u.NICKNAME != ''
            <if test="areaCode != null and areaCode != ''">
                <choose>
                    <when test="areaCode.length() == 4">
                        AND u.AREA_CODE LIKE CONCAT(#{areaCode}, '%')
                    </when>
                    <when test="areaCode.length() == 6">
                        AND u.AREA_CODE LIKE CONCAT(SUBSTRING(#{areaCode}, 1, 4), '%')
                    </when>
                    <otherwise>
                        AND u.AREA_CODE LIKE CONCAT(#{areaCode}, '%')
                    </otherwise>
                </choose>
            </if>
        GROUP BY u.ID, u.AREA_CODE, ra.NAME, u.NICKNAME
        ORDER BY u.AREA_CODE, u.NICKNAME
    </select>

    <select id="queryToDo" resultType="com.jh.finance.bean.report.ReportBesinessData">
        SELECT DISTINCT t.*
        FROM report_besiness_data t
        INNER JOIN sys_user u ON t.USER_ID = u.ID
        <where>
            t.YN = 1
            <!-- 机构角色只能查看自己的数据 -->
            <if test="hasOrgRole == true and hasRegionRole != true and hasFinanceRole != true and hasScienceRole != true">
                AND t.USER_ID = #{userId} AND t.ACT_NODE_KEY = #{actNodeKey}
            </if>
            <!-- 地市角色可以查看下属区县的数据 -->
            <if test="hasRegionRole == true and areaCode != null">
                AND u.AREA_CODE LIKE CONCAT(#{areaCode}, '%')
                AND t.ACT_NODE_KEY = #{actNodeKey}
            </if>
            AND t.TYPE_ID = #{typeId}
            AND t.FLOW_KEY = #{flowKey}
        </where>
    </select>


</mapper>