<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jh.finance.dao.report.ReportPlanMapper">

    <!-- 根据条件查询计划管理数据 -->
    <select id="findByCondition" parameterType="com.jh.finance.bean.report.vo.ReportPlanVo" resultType="com.jh.finance.bean.report.ReportPlan">
        SELECT
        *
        FROM report_plan
        <where>
            yn = 1
            <if test="vo.cityCode != null and vo.cityCode != ''">
                AND city_code = #{vo.cityCode}
            </if>
            <if test="vo.cityName != null and vo.cityName != ''">
                AND city_name LIKE CONCAT('%', #{vo.cityName}, '%')
            </if>
            <if test="vo.planTime != null">
                AND DATE_FORMAT(plan_time, '%Y-%m') = DATE_FORMAT(#{vo.planTime}, '%Y-%m')
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

</mapper>