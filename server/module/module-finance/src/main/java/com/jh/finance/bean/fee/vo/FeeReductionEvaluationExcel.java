package com.jh.finance.bean.fee.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 减费惠企改革-一指评价对象 fee_reduction_evaluation
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
public class FeeReductionEvaluationExcel implements Serializable {

    private static final long serialVersionUID = 1L;
    @ExcelProperty(value = "机构名称", index = 0)
    private String orgName;
    @ExcelProperty(value = "地区名称", index = 1)
    private String areaName;
    @ExcelProperty(value = "地区CODE", index = 2)
    private String areaCode;
    @ExcelProperty(value = "检测设备", index = 3)
    private String detectionEquipment;
    @ExcelProperty(value = "台件数", index = 4)
    private Long equipmentCount;
    @ExcelProperty(value = "行业名称", index = 5)
    private String industryName;
    @ExcelProperty(value = "政策名称", index = 6)
    private String policyName;
    @ExcelProperty(value = "减免费用", index = 7)
    private BigDecimal reductionAmount;
    @ExcelProperty(value = "享受时间", index = 8)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date enjoyTime;
    @ExcelProperty(value = "满意度", index = 9)
    private String satisfaction;
    /**
     * 导入情况
     */
    @ExcelProperty(value = "导入情况", index = 10)
    private String importSituation;
}
