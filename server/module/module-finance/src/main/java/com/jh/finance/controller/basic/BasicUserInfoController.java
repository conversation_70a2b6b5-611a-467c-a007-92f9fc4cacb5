package com.jh.finance.controller.basic;

import java.util.List;

import com.jh.finance.bean.basic.BasicUserInfo;
import com.jh.finance.bean.basic.vo.BasicUserInfoReq;
import com.jh.finance.bean.basic.vo.BasicUserInfoVo;
import com.jh.finance.service.basic.BasicUserInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.github.pagehelper.PageInfo;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.controller.BaseController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 用户平台信息Controller
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@RestController
@RequestMapping("/basic/userInfo")
@Tag(name = "用户平台信息")
public class BasicUserInfoController extends BaseController {
    @Autowired
    private BasicUserInfoService basicUserInfoService;

    private static final String PER_PREFIX = "btn:basic:userInfo:";

    /**
     * 新增用户平台信息
     *
     * @param basicUserInfo 用户平台信息数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/saveOrupdate")
    @Operation(summary = "新增用户平台信息")
    @SystemLogAnnotation(type = "用户平台信息", value = "新增用户平台信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveBasicUserInfo(@RequestBody BasicUserInfo basicUserInfo) {
        String id = basicUserInfoService.saveOrUpdateBasicUserInfo(basicUserInfo);
        return RestApiResponse.ok(id);
    }

    /**
     * 批量删除用户平台信息(判断 关联数据是否可以删除)
     *
     * @param ids
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @Operation(summary = "批量删除用户平台信息")
    @SystemLogAnnotation(type = "用户平台信息", value = "批量删除用户平台信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteBasicUserInfo(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        basicUserInfoService.deleteBasicUserInfo(ids);
        return RestApiResponse.ok();
    }

    /**
     * 分页查询用户平台信息
     *
     * @param basicUserInfoVo 用户平台信息 查询条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/findPageByQuery")
    @Operation(summary = "分页查询用户平台信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody BasicUserInfoVo basicUserInfoVo) {
        PageInfo<BasicUserInfo> basicUserInfo = basicUserInfoService.findPageByQuery(basicUserInfoVo);
        return RestApiResponse.ok(basicUserInfo);
    }

    /**
     * 验证用户平台APPID和APPKEY
     *
     * @param appId    平台ID
     * @param appKey   平台KEY
     * @param response HTTP响应对象
     * @return void 如果验证成功，直接返回token到response；如果失败，抛出异常
     * <AUTHOR>
     */
    @GetMapping("/validateAppIdAndKey")
    @Operation(summary = "验证用户平台APPID和APPKEY")
    public RestApiResponse<?> validateAppIdAndKey(@RequestParam("appId") String appId, @RequestParam("appKey") String appKey, HttpServletResponse response) {
        BasicUserInfoReq req = basicUserInfoService.validateAppIdAndKey(appId, appKey, response);
        return RestApiResponse.ok(req);
    }
}
