package com.jh.finance.dao.report;

import com.jh.common.mybatis.BaseInfoMapper;
import com.jh.finance.bean.report.ReportBasiness;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 收费统计表Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Mapper
public interface ReportBasinessMapper extends BaseInfoMapper<ReportBasiness> {
    
    /**
     * 根据主表ID查询收费统计数据
     * @param dataId 主表ID
     * @return List<ReportBasiness>
     */
    List<ReportBasiness> findByDataId(@Param("dataId") String dataId);
} 