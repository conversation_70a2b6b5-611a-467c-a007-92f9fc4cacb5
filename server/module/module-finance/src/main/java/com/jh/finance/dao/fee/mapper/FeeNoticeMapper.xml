<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.finance.dao.fee.FeeNoticeMapper" >

    <!-- 分页查询收费公示 -->
    <select id="findPageByQuery" parameterType="com.jh.finance.bean.fee.vo.FeeNoticeVo" resultType="com.jh.finance.bean.fee.FeeNotice">
        SELECT *
        FROM fee_notice
        <where>
            YN = 1
            
            <!-- 按用户ID查询 -->
            <if test="userId != null and userId != ''">
                AND USER_ID = #{userId}
            </if>
            
            <!-- 按标题模糊查询 -->
            <if test="title != null and title != ''">
                AND TITLE LIKE CONCAT('%', #{title}, '%')
            </if>
            
            <!-- 按发布状态查询 -->
            <if test="isPublish != null">
                AND IS_PUBLISH = #{isPublish}
            </if>
            
            <!-- 按发布日期范围查询 -->
            <if test="publishDateStart != null and publishDateStart != ''">
                AND PUBLISH_DATE &gt;= #{publishDateStart}
            </if>
            <if test="publishDateEnd != null and publishDateEnd != ''">
                AND PUBLISH_DATE &lt;= #{publishDateEnd}
            </if>
        </where>
        ORDER BY 
        <if test="isPublish != null and isPublish == 1">
            PUBLISH_DATE DESC,
        </if>
        CREATE_TIME DESC
    </select>
</mapper> 