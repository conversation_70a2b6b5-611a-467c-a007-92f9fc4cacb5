<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.finance.dao.report.ReportDocumentFileMapper">

    <resultMap id="ReportDocumentFileResult" type="com.jh.finance.bean.report.ReportDocumentFile">
        <result property="id" column="ID"/>
        <result property="documentId" column="DOCUMENT_ID"/>
        <result property="fileName" column="FILE_NAME"/>
        <result property="fileUrl" column="FILE_URL"/>
        <result property="filePath" column="FILE_PATH"/>
        <result property="sortOrder" column="SORT_ORDER"/>
        <result property="yn" column="YN"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="createUserNickname" column="CREATE_USER_NICKNAME"/>
        <result property="updateUserNickname" column="UPDATE_USER_NICKNAME"/>
        <result property="attr1" column="ATTR1"/>
        <result property="attr2" column="ATTR2"/>
        <result property="attr3" column="ATTR3"/>
        <result property="attr4" column="ATTR4"/>
        <result property="attr5" column="ATTR5"/>
    </resultMap>

    <sql id="selectReportDocumentFileVo">
        select *
        from REPORT_DOCUMENT_FILE
    </sql>

    <select id="selectByDocumentId" parameterType="String" resultMap="ReportDocumentFileResult">
        <include refid="selectReportDocumentFileVo"/>
        where DOCUMENT_ID = #{documentId} and YN = 1
        order by SORT_ORDER asc
    </select>

    <delete id="deleteByDocumentId" parameterType="String">
        update REPORT_DOCUMENT_FILE
        set YN = 0
        where DOCUMENT_ID = #{documentId}
    </delete>

</mapper>