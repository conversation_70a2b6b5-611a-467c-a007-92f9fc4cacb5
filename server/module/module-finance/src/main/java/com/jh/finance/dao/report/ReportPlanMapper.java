package com.jh.finance.dao.report;

import com.jh.common.mybatis.BaseInfoMapper;
import com.jh.finance.bean.report.ReportPlan;
import com.jh.finance.bean.report.vo.ReportPlanVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据上报-计划管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Mapper
public interface ReportPlanMapper extends BaseInfoMapper<ReportPlan> {

    /**
     * 根据条件查询计划管理数据
     *
     * @param reportPlanVo 查询条件
     * @return 计划管理数据列表
     */
    List<ReportPlan> findByCondition(@Param("vo") ReportPlanVo reportPlanVo);

}