package com.jh.finance.bean.report.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 数据上报情况查询结果VO
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Schema(description = "数据上报情况查询结果VO")
@Data
public class ReportSummaryVo {

    @Schema(description = "地区代码")
    private String areaCode;

    @Schema(description = "地区名称")
    private String areaName;

    @Schema(description = "机构名称（当查询具体市下机构时使用）")
    private String orgName;

    @Schema(description = "收费统计总金额")
    private BigDecimal feeTotal = BigDecimal.ZERO;

    @Schema(description = "消费减负总金额")
    private BigDecimal policyTotal = BigDecimal.ZERO;

    @Schema(description = "用户ID（当查询具体市下机构时使用）")
    private String userId;

    @Schema(description = "排序")
    private Long sort;

    public ReportSummaryVo() {
    }

    public ReportSummaryVo(String areaCode, String areaName, BigDecimal feeTotal, BigDecimal policyTotal) {
        this.areaCode = areaCode;
        this.areaName = areaName;
        this.feeTotal = feeTotal != null ? feeTotal : BigDecimal.ZERO;
        this.policyTotal = policyTotal != null ? policyTotal : BigDecimal.ZERO;
    }

    public ReportSummaryVo(String areaCode, String areaName, String orgName, String userId, BigDecimal feeTotal, BigDecimal policyTotal) {
        this.areaCode = areaCode;
        this.areaName = areaName;
        this.orgName = orgName;
        this.userId = userId;
        this.feeTotal = feeTotal != null ? feeTotal : BigDecimal.ZERO;
        this.policyTotal = policyTotal != null ? policyTotal : BigDecimal.ZERO;
    }
}