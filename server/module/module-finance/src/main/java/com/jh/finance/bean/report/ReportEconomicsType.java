package com.jh.finance.bean.report;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.jh.common.bean.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

/**
 * 行业类型对象 report_economics_type
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Table(name = "report_economics_type")
@Schema(description = "行业类型")
@Data
public class ReportEconomicsType extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "行业类型代码")
    private String code;
    @Column(name = "NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "行业类型名称")
    private String name;
    @Column(name = "CLASSLEVEL")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "级别 1行业门类 2行业大类 3行业中类 4行业小类")
    private Integer classlevel;
    @Column(name = "PARENT_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "父级代码")
    private String parentCode;
    @Column(name = "SORT")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "排序")
    private Integer sort;

}
