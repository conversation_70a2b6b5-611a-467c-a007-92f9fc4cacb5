package com.jh.finance.bean.fee.vo;


import com.jh.finance.bean.fee.FeeNonStandard;
import io.swagger.v3.oas.annotations.media.Schema;


/**
 * 非标类项目Vo
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Schema(description = "FeeNonStandardVo")
public class FeeNonStandardVo extends FeeNonStandard {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}