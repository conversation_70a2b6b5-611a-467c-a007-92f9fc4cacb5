package com.jh.finance.service.report.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.common.bean.LoginUser;
import com.jh.common.bean.SysRole;
import com.jh.common.bean.SysUser;
import com.jh.common.constant.CommonConstant;
import com.jh.common.exception.ServiceException;
import com.jh.common.util.AppUserUtil;

import com.jh.common.util.security.UUIDUtils;
import com.jh.finance.bean.report.ReportPlan;
import com.jh.finance.bean.report.vo.ReportPlanVo;
import com.jh.finance.constant.RoleConstant;
import com.jh.finance.dao.report.ReportPlanMapper;
import com.jh.finance.service.report.ReportPlanService;
import com.jh.sys.dao.SysUserMapper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import java.util.List;

/**
 * 数据上报-计划管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class ReportPlanServiceImpl implements ReportPlanService {
    private static final Logger logger = LoggerFactory.getLogger(ReportPlanServiceImpl.class);

    @Autowired
    private ReportPlanMapper reportPlanMapper;

    /**
     * 保存或更新计划管理数据
     *
     * @param reportPlan 计划管理数据对象
     * @return String 计划管理数据ID
     * <AUTHOR>
     */
    @Override
    @Transactional(readOnly = false)
    public String saveOrUpdateReportPlan(ReportPlan reportPlan) {
        if (StringUtils.isEmpty(reportPlan.getId())) {
            // 新增
            reportPlan.setId(UUIDUtils.getUUID());
            reportPlan.setYn(CommonConstant.FLAG_YES);
            reportPlanMapper.insertSelective(reportPlan);
        } else {
            // 更新
            reportPlanMapper.updateByPrimaryKeySelective(reportPlan);
        }
        return reportPlan.getId();
    }

    /**
     * 删除计划管理数据
     *
     * @param ids void 计划管理数据ID
     * <AUTHOR>
     */
    @Override
    @Transactional(readOnly = false)
    public void deleteReportPlan(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException("参数异常");
        }
        for (String id : ids) {
            ReportPlan reportPlan = new ReportPlan();
            reportPlan.setId(id);
            reportPlan.setYn(CommonConstant.FLAG_NO);
            reportPlanMapper.updateByPrimaryKeySelective(reportPlan);
        }
    }

    /**
     * 查询计划管理数据详情
     *
     * @param id
     * @return ReportPlan
     * <AUTHOR>
     */
    @Override
    public ReportPlan findById(String id) {
        return reportPlanMapper.selectByPrimaryKey(id);
    }

    /**
     * 分页查询计划管理数据
     *
     * @param reportPlanVo
     * @return PageInfo<ReportPlan>
     * <AUTHOR>
     */
    @Override
    public PageInfo<ReportPlan> findPageByQuery(ReportPlanVo reportPlanVo) {
        PageHelper.startPage(reportPlanVo.getPageNum(), reportPlanVo.getPageSize());
        List<ReportPlan> reportPlanList = reportPlanMapper.findByCondition(reportPlanVo);
        return new PageInfo<ReportPlan>(reportPlanList);
    }
}