package com.jh.finance.service.fee.impl;


import com.jh.common.util.AppUserUtil;
import com.jh.finance.bean.fee.FeeReductionHalve;
import com.jh.finance.bean.fee.vo.FeeReductionHalveVo;
import com.jh.finance.bean.fee.vo.FeeReductionHalveDataPushRequest;
import com.jh.finance.bean.report.ReportArea;
import com.jh.finance.bean.report.ReportEconomicsType;
import com.jh.finance.dao.fee.FeeReductionHalveMapper;
import com.jh.finance.dao.report.ReportAreaMapper;
import com.jh.finance.dao.report.ReportEconomicsTypeMapper;
import com.jh.finance.service.fee.FeeReductionHalveService;
import com.jh.finance.service.basic.BasicUserInfoService;
import com.jh.common.bean.SysUser;

import java.util.Date;
import java.util.List;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.regex.Pattern;
import java.math.BigDecimal;

import org.springframework.beans.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;

import com.jh.common.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import static com.github.pagehelper.page.PageMethod.orderBy;

/**
 * 减半收取Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Service
@Transactional(readOnly = true)
public class FeeReductionHalveServiceImpl extends BaseServiceImpl<FeeReductionHalveMapper, FeeReductionHalve> implements FeeReductionHalveService {

    private static final Logger logger = LoggerFactory.getLogger(FeeReductionHalveServiceImpl.class);
    @Autowired
    private FeeReductionHalveMapper feeReductionHalveMapper;

    @Autowired
    private ReportAreaMapper reportAreaMapper;

    @Autowired
    private ReportEconomicsTypeMapper reportEconomicsTypeMapper;

    @Autowired
    private BasicUserInfoService basicUserInfoService;


    @Override
    @Transactional(readOnly = false)
    /**
     * 保存或更新减半收取
     *@param feeReductionHalve 减半收取对象
     *@return String 减半收取ID
     *<AUTHOR>
     */
    public String saveOrUpdateFeeReductionHalve(FeeReductionHalve feeReductionHalve) {
        if (feeReductionHalve == null) {
            throw new ServiceException("数据异常");
        }

        if (StringUtils.isEmpty(feeReductionHalve.getId())) {
            //新增
            feeReductionHalve.setId(UUIDUtils.getUUID());
            feeReductionHalveMapper.insertSelective(feeReductionHalve);
        } else {
            //TODO 做判断后方能执行修改 一般判断是否是自己的数据
            //避免页面传入修改
            feeReductionHalve.setYn(null);
            feeReductionHalveMapper.updateByPrimaryKeySelective(feeReductionHalve);
        }
        return feeReductionHalve.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     * 删除减半收取
     *@param ids void 减半收取ID
     *<AUTHOR>
     */
    public void deleteFeeReductionHalve(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除 一般判断是否是自己的数据
            FeeReductionHalve feeReductionHalve = feeReductionHalveMapper.selectByPrimaryKey(id);
            if (feeReductionHalve == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            FeeReductionHalve temfeeReductionHalve = new FeeReductionHalve();
            temfeeReductionHalve.setYn(CommonConstant.FLAG_NO);
            temfeeReductionHalve.setId(feeReductionHalve.getId());
            feeReductionHalveMapper.updateByPrimaryKeySelective(temfeeReductionHalve);
        }
    }

    /**
     * 查询减半收取详情
     *
     * @param id
     * @return FeeReductionHalve
     * <AUTHOR>
     */
    @Override
    public FeeReductionHalve findById(String id) {
        // TODO 做判断后方能反回数据 一般判断是否是自己的数据
        return feeReductionHalveMapper.selectByPrimaryKey(id);
    }


    /**
     * 分页查询减半收取
     *
     * @param feeReductionHalveVo
     * @return PageInfo<FeeReductionHalve>
     * <AUTHOR>
     */
    @Override
    public PageInfo<FeeReductionHalve> findPageByQuery(FeeReductionHalveVo feeReductionHalveVo) {
        // TODO 做判断后方能执行查询 一般判断是否是自己的数据
        PageHelper.startPage(feeReductionHalveVo.getPageNum(), feeReductionHalveVo.getPageSize());
        orderBy(feeReductionHalveVo.getOrderColumn() + " " + feeReductionHalveVo.getOrderValue());
        Example example = getExample(feeReductionHalveVo);
        List<FeeReductionHalve> feeReductionHalveList = feeReductionHalveMapper.selectByExample(example);
        return new PageInfo<FeeReductionHalve>(feeReductionHalveList);
    }

    private Example getExample(FeeReductionHalveVo feeReductionHalveVo) {
        Example example = new Example(FeeReductionHalve.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        criteria.andEqualTo("orgCode", AppUserUtil.getCurrentUserName());
        example.orderBy("createTime").desc();
        return example;
    }

    /**
     * 按条件导出查询减半收取
     *
     * @param feeReductionHalveVo
     * @return PageInfo<FeeReductionHalve>
     * <AUTHOR>
     */
    @Override
    public List<FeeReductionHalve> findByQuery(FeeReductionHalveVo feeReductionHalveVo) {
        orderBy(feeReductionHalveVo.getOrderColumn() + " " + feeReductionHalveVo.getOrderValue());
        Example example = getExample(feeReductionHalveVo);
        return feeReductionHalveMapper.selectByExample(example);
    }

    /**
     * 处理外部推送的减半收取数据
     *
     * @param request 推送的减半收取数据
     * @return String 处理结果消息
     * <AUTHOR>
     */
    @Override
    @Transactional(readOnly = false)
    public String processPushData(FeeReductionHalveDataPushRequest request) {
        logger.info("开始处理减半收取数据推送，数据：{}", request);

        // 验证请求参数
        if (request == null) {
            throw new ServiceException("推送数据不能为空");
        }

        if (StringUtils.isEmpty(request.getToken())) {
            throw new ServiceException("Token不能为空");
        }

        // 验证Token并获取用户信息
        SysUser user = basicUserInfoService.validateTokenAndGetUser(request.getToken());
        if (user == null) {
            throw new ServiceException("用户信息验证不通过");
        }

        // 创建实体对象并复制属性
        FeeReductionHalve feeReductionHalve = new FeeReductionHalve();
        BeanUtils.copyProperties(request, feeReductionHalve);

        // 处理特殊字段映射
        if (request.getProductCount() != null) {
            feeReductionHalve.setNum(request.getProductCount());
        }

        try {
            // 1. 数据校验
            StringBuilder errorMsg = validatePushData(request);
            if (!errorMsg.isEmpty()) {
                throw new ServiceException(errorMsg.toString());
            }
            // 2. 处理数据 - 根据UniqueCode判断是新增还是更新
            Example example = new Example(FeeReductionHalve.class);
            example.createCriteria()
                    .andEqualTo("uniqueCode", feeReductionHalve.getUniqueCode())
                    .andEqualTo("yn", CommonConstant.FLAG_YES);
            FeeReductionHalve existing = feeReductionHalveMapper.selectOneByExample(example);

            if (existing != null) {
                // 数据已存在，进行更新
                BeanUtils.copyProperties(feeReductionHalve, existing, "id", "createTime", "yn");
                existing.setUpdateTime(new Date());
                existing.setIsImport(3); // 标记为推送导入
                // 设置更新用户信息
                existing.setUpdateUser(user.getUsername());
                existing.setUpdateUserNickname(user.getNickname());

                int updateResult = feeReductionHalveMapper.updateByPrimaryKeySelective(existing);
                if (updateResult <= 0) {
                    throw new ServiceException("更新数据失败，唯一标识：" + feeReductionHalve.getUniqueCode());
                }
            } else {
                // 数据不存在，进行新增
                feeReductionHalve.setId(UUIDUtils.getUUID());
                feeReductionHalve.setCreateTime(new Date());
                feeReductionHalve.setUpdateTime(new Date());
                feeReductionHalve.setYn(CommonConstant.FLAG_YES);
                feeReductionHalve.setIsImport(3); // 标记为推送导入
                // 设置创建用户信息
                feeReductionHalve.setCreateUser(user.getUsername());
                feeReductionHalve.setCreateUserNickname(user.getNickname());

                int insertResult = feeReductionHalveMapper.insertSelective(feeReductionHalve);
                if (insertResult <= 0) {
                    throw new ServiceException("插入数据失败，唯一标识：" + feeReductionHalve.getUniqueCode());
                }
            }

            logger.info("减半收取数据推送处理完成，结果：成功：{}", request);
            return "数据处理成功";
        } catch (Exception e) {
            logger.error("处理减半收取数据推送失败", e);
            throw new ServiceException("处理推送数据失败：" + e.getMessage());
        }
    }

    /**
     * 验证推送数据
     */
    private StringBuilder validatePushData(FeeReductionHalveDataPushRequest request) {
        StringBuilder errorMsg = new StringBuilder();

        if (StringUtils.isEmpty(request.getUniqueCode())) {
            errorMsg.append("唯一标识不能为空;");
        }
        if (StringUtils.isEmpty(request.getCompany())) {
            errorMsg.append("企业名称不能为空;");
        }
        if (StringUtils.isEmpty(request.getUscc())) {
            errorMsg.append("统一社会信用代码不能为空;");
        }
        if (StringUtils.isEmpty(request.getProductName())) {
            errorMsg.append("产品或设备名称不能为空;");
        }
        if (request.getEnjoyTime() == null) {
            errorMsg.append("享受时间不能为空;");
        }
        if (StringUtils.isEmpty(request.getCountryCode())) {
            errorMsg.append("区县代码不能为空;");
        }
        if (StringUtils.isEmpty(request.getIndustryCategoryCode())) {
            errorMsg.append("行业门类代码不能为空;");
        }
        if (StringUtils.isEmpty(request.getIndustryCode())) {
            errorMsg.append("行业大类代码不能为空;");
        }
        if (StringUtils.isEmpty(request.getPolicyId())) {
            errorMsg.append("政策ID不能为空;");
        }

        // 验证行政区划信息
        if (StringUtils.isNotEmpty(request.getCountryCode())) {
            Example areaExample = new Example(ReportArea.class);
            areaExample.createCriteria().andEqualTo("code", request.getCountryCode()).andEqualTo("yn", CommonConstant.FLAG_YES);
            ReportArea countryArea = reportAreaMapper.selectOneByExample(areaExample);

            if (countryArea == null) {
                errorMsg.append("库中无此区县代码：").append(request.getCountryCode()).append(";");
            }
        }

        // 验证行业类型信息
        if (StringUtils.isNotEmpty(request.getIndustryCategoryCode())) {
            Example categoryExample = new Example(ReportEconomicsType.class);
            categoryExample.createCriteria()
                    .andEqualTo("code", request.getIndustryCategoryCode())
                    .andEqualTo("classlevel", 1)
                    .andEqualTo("yn", CommonConstant.FLAG_YES);
            ReportEconomicsType categoryType = reportEconomicsTypeMapper.selectOneByExample(categoryExample);

            if (categoryType == null) {
                errorMsg.append("库中无此行业门类代码：").append(request.getIndustryCategoryCode()).append(";");
            }
        }

        if (StringUtils.isNotEmpty(request.getIndustryCategoryCode()) && StringUtils.isNotEmpty(request.getIndustryCode())) {
            String fullIndustryCode = request.getIndustryCategoryCode() + request.getIndustryCode();
            Example industryExample = new Example(ReportEconomicsType.class);
            industryExample.createCriteria()
                    .andEqualTo("code", fullIndustryCode)
                    .andEqualTo("classlevel", 2L)
                    .andEqualTo("yn", CommonConstant.FLAG_YES);
            ReportEconomicsType industryType = reportEconomicsTypeMapper.selectOneByExample(industryExample);

            if (industryType == null) {
                errorMsg.append("库中无此行业大类代码：").append(fullIndustryCode).append(";");
            }
        }

        return errorMsg;
    }

}
