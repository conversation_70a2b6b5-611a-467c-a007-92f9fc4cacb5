package com.jh.finance.service.home.impl;
import com.jh.constant.FlowConstant;
import com.jh.finance.bean.home.HomePageVo;
import com.jh.finance.bean.home.HomeVo;
import com.jh.finance.bean.report.vo.ReportDocumentVo;
import com.jh.finance.bean.report.vo.ReportReduceVo;
import com.jh.finance.dao.report.ReportBesinessDataMapper;
import com.jh.finance.dao.report.ReportDocumentMapper;
import com.jh.finance.dao.report.ReportReduceMapper;
import com.jh.finance.enums.FeeReportStatusEnum;
import com.jh.finance.service.home.HomePageService;
import com.jh.sys.bean.vo.SysMenuVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jh.finance.enums.TodoModuleEnum;
import com.jh.sys.service.MenuService;
import com.jh.sys.bean.vo.RouterVo;
import java.util.*;
import java.util.stream.Collectors;
import com.jh.finance.bean.report.ReportBusinessType;
import com.jh.finance.dao.report.ReportBusinessTypeMapper;
import com.jh.finance.bean.report.vo.ReportBesinessDataVo;
import com.jh.common.bean.SysRole;
import com.jh.common.bean.SysUser;
import com.jh.sys.dao.SysUserMapper;
import com.jh.finance.constant.RoleConstant;
import com.jh.common.util.AppUserUtil;
import static com.jh.common.controller.BaseController.isAdmin;
import static com.jh.common.controller.BaseController.getUserRoleList;

@Service
public class HomePageServiceImpl implements HomePageService {
    public static final Logger logger = LoggerFactory.getLogger(HomePageServiceImpl.class);

    @Autowired
    private MenuService menuService;

    @Autowired
    private ReportBusinessTypeMapper reportBusinessTypeMapper;

    @Autowired
    private ReportBesinessDataMapper reportBesinessDataMapper;

    @Autowired
    private ReportReduceMapper reportReduceMapper;

    @Autowired
    private ReportDocumentMapper reportDocumentMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 获取首页待办事项列表
     *
     * @param vo 查询参数
     * @return 待办事项列表
     */
    @Override
    public List<HomePageVo> getHomePageMatter(HomeVo vo) {
        String userId = vo.getOperUserId();
        SysMenuVo menuVo = new SysMenuVo();
        menuVo.setAdmin(isAdmin());
        List<SysMenuVo> menus = menuService.selectMenuList(menuVo, userId);
        List<SysMenuVo> menusTree = buildMenuTree(menus);
        List<RouterVo> routers = menuService.buildMenus(menusTree);
        routers = menuService.filterRoutersByOrgType(routers, userId);
        Set<String> permittedNames = new HashSet<>();
        collectPermittedNames(routers, permittedNames);

        // 角色列表和用户ID
        List<SysRole> roleList = getUserRoleList();
        String currentUserId = AppUserUtil.getCurrentUserId();

        // 所有业务类型ID
        List<ReportBusinessType> types = reportBusinessTypeMapper.selectAll();
        Map<String, String> typeNameToId = new HashMap<>();
        for (ReportBusinessType type : types) {
            typeNameToId.put(type.getName(), type.getId());
        }

        // 计算角色标志
        boolean hasOrgRole = false;
        boolean hasRegionRole = false;
        boolean hasAdminRole = false;
        boolean hasFinanceRole = false;
        boolean hasScienceRole = false;
        if (roleList != null && !roleList.isEmpty()) {
            for (SysRole role : roleList) {
                String roleCode = role.getRoleCode();
                if (RoleConstant.ROLE_ORG.equals(roleCode)) hasOrgRole = true;
                else if (RoleConstant.ROLE_REGION.equals(roleCode)) hasRegionRole = true;
                else if (RoleConstant.ROLE_ADMIN.equals(roleCode) || RoleConstant.ROLE_SUPERADMIN.equals(roleCode)) hasAdminRole = true;
                else if (RoleConstant.ROLE_FINANCE.equals(roleCode)) hasFinanceRole = true;
                else if (RoleConstant.ROLE_SCIENCE.equals(roleCode)) hasScienceRole = true;
            }
        }

        // 获取areaCode
        String areaCode = null;
        if (hasRegionRole || hasFinanceRole || hasScienceRole) {
            SysUser user = new SysUser();
            user.setId(currentUserId);
            SysUser currentUser = sysUserMapper.selectOne(user);
            if (currentUser != null && currentUser.getAreaCode() != null) {
                areaCode = currentUser.getAreaCode();
                if (areaCode.endsWith("00")) {
                    areaCode = areaCode.substring(0, areaCode.length() - 2);
                }
            }
        }

        List<HomePageVo> list = new ArrayList<>();
        for (TodoModuleEnum module : TodoModuleEnum.values()) {
            if (permittedNames.contains(module.getName())) {
                ReportBesinessDataVo queryVo = new ReportBesinessDataVo();
                queryVo.setUserId(currentUserId);
                queryVo.setHasOrgRole(hasOrgRole);
                queryVo.setHasRegionRole(hasRegionRole);
                queryVo.setHasAdminRole(hasAdminRole);
                queryVo.setHasFinanceRole(hasFinanceRole);
                queryVo.setHasScienceRole(hasScienceRole);
                queryVo.setAreaCode(areaCode);

                String typeIdStr = typeNameToId.get(module.getTypeName());
                if (typeIdStr != null) {
                    queryVo.setTypeId(Integer.valueOf(typeIdStr));
                }

                int count = getTodoCount(module, queryVo, hasOrgRole, hasRegionRole, hasFinanceRole);
                if (count > 0) {
                    HomePageVo record = new HomePageVo();
                    record.setMenuName(module.getDisplayName());
                    record.setPath(module.getPath());
                    record.setComponent(module.getComponent());
                    record.setCount(count);
                    record.setStatusDesc(getStatusDesc(module, hasOrgRole, hasRegionRole, hasFinanceRole));
                    list.add(record);
                }
            }
        }
        return list;
    }


    /**
     * 获取待办数量
     *
     * @param module 模块枚举
     * @param queryVo 查询参数
     * @param hasOrgRole 是否有机构角色
     * @param hasRegionRole 是否有地市角色
     * @param hasFinanceRole 是否有财务处角色
     * @return 待办数量
     */
    private int getTodoCount(TodoModuleEnum module, ReportBesinessDataVo queryVo, boolean hasOrgRole, boolean hasRegionRole, boolean hasFinanceRole) {
        if (!hasOrgRole && !hasRegionRole && !hasFinanceRole) {
            return 0;
        }

        String actNodeKey = null;
        if (module.name().contains("REDUCE")) {
            actNodeKey = hasOrgRole ? FeeReportStatusEnum.DTC.getCode() : (hasRegionRole ? FeeReportStatusEnum.DCL.getCode() : null);
        } else if (module == TodoModuleEnum.FILE_MANAGE) {
            actNodeKey = hasOrgRole ? FeeReportStatusEnum.DTC.getCode() : (hasFinanceRole ? FeeReportStatusEnum.DCL.getCode() : null);
        } else {
            actNodeKey = hasOrgRole ? FeeReportStatusEnum.DTC.getCode() : (hasRegionRole ? FeeReportStatusEnum.DCL.getCode() : null);
        }
        if (actNodeKey == null) return 0;
        queryVo.setActNodeKey(actNodeKey);

        ReportReduceVo queryReduceVo = new ReportReduceVo();
        BeanUtils.copyProperties(queryVo, queryReduceVo);

        ReportDocumentVo reportDocumentVo = new ReportDocumentVo();
        BeanUtils.copyProperties(queryVo, reportDocumentVo);

        if (module.name().contains("CHARGE")) {
            queryVo.setFlowKey(FlowConstant.REPORT_FEE_KEY);
            return reportBesinessDataMapper.queryToDo(queryVo).size();
        } else if (module.name().contains("LIGHTEN")) {
            queryVo.setFlowKey(FlowConstant.REPORT_FEE_POLICY_KEY);
            return reportBesinessDataMapper.queryToDo(queryVo).size();
        } else if (module.name().contains("REDUCE")) {
            return reportReduceMapper.queryToDo(queryReduceVo).size();
        } else if (module.name().contains("FILE")) {
            return reportDocumentMapper.queryToDo(reportDocumentVo).size();
        }
        return 0;
    }

    /**
     * 获取状态说明
     *
     * @param module 模块枚举
     * @param hasOrgRole 是否有机构角色
     * @param hasRegionRole 是否有地市角色
     * @param hasFinanceRole 是否有财务处角色
     * @return 状态说明
     */
    private String getStatusDesc(TodoModuleEnum module, boolean hasOrgRole, boolean hasRegionRole, boolean hasFinanceRole) {
        if (!hasOrgRole && !hasRegionRole && !hasFinanceRole) {
            return null;
        }

        if (module.name().contains("REDUCE")) {
            return hasOrgRole ? FeeReportStatusEnum.DTC.getValue() : (hasRegionRole ? FeeReportStatusEnum.DCL.getValue() : null);
        } else if (module == TodoModuleEnum.FILE_MANAGE) {
            return hasOrgRole ? FeeReportStatusEnum.DTC.getValue() : (hasFinanceRole ? FeeReportStatusEnum.DCL.getValue() : null);
        } else {
            return hasOrgRole ? FeeReportStatusEnum.DTC.getValue() : (hasRegionRole ? FeeReportStatusEnum.DCL.getValue() : null);
        }
    }

    /**
     * 构建菜单树
     *
     * @param menus 菜单列表
     * @return 菜单树
     */
    private List<SysMenuVo> buildMenuTree(List<SysMenuVo> menus) {
        List<SysMenuVo> returnList = new ArrayList<>();
        List<String> tempList = new ArrayList<>();
        for (SysMenuVo dept : menus) {
            tempList.add(dept.getId());
        }
        for (SysMenuVo res : menus) {
            if (!tempList.contains(res.getParentId()) || "0".equals(res.getParentId())) {
                recursionFn(menus, res);
                returnList.add(res);
            }
        }
        return returnList;
    }

    /**
     * 递归构建菜单子树
     *
     * @param list 菜单列表
     * @param parent 父菜单
     */
    private void recursionFn(List<SysMenuVo> list, SysMenuVo parent) {
        List<SysMenuVo> childList = list.stream().filter(p -> p.getParentId().equals(parent.getId())).collect(Collectors.toList());
        parent.setChildren(childList);
        if (childList.isEmpty()) {
            return;
        }
        for (SysMenuVo child : childList) {
            recursionFn(list, child);
        }
    }

    /**
     * 收集许可名称
     *
     * @param routers 路由列表
     * @param names 名称集合
     */
    private void collectPermittedNames(List<RouterVo> routers, Set<String> names) {
        if (routers == null) return;
        for (RouterVo router : routers) {
            names.add(router.getName());
            collectPermittedNames(router.getChildren(), names);
        }
    }
}
