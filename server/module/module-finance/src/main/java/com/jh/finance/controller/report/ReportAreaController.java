package com.jh.finance.controller.report;

import java.util.List;

import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.common.bean.LoginUser;
import com.jh.finance.bean.report.ReportArea;
import com.jh.finance.bean.report.vo.ReportAreaVo;
import com.jh.finance.service.report.ReportAreaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.jh.common.bean.RestApiResponse;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.controller.BaseController;

import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

/**
 * 浙江省行政区划Controller
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@RestController
@RequestMapping("/report/area")
@Tag(name = "浙江省行政区划")
public class ReportAreaController extends BaseController {
    @Autowired
    private ReportAreaService reportAreaService;

    private static final String PER_PREFIX = "btn:report:area:";

    /**
     * 新增浙江省行政区划
     *
     * @param reportArea 浙江省行政区划数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @Operation(summary = "新增浙江省行政区划")
    @SystemLogAnnotation(type = "浙江省行政区划", value = "新增浙江省行政区划")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveReportArea(@RequestBody ReportArea reportArea) {
        String id = reportAreaService.saveOrUpdateReportArea(reportArea);
        return RestApiResponse.ok(id);
    }

    /**
     * 修改浙江省行政区划
     *
     * @param reportArea 浙江省行政区划数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @Operation(summary = "修改浙江省行政区划")
    @SystemLogAnnotation(type = "浙江省行政区划", value = "修改浙江省行政区划")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateReportArea(@RequestBody ReportArea reportArea) {
        String id = reportAreaService.saveOrUpdateReportArea(reportArea);
        return RestApiResponse.ok(id);
    }

    /**
     * 批量删除浙江省行政区划(判断 关联数据是否可以删除)
     *
     * @param ids
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @Operation(summary = "批量删除浙江省行政区划")
    @SystemLogAnnotation(type = "浙江省行政区划", value = "批量删除浙江省行政区划")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteReportArea(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        reportAreaService.deleteReportArea(ids);
        return RestApiResponse.ok();
    }

    /**
     * 查询浙江省行政区划详情
     *
     * @param id
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findById")
    @Operation(summary = "查询浙江省行政区划详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        ReportArea reportArea = reportAreaService.findById(id);
        return RestApiResponse.ok(reportArea);
    }

    /**
     * 分页查询浙江省行政区划
     *
     * @param reportAreaVo 浙江省行政区划 查询条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/findPageByQuery")
    @Operation(summary = "分页查询浙江省行政区划")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody ReportAreaVo reportAreaVo) {
        PageInfo<ReportArea> reportArea = reportAreaService.findPageByQuery(reportAreaVo);
        return RestApiResponse.ok(reportArea);
    }

    /**
     * 查询省级数据
     *
     * @return RestApiResponse<?>
     */
    @GetMapping("/findProvinceList")
    @Operation(summary = "查询省级数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findProvinceList')")
    public RestApiResponse<?> findProvinceList() {
        List<ReportArea> list = reportAreaService.findProvinceList();
        return RestApiResponse.ok(list);
    }

    /**
     * 查询市级数据
     *
     * @return RestApiResponse<?>
     */
    @GetMapping("/findCityList")
    @Operation(summary = "查询市级数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findCityList')")
    public RestApiResponse<?> findCityList() {
        List<ReportArea> list = reportAreaService.findCityList();
        return RestApiResponse.ok(list);
    }

    /**
     * 查询区县级数据
     *
     * @return RestApiResponse<?>
     */
    @GetMapping("/findCountyList")
    @Operation(summary = "查询区县级数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findCountyList')")
    public RestApiResponse<?> findCountyList() {
        List<ReportArea> list = reportAreaService.findCountyList();
        return RestApiResponse.ok(list);
    }

    /**
     * 根据父级代码查询下级区划
     *
     * @param parentCode 父级代码
     * @return RestApiResponse<?>
     */
    @GetMapping("/findByParentCode")
    @Operation(summary = "根据父级代码查询下级区划")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findByParentCode')")
    public RestApiResponse<?> findByParentCode(@RequestParam("parentCode") String parentCode) {
        List<ReportArea> list = reportAreaService.findByParentCode(parentCode);
        return RestApiResponse.ok(list);
    }

    /**
     * 根据级别查询区划
     *
     * @param level 级别
     * @return RestApiResponse<?>
     */
    @GetMapping("/findByLevel")
    @Operation(summary = "根据级别查询区划")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findByLevel')")
    public RestApiResponse<?> findByLevel(@RequestParam("level") Long level) {
        List<ReportArea> list = reportAreaService.findByLevel(level);
        return RestApiResponse.ok(list);
    }

    /**
     * 获取省市区县树形结构
     *
     * @return RestApiResponse<?>
     */
    @GetMapping("/getAreaTree")
    @Operation(summary = "获取省市区县树形结构")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "getAreaTree')")
    public RestApiResponse<?> getAreaTree() {
        List<ReportArea> list = reportAreaService.getAreaTree();
        return RestApiResponse.ok(list);
    }

    /**
     * 根据用户行政区划获取下属区划列表
     *
     * @return RestApiResponse<?>
     */
    @GetMapping("/getUserSubordinateAreas")
    @Operation(summary = "根据用户行政区划获取下属区划列表")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "getUserSubordinateAreas')")
    public RestApiResponse<?> getUserSubordinateAreas() {
        List<ReportArea> list = reportAreaService.getUserSubordinateAreas(getCurrentUserId());
        return RestApiResponse.ok(list);
    }
}
