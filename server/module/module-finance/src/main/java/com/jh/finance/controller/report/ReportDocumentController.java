package com.jh.finance.controller.report;

import com.jh.common.bean.RestApiResponse;
import com.jh.common.controller.BaseController;

import com.jh.finance.bean.report.vo.ReportDocumentVo;
import com.jh.finance.service.report.ReportDocumentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 文件管理Controller
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@RestController
@RequestMapping("/report/document")
@Tag(name = "文件管理", description = "文件管理相关接口")
public class ReportDocumentController extends BaseController {

    private static final String PER_PREFIX = "btn:report:document:";

    @Autowired
    private ReportDocumentService reportDocumentService;

    /**
     * 分页查询文件管理
     *
     * @param reportDocumentVo 查询参数
     * @return 分页结果
     */
    @PostMapping("/list")
    @Operation(summary = "分页查询文件管理")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "list')")
    public RestApiResponse<?> list(@RequestBody ReportDocumentVo reportDocumentVo) {
        return RestApiResponse.ok(reportDocumentService.findPageByQuery(reportDocumentVo));
    }

    /**
     * 查询文件管理详情
     *
     * @param id 文件ID
     * @return 文件详情（包含文件列表）
     */
    @GetMapping("/findById")
    @Operation(summary = "查询文件管理详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> get(@RequestParam("id") String id) {
        return RestApiResponse.ok(reportDocumentService.findDetailById(id));
    }

    /**
     * 文件管理上报
     *
     * @param vo 上报参数
     * @return 结果
     */
    @PostMapping("/report")
    @Operation(summary = "文件管理上报")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "report')")
    public RestApiResponse<?> report(@RequestBody ReportDocumentVo vo) {
        reportDocumentService.documentReport(vo);
        return RestApiResponse.ok();
    }

    /**
     * 文件管理审核
     *
     * @param vo 审核参数
     * @return 结果
     */
    @PostMapping("/audit")
    @Operation(summary = "文件管理审核")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "audit')")
    public RestApiResponse<?> audit(@RequestBody ReportDocumentVo vo) {
        reportDocumentService.documentAudit(vo);
        return RestApiResponse.ok();
    }

    /**
     * 删除文件管理
     *
     * @param ids 文件ID列表
     * @return 结果
     */
    @PostMapping("/delete")
    @Operation(summary = "删除文件管理")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> delete(@RequestBody List<String> ids) {
        reportDocumentService.deleteReportDocument(ids);
        return RestApiResponse.ok();
    }
}
