package com.jh.finance.bean.fee.vo;


import com.jh.finance.bean.fee.FeeReductionHalve;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 减半收取Vo
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Schema(description = "FeeReductionHalveVo")
@Data
public class FeeReductionHalveVo extends FeeReductionHalve {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;
    


}