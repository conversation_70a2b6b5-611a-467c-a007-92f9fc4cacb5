package com.jh.finance.dao.report;

import com.jh.common.mybatis.BaseInfoMapper;
import com.jh.finance.bean.report.ReportArea;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 浙江省行政区划Mapper接口
 *<AUTHOR>
 *@date 2025-06-09
 */
@Mapper
public interface ReportAreaMapper extends BaseInfoMapper<ReportArea>{
    
    /**
     * 查询省级数据
     * @return List<ReportArea>
     */
    List<ReportArea> findProvinceList();
    
    /**
     * 查询市级数据
     * @return List<ReportArea>
     */
    List<ReportArea> findCityList();
    
    /**
     * 查询区县级数据
     * @return List<ReportArea>
     */
    List<ReportArea> findCountyList();
    
    /**
     * 根据父级代码查询下级区划
     * @param parentCode 父级代码
     * @return List<ReportArea>
     */
    List<ReportArea> findByParentCode(@Param("parentCode") String parentCode);
    
    /**
     * 根据级别查询区划
     * @param level 级别
     * @return List<ReportArea>
     */
    List<ReportArea> findByLevel(@Param("level") Long level);
}
