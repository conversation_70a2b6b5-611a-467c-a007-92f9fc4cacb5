package com.jh.finance.service.fee;

import java.util.List;
import com.github.pagehelper.PageInfo;
import com.jh.finance.bean.fee.FeeReductionFeedback;
import com.jh.finance.bean.fee.vo.FeeReductionFeedbackVo;
import org.springframework.web.multipart.MultipartFile;
/**
 * 减费惠企改革-在线反馈Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface FeeReductionFeedbackService{
	/**
	 * 保存或更新减费惠企改革-在线反馈
	 *@param feeReductionFeedback 减费惠企改革-在线反馈对象
	 *@return String 减费惠企改革-在线反馈ID
	 *<AUTHOR>
	 */
	String saveOrUpdateFeeReductionFeedback(FeeReductionFeedback feeReductionFeedback);
	
	/**
	 * 删除减费惠企改革-在线反馈
	 *@param ids void 减费惠企改革-在线反馈ID
	 *<AUTHOR>
	 */
	void deleteFeeReductionFeedback(List<String> ids);

	/**
	 * 查询减费惠企改革-在线反馈详情
	 *@param id
	 *@return FeeReductionFeedback
	 *<AUTHOR>
	 */
	FeeReductionFeedback findById(String id);

	/**
	 * 分页查询减费惠企改革-在线反馈
	 *@param feeReductionFeedbackVo
	 *@return PageInfo<FeeReductionFeedback>
	 *<AUTHOR>
	 */
	PageInfo<FeeReductionFeedback> findPageByQuery(FeeReductionFeedbackVo feeReductionFeedbackVo);

	/**
     * 按条件导出查询减费惠企改革-在线反馈
     *@param feeReductionFeedbackVo
     *@return PageInfo<FeeReductionFeedback>
     *<AUTHOR>
     */
    List<FeeReductionFeedback> findByQuery(FeeReductionFeedbackVo feeReductionFeedbackVo);

    /**
     * 导入减费惠企改革-在线反馈
     *@param file
     *@param cover 是否覆盖 1 覆盖 0 不覆盖
     *@return
     *<AUTHOR>
     */
    void importFeeReductionFeedbackAsync(MultipartFile file, Integer cover);
}
