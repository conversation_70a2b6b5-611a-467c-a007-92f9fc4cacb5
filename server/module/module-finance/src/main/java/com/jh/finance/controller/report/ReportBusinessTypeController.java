package com.jh.finance.controller.report;

import java.util.List;

import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.finance.bean.report.ReportBusinessType;
import com.jh.finance.bean.report.vo.ReportBusinessTypeVo;
import com.jh.finance.service.report.ReportBusinessTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.jh.common.bean.RestApiResponse;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.controller.BaseController;

import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

/**
 * 业务类型表Controller
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@RestController
@RequestMapping("/report/bType")
@Tag(name = "业务类型表")
public class ReportBusinessTypeController extends BaseController {
    @Autowired
    private ReportBusinessTypeService reportBusinessTypeService;

    private static final String PER_PREFIX = "btn:report:business:type:";

    /**
     * 新增业务类型表
     *
     * @param reportBusinessType 业务类型表数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @Operation(summary = "新增业务类型表")
    @SystemLogAnnotation(type = "业务类型表", value = "新增业务类型表")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveReportBusinessType(@RequestBody ReportBusinessType reportBusinessType) {
        String id = reportBusinessTypeService.saveOrUpdateReportBusinessType(reportBusinessType);
        return RestApiResponse.ok(id);
    }

    /**
     * 修改业务类型表
     *
     * @param reportBusinessType 业务类型表数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @Operation(summary = "修改业务类型表")
    @SystemLogAnnotation(type = "业务类型表", value = "修改业务类型表")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateReportBusinessType(@RequestBody ReportBusinessType reportBusinessType) {
        String id = reportBusinessTypeService.saveOrUpdateReportBusinessType(reportBusinessType);
        return RestApiResponse.ok(id);
    }

    /**
     * 批量删除业务类型表(判断 关联数据是否可以删除)
     *
     * @param ids
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @Operation(summary = "批量删除业务类型表")
    @SystemLogAnnotation(type = "业务类型表", value = "批量删除业务类型表")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteReportBusinessType(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        reportBusinessTypeService.deleteReportBusinessType(ids);
        return RestApiResponse.ok();
    }

    /**
     * 查询业务类型表详情
     *
     * @param id
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findById")
    @Operation(summary = "查询业务类型表详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        ReportBusinessType reportBusinessType = reportBusinessTypeService.findById(id);
        return RestApiResponse.ok(reportBusinessType);
    }

    /**
     * 分页查询业务类型表
     *
     * @param reportBusinessTypeVo 业务类型表 查询条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/findPageByQuery")
    @Operation(summary = "分页查询业务类型表")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody ReportBusinessTypeVo reportBusinessTypeVo) {
        PageInfo<ReportBusinessType> reportBusinessType = reportBusinessTypeService.findPageByQuery(reportBusinessTypeVo);
        return RestApiResponse.ok(reportBusinessType);
    }

    /**
     * 查询顶级业务类型
     *
     * @return RestApiResponse<?>
     */
    @GetMapping("/findParentTypes")
    @Operation(summary = "查询顶级业务类型")
    public RestApiResponse<?> findParentTypes() {
        List<ReportBusinessType> list = reportBusinessTypeService.findParentTypes();
        return RestApiResponse.ok(list);
    }

    /**
     * 根据父ID查询子业务类型
     *
     * @param parentId 父ID
     * @return RestApiResponse<?>
     */
    @GetMapping("/findByParentId")
    @Operation(summary = "根据父ID查询子业务类型")
    public RestApiResponse<?> findByParentId(@RequestParam("parentId") Integer parentId) {
        List<ReportBusinessType> list = reportBusinessTypeService.findByParentId(parentId);
        return RestApiResponse.ok(list);
    }

    /**
     * 根据组织ID查询业务类型
     *
     * @param orgId 组织ID
     * @return RestApiResponse<?>
     */
    @GetMapping("/findByOrgId")
    @Operation(summary = "根据组织ID查询业务类型")
    public RestApiResponse<?> findByOrgId(@RequestParam("orgId") Integer orgId) {
        List<ReportBusinessType> list = reportBusinessTypeService.findByOrgId(orgId);
        return RestApiResponse.ok(list);
    }

    /**
     * 查询业务类型树形结构
     *
     * @return RestApiResponse<?>
     */
    @GetMapping("/findTypeTree")
    @Operation(summary = "查询业务类型树形结构")
    public RestApiResponse<?> findTypeTree() {
        List<ReportBusinessType> list = reportBusinessTypeService.findTypeTree();
        return RestApiResponse.ok(list);
    }
} 