package com.jh.finance.bean.fee;

import java.math.BigDecimal;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.jh.common.bean.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

/**
 * 收费赋码对象 fee_coding
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Table(name = "fee_coding")
@Schema(description = "收费赋码")
@Data
public class FeeCoding extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "收费代码")
    private String code;
    @Column(name = "MAJOR_CATEGORY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "收费大类")
    private String majorCategory;
    @Column(name = "MIDDLE_CATEGORY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "收费中类")
    private String middleCategory;
    @Column(name = "MINOR_CATEGORY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "收费小类")
    private String minorCategory;
    @Column(name = "PRODUCT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "收费产品（检测对象）")
    private String product;
    @Column(name = "DETECTION_ITEM")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "收费项目/参数")
    private String detectionItem;
    @Column(name = "FEE_PRICE")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @Schema(description = "收费价格（元）")
    private BigDecimal feePrice;
    @Column(name = "BILLING_UNIT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "收费计费单元")
    private String billingUnit;
    @Column(name = "STANDARD_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "收费标准代号")
    private String standardCode;
    @Column(name = "REMARK")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "收费备注")
    private String remark;
    @Column(name = "USER_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "用户ID")
    private String userId;

}
