package com.jh.finance.controller.fee;
import java.util.List;
import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.finance.bean.fee.FeeReductionEvaluation;
import com.jh.finance.bean.fee.vo.FeeReductionEvaluationVo;
import com.jh.finance.service.fee.FeeReductionEvaluationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.github.pagehelper.PageInfo;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.controller.BaseController;
import org.springframework.web.multipart.MultipartFile;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

/**
 * 减费惠企改革-一指评价Controller
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@RestController
@RequestMapping("/fee/evaluation")
@Tag(name = "减费惠企改革-一指评价")
public class FeeReductionEvaluationController extends BaseController {
    @Autowired
    private FeeReductionEvaluationService feeReductionEvaluationService;

    private static final String PER_PREFIX = "btn:fee:evaluation:";

    /**
     * 新增减费惠企改革-一指评价
     *
     * @param feeReductionEvaluation 减费惠企改革-一指评价数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @Operation(summary = "新增减费惠企改革-一指评价")
    @SystemLogAnnotation(type = "减费惠企改革-一指评价", value = "新增减费惠企改革-一指评价")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveFeeReductionEvaluation(@RequestBody FeeReductionEvaluation feeReductionEvaluation) {
        String id = feeReductionEvaluationService.saveOrUpdateFeeReductionEvaluation(feeReductionEvaluation);
        return RestApiResponse.ok(id);
    }

    /**
     * 修改减费惠企改革-一指评价
     *
     * @param feeReductionEvaluation 减费惠企改革-一指评价数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @Operation(summary = "修改减费惠企改革-一指评价")
    @SystemLogAnnotation(type = "减费惠企改革-一指评价", value = "修改减费惠企改革-一指评价")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateFeeReductionEvaluation(@RequestBody FeeReductionEvaluation feeReductionEvaluation) {
        String id = feeReductionEvaluationService.saveOrUpdateFeeReductionEvaluation(feeReductionEvaluation);
        return RestApiResponse.ok(id);
    }

    /**
     * 批量删除减费惠企改革-一指评价(判断 关联数据是否可以删除)
     *
     * @param ids
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @Operation(summary = "批量删除减费惠企改革-一指评价")
    @SystemLogAnnotation(type = "减费惠企改革-一指评价", value = "批量删除减费惠企改革-一指评价")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteFeeReductionEvaluation(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        feeReductionEvaluationService.deleteFeeReductionEvaluation(ids);
        return RestApiResponse.ok();
    }

    /**
     * 查询减费惠企改革-一指评价详情
     *
     * @param id
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findById")
    @Operation(summary = "查询减费惠企改革-一指评价详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        FeeReductionEvaluation feeReductionEvaluation = feeReductionEvaluationService.findById(id);
        return RestApiResponse.ok(feeReductionEvaluation);
    }

    /**
     * 分页查询减费惠企改革-一指评价
     *
     * @param feeReductionEvaluationVo 减费惠企改革-一指评价 查询条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/findPageByQuery")
    @Operation(summary = "分页查询减费惠企改革-一指评价")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody FeeReductionEvaluationVo feeReductionEvaluationVo) {
        PageInfo<FeeReductionEvaluation> feeReductionEvaluation = feeReductionEvaluationService.findPageByQuery(feeReductionEvaluationVo);
        return RestApiResponse.ok(feeReductionEvaluation);
    }

    @PostMapping("/excel")
    @Operation(summary = "导出减费惠企改革-一指评价")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "excel')")
    // 这里的模块名称和子模块名称，请根据实际情况填写
    @ExportRespAnnotation(modType = "减费惠企改革-一指评价", sonMod = "减费惠企改革-一指评价", key = "jh_evaluation_Table", isNotice = true, fileName = "减费惠企改革-一指评价")
    public RestApiResponse<?> excel(@RequestBody FeeReductionEvaluationVo feeReductionEvaluationVo) {
        List<FeeReductionEvaluation> feeReductionEvaluationList = feeReductionEvaluationService.findByQuery(feeReductionEvaluationVo);
        //这里对数据进行转换处理，如没使用字典的或关联其它表的数据。如在查询中已处理请忽略
        return RestApiResponse.ok(feeReductionEvaluationList);
    }

    @PostMapping("/import")
    @Operation(summary = "导入减费惠企改革-一指评价")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "import')")
    public RestApiResponse<?> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "cover") Integer cover) {
        feeReductionEvaluationService.importFeeReductionEvaluationAsync(file, cover);
        return RestApiResponse.ok();
    }
}
