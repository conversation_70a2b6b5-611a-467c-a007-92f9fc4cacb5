package com.jh.finance.bean.fee;

import java.math.BigDecimal;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.jh.common.bean.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

/**
 * 非标类项目对象 fee_non_standard
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Table(name = "fee_non_standard")
@Schema(description = "非标类项目")
@Data
public class FeeNonStandard extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "FEE_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "收费编码")
    private String feeCode;
    @Column(name = "FEE_ITEM")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "收费项目")
    private String feeItem;
    @Column(name = "FEE_STANDARD")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @Schema(description = "收费标准（元）")
    private BigDecimal feeStandard;
    @Column(name = "FEE_UNIT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "收费单元")
    private String feeUnit;
    @Column(name = "FEE_BASIS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "收费依据")
    private String feeBasis;
    @Column(name = "REMARK")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "备注")
    private String remark;
    @Column(name = "USER_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "用户ID")
    private String userId;

    @Column(name = "ORG_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "机构CODE")
    private String orgCode;

}
