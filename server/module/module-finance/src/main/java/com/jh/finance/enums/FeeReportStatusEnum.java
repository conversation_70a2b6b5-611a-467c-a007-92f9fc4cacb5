package com.jh.finance.enums;

/**
 * 收费统计上报流程状态
 *
 * <AUTHOR>
 * @date 2024/06/10
 */
public enum FeeReportStatusEnum {
    DTC("00", "待提交"),
    DCL("10", "待审核"),
    YCL("20", "已审核");

    private final String code;
    private final String value;

    FeeReportStatusEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 根据code获取枚举对象
     *
     * @param code code
     * @return 枚举对象
     */
    public static FeeReportStatusEnum getFeeReportStatusEnum(String code) {
        for (FeeReportStatusEnum e : FeeReportStatusEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
} 