package com.jh.finance.service.fee.impl;


import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.common.bean.LoginUser;
import com.jh.common.util.AppUserUtil;
import com.jh.common.util.io.FileConvertUtils;
import com.jh.common.util.io.FileUtil;
import com.jh.common.util.poi.EasyExcelUtils;
import com.jh.constant.enums.ImportStatusEnum;
import com.jh.finance.bean.fee.FeeNonStandard;
import com.jh.finance.bean.fee.vo.FeeNonStandardExcel;
import com.jh.finance.bean.fee.vo.FeeNonStandardVo;
import com.jh.finance.dao.fee.FeeNonStandardMapper;
import com.jh.finance.service.fee.FeeNonStandardService;
import com.jh.utils.ImportService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.CollectionUtils;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.concurrent.CompletableFuture;

import java.util.List;
import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;

import com.jh.common.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;
import static com.github.pagehelper.page.PageMethod.orderBy;
import org.springframework.web.multipart.MultipartFile;

/**
 *  非标类项目Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-02
 */
@Service
@Transactional(readOnly = true)
public class FeeNonStandardServiceImpl extends BaseServiceImpl<FeeNonStandardMapper, FeeNonStandard> implements FeeNonStandardService {
	
	private static final Logger logger = LoggerFactory.getLogger(FeeNonStandardServiceImpl.class);
    @Autowired
    private FeeNonStandardMapper feeNonStandardMapper;

    @Autowired
	private ImportService importService;

	@Value("${file.temp.path}")
	private String tempPath;

	@Override
	@Transactional(readOnly = false)
	/**
	 * 保存或更新非标类项目
	 *@param feeNonStandard 非标类项目对象
	 *@return String 非标类项目ID
	 *<AUTHOR>
	 */
	public String saveOrUpdateFeeNonStandard(FeeNonStandard feeNonStandard) {
		if(feeNonStandard==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(feeNonStandard.getId())){
			//新增
			feeNonStandard.setId(UUIDUtils.getUUID());
			feeNonStandard.setUserId(AppUserUtil.getCurrentUserId());
            feeNonStandard.setOrgCode(AppUserUtil.getCurrentUserName());
			feeNonStandardMapper.insertSelective(feeNonStandard);
		}else{
			//TODO 做判断后方能执行修改 一般判断是否是自己的数据
			//避免页面传入修改
			feeNonStandard.setYn(null);
			feeNonStandardMapper.updateByPrimaryKeySelective(feeNonStandard);
		}
		return feeNonStandard.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 * 删除非标类项目
	 *@param ids void 非标类项目ID
	 *<AUTHOR>
	 */
	public void deleteFeeNonStandard(List<String> ids) {
		for(String id:ids){
			//TODO 做判断后方能执行删除 一般判断是否是自己的数据
			FeeNonStandard feeNonStandard=feeNonStandardMapper.selectByPrimaryKey(id);
			if(feeNonStandard==null || !AppUserUtil.getCurrentUserId().equals(feeNonStandard.getUserId())){
				throw new ServiceException("非法请求");
			}
			//逻辑删除
			FeeNonStandard temfeeNonStandard=new FeeNonStandard();
			temfeeNonStandard.setYn(CommonConstant.FLAG_NO);
			temfeeNonStandard.setId(feeNonStandard.getId());
			feeNonStandardMapper.updateByPrimaryKeySelective(temfeeNonStandard);
		}
	}

	/**
	 * 查询非标类项目详情
	 *@param id
	 *@return FeeNonStandard
	 *<AUTHOR>
	 */
    @Override
	public FeeNonStandard findById(String id) {
	    // TODO 做判断后方能反回数据 一般判断是否是自己的数据
		FeeNonStandard feeNonStandard = feeNonStandardMapper.selectByPrimaryKey(id);
		if(feeNonStandard != null && !AppUserUtil.getCurrentUserId().equals(feeNonStandard.getUserId())){
			throw new ServiceException("非法请求");
		}
		return feeNonStandard;
	}


	/**
	 * 分页查询非标类项目
	 *@param feeNonStandardVo
	 *@return PageInfo<FeeNonStandard>
	 *<AUTHOR>
	 */
	@Override
	public PageInfo<FeeNonStandard> findPageByQuery(FeeNonStandardVo feeNonStandardVo) {
		// TODO 做判断后方能执行查询 一般判断是否是自己的数据
		PageHelper.startPage(feeNonStandardVo.getPageNum(),feeNonStandardVo.getPageSize());
		orderBy(feeNonStandardVo.getOrderColumn() + " " + feeNonStandardVo.getOrderValue());
		Example example=getExample(feeNonStandardVo);
		List<FeeNonStandard> feeNonStandardList=feeNonStandardMapper.selectByExample(example);
		return new PageInfo<FeeNonStandard>(feeNonStandardList);
	}
	private Example getExample(FeeNonStandardVo feeNonStandardVo){
		Example example=new Example(FeeNonStandard.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		criteria.andEqualTo("userId", AppUserUtil.getCurrentUserId());
        criteria.andEqualTo("orgCode", AppUserUtil.getCurrentUserName());
		//查询条件
		//if(!StringUtils.isEmpty(feeNonStandardVo.getName())){
		//	criteria.andEqualTo(feeNonStandardVo.getName());
		//}
		example.orderBy("updateTime").desc();
		return example;
	}

	/**
     * 按条件导出查询非标类项目
     *@param feeNonStandardVo
     *@return PageInfo<FeeNonStandard>
     *<AUTHOR>
     */
    @Override
    public List<FeeNonStandard> findByQuery(FeeNonStandardVo feeNonStandardVo){
    	orderBy(feeNonStandardVo.getOrderColumn() + " " + feeNonStandardVo.getOrderValue());
        Example example=getExample(feeNonStandardVo);
        return feeNonStandardMapper.selectByExample(example);
    }

    /**
     * 导入非标类项目
     *@param file
     *@param cover 是否覆盖 1 覆盖 0 不覆盖
     *@return
     *<AUTHOR>
     */
    @Override
    public void importFeeNonStandardAsync(MultipartFile file, Integer cover) {
        //为后续数据处理线程设置用户信息
        Object user = AppUserUtil.getLoginAppUser();
        LoginUser appLoginUser = (LoginUser) user;
        importService.notification(appLoginUser,"已创建非标类项目导入任务，完成后会通知", ExportRespAnnotation.SocketMessageTypeEnum.NORMAL,"非标类项目",null);
        // 开始时间
        Date startTime = new Date();
        // 验证excel
        importService.verifyExcel(file);
        List<FeeNonStandardExcel> list;
        // 读取数据
        try(InputStream inputStream = file.getInputStream()){
            list = (List<FeeNonStandardExcel>) EasyExcelUtils.readExcel(FeeNonStandardExcel.class, inputStream);
            if (CollectionUtils.isEmpty(list)) {
                throw new ServiceException("模板文件为空或者异常，请使用提供的模板进行导入");
            }
        } catch (IOException e) {
            throw new ServiceException("请检查填写内容，如：日期格式");
        }
        // 复制file 文件
        String pathForSave = tempPath + "/" + UUIDUtils.getUUID()+"&&" + file.getOriginalFilename();
        File copyFile = FileUtil.copyFile(file, pathForSave);
        MultipartFile copyMultipartFile = FileConvertUtils.createFileItem(copyFile);
        //这里写数据处理逻辑
        CompletableFuture.runAsync(() -> {
            try {
                // 设置用户信息 避免异步处理数据时丢失用户信息
                UsernamePasswordAuthenticationToken authentication =
                        new UsernamePasswordAuthenticationToken(appLoginUser, null, appLoginUser.getAuthorities());
                SecurityContextHolder.getContext().setAuthentication(authentication);
                List<FeeNonStandard> insertList = new ArrayList<>();
                //数据处理
                for(FeeNonStandardExcel item:list){
                    //这里对数据进行校验
                    if(StringUtils.isEmpty(item.getFeeCode())){
                        item.setImportSituation(ImportStatusEnum.ERROR.getName()+"编码为空");
                        continue;
                    }
                   
                    // 检查收费项目
                    if(StringUtils.isEmpty(item.getFeeItem())){
                        item.setImportSituation(ImportStatusEnum.ERROR.getName()+"收费项目为空");
                        continue;
                    }
                    
                    // 检查收费标准
                    if(item.getFeeStandard() == null){
                        item.setImportSituation(ImportStatusEnum.ERROR.getName()+"收费标准为空");
                        continue;
                    }
                    
                    // 检查收费单元
                    if(StringUtils.isEmpty(item.getFeeUnit())){
                        item.setImportSituation(ImportStatusEnum.ERROR.getName()+"收费单元为空");
                        continue;
                    }

                    // 检查是否存在相同编码的记录，存在则更新，不存在则新增
                    Example example = new Example(FeeNonStandard.class);
                    example.createCriteria().andEqualTo("feeCode", item.getFeeCode())
                            .andEqualTo("userId", AppUserUtil.getCurrentUserId())
                            .andEqualTo("yn", CommonConstant.FLAG_YES);
                    List<FeeNonStandard> existingList = feeNonStandardMapper.selectByExample(example);
                    
                    if (!CollectionUtils.isEmpty(existingList)) {
                        // 存在记录，更新第一条记录
                        FeeNonStandard existing = existingList.get(0);
                        BeanUtils.copyProperties(item, existing);
                        existing.setUpdateUser(AppUserUtil.getCurrentUserName());
                        existing.setUpdateUserNickname(AppUserUtil.getCurrentRealName());
                        existing.setUpdateTime(new Date());
                        feeNonStandardMapper.updateByPrimaryKeySelective(existing);
                    } else {
                        // 不存在记录，新增
                        FeeNonStandard feeNonStandard = new FeeNonStandard();
                        BeanUtils.copyProperties(item, feeNonStandard);
                        feeNonStandard.setId(UUIDUtils.getUUID());
                        feeNonStandard.setUserId(AppUserUtil.getCurrentUserId());
                        feeNonStandard.setCreateUser(AppUserUtil.getCurrentUserName());
                        feeNonStandard.setCreateUserNickname(AppUserUtil.getCurrentRealName());
                        feeNonStandard.setUpdateUser(AppUserUtil.getCurrentUserName());
                        feeNonStandard.setUpdateUserNickname(AppUserUtil.getCurrentRealName());
                        feeNonStandard.setCreateTime(new Date());
                        feeNonStandard.setUpdateTime(new Date());
                        feeNonStandard.setYn(CommonConstant.FLAG_YES);
                        insertList.add(feeNonStandard);
                    }
                    item.setImportSituation(ImportStatusEnum.IMPORT_SUCCESS.getName());
                }
                //保存数据
                if(!CollectionUtils.isEmpty(insertList)){
                    // 批量插入数据
                    feeNonStandardMapper.insertList(insertList);
                }
                String fileUrl = importService.faultDataAsync(list, FeeNonStandardExcel.class, "导入结果.xls",
                        "非标类项目", "非标类项目", startTime, copyMultipartFile, appLoginUser);
                importService.notification(appLoginUser,"非标类项目-导入结果.xls", ExportRespAnnotation.SocketMessageTypeEnum.DOWN,file.getOriginalFilename() + "导入成功",fileUrl);
            }catch (Exception e){
                logger.error("导入非标类项目异常", e);
                importService.notification(appLoginUser,e.getMessage(), ExportRespAnnotation.SocketMessageTypeEnum.NORMAL,"非标类项目导入【出现异常】",null);
            }finally {
                try {
                    Files.deleteIfExists(Paths.get(pathForSave));
                } catch (Exception e) {
                    logger.error("删除文件失败", e);
                }
            }
        });
    }

    /**
     * 根据机构代码查询检验检测服务项目（非标类）
     *
     * @param orgCode 机构代码
     * @return List<FeeNonStandard> 非标类收费信息列表
     * <AUTHOR>
     */
    @Override
    public List<FeeNonStandard> findNonStandardByOrgCode(String orgCode) {
        return feeNonStandardMapper.findNonStandardByOrgCode(orgCode);
    }
}
