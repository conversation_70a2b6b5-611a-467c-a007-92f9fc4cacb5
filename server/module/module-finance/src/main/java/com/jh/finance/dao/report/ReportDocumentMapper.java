package com.jh.finance.dao.report;

import com.jh.common.mybatis.BaseInfoMapper;
import com.jh.finance.bean.report.ReportDocument;
import com.jh.finance.bean.report.vo.ReportDocumentVo;

import java.util.List;

/**
 * 文件管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
public interface ReportDocumentMapper extends BaseInfoMapper<ReportDocument> {

    /**
     * 根据条件查询文件管理数据
     *
     * @param vo 查询条件
     * @return 文件管理列表
     */
    List<ReportDocument> query(ReportDocumentVo vo);

    /**
     * 查询代办
     *
     * @param vo 查询条件
     * @return 文件管理列表
     */
    List<ReportDocument> queryToDo(ReportDocumentVo vo);

}
