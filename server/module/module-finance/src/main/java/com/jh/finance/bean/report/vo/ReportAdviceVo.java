package com.jh.finance.bean.report.vo;

import com.jh.finance.bean.report.ReportAdvice;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 数据上报流程-审批意见表Vo
 *
 * <AUTHOR>
 * @date 2024-06-10
 */
@Schema(description = "ReportAdviceVo")
@Data
public class ReportAdviceVo extends ReportAdvice {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;
} 