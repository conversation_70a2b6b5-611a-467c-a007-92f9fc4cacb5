package com.jh.finance.bean.report;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.jh.common.bean.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

/**
 * 减负降本数据对象 report_reduce
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Table(name = "report_reduce")
@Schema(description = "减负降本数据")
@Data
public class ReportReduce extends BaseEntity {
    private static final long serialVersionUID = 1L;


    @Column(name = "FLOW_KEY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    private String flowKey;

    @Column(name = "FLOW_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    private String flowName;

    @Column(name = "ACT_NODE_KEY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    private String actNodeKey;

    @Column(name = "ACT_NODE_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    private String actNodeName;

    @Column(name = "USER_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    private String userId;

    @Column(name = "COMPANY_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "企业名称")
    private String companyName;
    @Column(name = "USCC")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "统一社会信用代码")
    private String uscc;
    @Column(name = "COUNTY_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "区县code")
    private String countyCode;
    @Column(name = "COUNTY_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "区县名称")
    private String countyName;
    @Column(name = "CITY_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "地市code")
    private String cityCode;
    @Column(name = "CITY_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "地市名称")
    private String cityName;
    @Column(name = "PRODUCT_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "产品或设备名称")
    private String productName;
    @Column(name = "PRODUCT_COUNT")
    @ColumnType(jdbcType = JdbcType.BIGINT)
    @Schema(description = "数量")
    private Integer productCount;
    @Column(name = "INDUSTRY_CLASS_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "行业大类code")
    private String industryClassCode;
    @Column(name = "INDUSTRY_CLASS_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "行业大类")
    private String industryClassName;
    @Column(name = "INDUSTRY_CATEGORY_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "行业门类code")
    private String industryCategoryCode;
    @Column(name = "INDUSTRY_CATEGORY_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "行业门类")
    private String industryCategoryName;
    @Column(name = "IS_POLICY_COMPLIANT")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "是否符合政策 1：是 0：否")
    private Integer isPolicyCompliant;
    @Column(name = "POLICY_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "政策名称")
    private String policyName;
    @Column(name = "POLICY_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "政策ID")
    private String policyId;
    @Column(name = "POLICY_TYPE_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "政策类型id")
    private String policyTypeId;
    @Column(name = "POLICY_TYPE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "政策类型")
    private String policyType;
    @Column(name = "ENJOY_TIME")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @Schema(description = "享受时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date enjoyTime;
    @Column(name = "REDUCE_AMOUNT")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @Schema(description = "本次减免费用（元）")
    private BigDecimal reduceAmount;
    @Column(name = "CONTACT_PHONE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "联系人手机")
    private String contactPhone;

    @Column(name = "IS_COMMIT")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "是否提交")
    private Integer isCommit;

}
