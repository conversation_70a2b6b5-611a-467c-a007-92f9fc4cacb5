package com.jh.finance.bean.fee.vo;


import com.jh.finance.bean.fee.FeeReductionFeedback;
import io.swagger.v3.oas.annotations.media.Schema;


/**
 * 减费惠企改革-在线反馈Vo
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Schema(description = "FeeReductionFeedbackVo")
public class FeeReductionFeedbackVo extends FeeReductionFeedback {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}