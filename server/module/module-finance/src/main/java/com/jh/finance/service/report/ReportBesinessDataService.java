package com.jh.finance.service.report;

import java.util.List;
import java.util.Map;
import com.github.pagehelper.PageInfo;
import com.jh.finance.bean.report.ReportBesinessData;
import com.jh.finance.bean.report.vo.ReportBesinessDataVo;
import com.jh.finance.bean.report.vo.ReportStatisticsQueryVo;
import com.jh.finance.bean.report.vo.ReportStatisticsVo;
import com.jh.finance.bean.report.vo.ReportSummaryVo;
/**
 * 收费统计Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface ReportBesinessDataService{
	/**
	 * 保存或更新收费统计
	 *@param reportBesinessData 收费统计对象
	 *@return String 收费统计ID
	 *<AUTHOR>
	 */
	String saveOrUpdateReportBesinessData(ReportBesinessData reportBesinessData);
	
	/**
	 * 删除收费统计
	 *@param ids void 收费统计ID
	 *<AUTHOR>
	 */
	void deleteReportBesinessData(List<String> ids);

	/**
	 * 查询收费统计详情
	 *@param id
	 *@return ReportBesinessDataVo
	 *<AUTHOR>
	 */
	ReportBesinessDataVo findById(String id);

	/**
	 * 分页查询收费统计
	 *@param reportBesinessDataVo
	 *@return PageInfo<ReportBesinessData>
	 *<AUTHOR>
	 */
	PageInfo<ReportBesinessData> findPageByQuery(ReportBesinessDataVo reportBesinessDataVo);

	/**
	 * 收费统计上报
	 *
	 * @param vo
	 * @return
	 */
	void feeReport(ReportBesinessDataVo vo);
	
	/**
	 * 收费统计审核
	 *
	 * @param vo 包含审核参数的VO对象
	 */
	void feeAudit(ReportBesinessDataVo vo);
	
	/**
     * 获取收费数据总计
     *
     * @param vo 查询参数
     * @return 统计结果
     */
	List<ReportStatisticsQueryVo> getDataTotal(ReportBesinessDataVo vo);

	/**
     * 获取减负降本数据总计
     *
     * @param vo 查询参数
     * @return 统计结果
     */
	List<ReportStatisticsQueryVo> getReduceDataTotal(ReportBesinessDataVo vo);

	/**
     * 数据上报情况查询
     * 根据地区代码返回不同层级的数据：
     * - 传入330000：返回浙江省内各市的汇总数据
     * - 传入具体市代码：返回该市下各机构的详细数据（支持分页）
     *
     * @param vo 查询参数（年份、季度、地区代码、分页参数）
     * @return 统计结果
     */
	PageInfo<ReportSummaryVo> getReportSummary(ReportBesinessDataVo vo);
}
