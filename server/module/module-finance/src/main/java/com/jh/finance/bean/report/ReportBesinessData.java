package com.jh.finance.bean.report;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.jh.common.bean.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

/**
 * 收费统计对象 report_besiness_data
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Table(name = "report_besiness_data")
@Schema(description = "收费统计")
@Data
public class ReportBesinessData extends BaseEntity {
    private static final long serialVersionUID = 1L;


    @Column(name = "FLOW_KEY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    private String flowKey;

    @Column(name = "FLOW_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    private String flowName;

    @Column(name = "ACT_NODE_KEY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    private String actNodeKey;

    @Column(name = "ACT_NODE_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    private String actNodeName;

    @Column(name = "USER_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    private String userId;

    @Column(name = "TYPE_ID")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "类型ID")
    private Integer typeId;

    @Column(name = "INPUT_TIME")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @Schema(description = "录入时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date inputTime;

    @Column(name = "IS_COMMIT")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "是否提交")
    private Integer isCommit;

    @Column(name = "YEAR")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "年份")
    private Integer year;

    @Column(name = "QUARTER")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "季度")
    private Integer quarter;

    @Column(name = "MONTH")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "月份")
    private Integer month;

    @Column(name = "COUNT_MONEY")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @Schema(description = "总金额")
    private BigDecimal countMoney;
}
