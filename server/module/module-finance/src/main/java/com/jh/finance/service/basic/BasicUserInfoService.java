package com.jh.finance.service.basic;

import java.util.List;
import com.github.pagehelper.PageInfo;

import com.jh.common.bean.SysUser;
import com.jh.finance.bean.basic.BasicUserInfo;
import com.jh.finance.bean.basic.vo.BasicUserInfoReq;
import com.jh.finance.bean.basic.vo.BasicUserInfoVo;
import org.springframework.web.multipart.MultipartFile;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;
/**
 * 用户平台信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
public interface BasicUserInfoService{
	/**
	 * 保存或更新用户平台信息
	 *@param basicUserInfo 用户平台信息对象
	 *@return String 用户平台信息ID
	 *<AUTHOR>
	 */
	String saveOrUpdateBasicUserInfo(BasicUserInfo basicUserInfo);
	
	/**
	 * 删除用户平台信息
	 *@param ids void 用户平台信息ID
	 *<AUTHOR>
	 */
	void deleteBasicUserInfo(List<String> ids);

	/**
	 * 查询用户平台信息详情
	 *@param id
	 *@return BasicUserInfo
	 *<AUTHOR>
	 */
	BasicUserInfo findById(String id);

	/**
	 * 分页查询用户平台信息
	 *@param basicUserInfoVo
	 *@return PageInfo<BasicUserInfo>
	 *<AUTHOR>
	 */
	PageInfo<BasicUserInfo> findPageByQuery(BasicUserInfoVo basicUserInfoVo);

	/**
     * 按条件导出查询用户平台信息
     *@param basicUserInfoVo
     *@return PageInfo<BasicUserInfo>
     *<AUTHOR>
     */
    List<BasicUserInfo> findByQuery(BasicUserInfoVo basicUserInfoVo);

    /**
     * 验证用户平台APPID和APPKEY
     *@param appId 平台ID
     *@param appKey 平台KEY
     *@param response HTTP响应对象
     *@return void 如果验证成功，直接返回token到response；如果失败，抛出异常
     *<AUTHOR>
     */
	BasicUserInfoReq validateAppIdAndKey(String appId, String appKey, HttpServletResponse response);

    /**
     * 验证token的有效性并返回用户信息
     *@param token 待验证的token
     *@return BasicUserInfo 如果token有效则返回用户信息，否则返回null
     *<AUTHOR>
     */
    SysUser validateTokenAndGetUser(String token);

}
