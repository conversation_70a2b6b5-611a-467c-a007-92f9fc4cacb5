package com.jh.finance.service.report;

import java.util.List;
import java.util.Map;
import com.github.pagehelper.PageInfo;

import com.jh.finance.bean.report.ReportBusinessTypePolicy;
import com.jh.finance.bean.report.vo.ReportBusinessTypePolicyVo;

/**
 * 业务类型政策Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface ReportBusinessTypePolicyService{
	/**
	 * 保存或更新业务类型政策
	 *@param reportBusinessTypePolicy 业务类型政策对象
	 *@return String 业务类型政策ID
	 *<AUTHOR>
	 */
	String saveOrUpdateReportBusinessTypePolicy(ReportBusinessTypePolicy reportBusinessTypePolicy);
	
	/**
	 * 删除业务类型政策
	 *@param ids void 业务类型政策ID
	 *<AUTHOR>
	 */
	void deleteReportBusinessTypePolicy(List<String> ids);

	/**
	 * 查询业务类型政策详情
	 *@param id
	 *@return ReportBusinessTypePolicy
	 *<AUTHOR>
	 */
	ReportBusinessTypePolicy findById(String id);

	/**
	 * 分页查询业务类型政策
	 *@param reportBusinessTypePolicyVo
	 *@return PageInfo<ReportBusinessTypePolicy>
	 *<AUTHOR>
	 */
	PageInfo<ReportBusinessTypePolicy> findPageByQuery(ReportBusinessTypePolicyVo reportBusinessTypePolicyVo);

	/**
	 * 根据业务类型ID查询政策
	 * @param businessTypeId 业务类型ID
	 * @return List<ReportBusinessTypePolicy> 政策列表
	 * <AUTHOR>
	 */
	List<ReportBusinessTypePolicy> findByBusinessTypeId(String businessTypeId);

	/**
	 * 根据业务类型ID查询政策并按TYPE分组返回VO对象
	 * @param businessTypeId 业务类型ID
	 * @return ReportBusinessTypePolicyVo 包含分组后的政策数据
	 * <AUTHOR>
	 */
	ReportBusinessTypePolicyVo findByBusinessTypeIdGrouped(String businessTypeId);
}
