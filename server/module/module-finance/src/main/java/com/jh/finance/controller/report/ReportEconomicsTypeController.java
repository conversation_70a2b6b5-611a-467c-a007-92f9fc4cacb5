package com.jh.finance.controller.report;

import java.util.List;

import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.finance.bean.report.ReportEconomicsType;
import com.jh.finance.bean.report.vo.ReportEconomicsTypeVo;
import com.jh.finance.service.report.ReportEconomicsTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.jh.common.bean.RestApiResponse;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.controller.BaseController;
import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

/**
 * 行业类型Controller
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@RestController
@RequestMapping("/report/type")
@Tag(name = "行业类型")
public class ReportEconomicsTypeController extends BaseController {
    @Autowired
    private ReportEconomicsTypeService reportEconomicsTypeService;

    private static final String PER_PREFIX = "btn:report:type:";

    /**
     * 新增行业类型
     *
     * @param reportEconomicsType 行业类型数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @Operation(summary = "新增行业类型")
    @SystemLogAnnotation(type = "行业类型", value = "新增行业类型")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveReportEconomicsType(@RequestBody ReportEconomicsType reportEconomicsType) {
        String id = reportEconomicsTypeService.saveOrUpdateReportEconomicsType(reportEconomicsType);
        return RestApiResponse.ok(id);
    }

    /**
     * 修改行业类型
     *
     * @param reportEconomicsType 行业类型数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @Operation(summary = "修改行业类型")
    @SystemLogAnnotation(type = "行业类型", value = "修改行业类型")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateReportEconomicsType(@RequestBody ReportEconomicsType reportEconomicsType) {
        String id = reportEconomicsTypeService.saveOrUpdateReportEconomicsType(reportEconomicsType);
        return RestApiResponse.ok(id);
    }

    /**
     * 批量删除行业类型(判断 关联数据是否可以删除)
     *
     * @param ids
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @Operation(summary = "批量删除行业类型")
    @SystemLogAnnotation(type = "行业类型", value = "批量删除行业类型")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteReportEconomicsType(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        reportEconomicsTypeService.deleteReportEconomicsType(ids);
        return RestApiResponse.ok();
    }

    /**
     * 查询行业类型详情
     *
     * @param id
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findById")
    @Operation(summary = "查询行业类型详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        ReportEconomicsType reportEconomicsType = reportEconomicsTypeService.findById(id);
        return RestApiResponse.ok(reportEconomicsType);
    }

    /**
     * 分页查询行业类型
     *
     * @param reportEconomicsTypeVo 行业类型 查询条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/findPageByQuery")
    @Operation(summary = "分页查询行业类型")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody ReportEconomicsTypeVo reportEconomicsTypeVo) {
        PageInfo<ReportEconomicsType> reportEconomicsType = reportEconomicsTypeService.findPageByQuery(reportEconomicsTypeVo);
        return RestApiResponse.ok(reportEconomicsType);
    }

    @PostMapping("/excel")
    @Operation(summary = "导出行业类型")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "excel')")
    // 这里的模块名称和子模块名称，请根据实际情况填写
    @ExportRespAnnotation(modType = "行业类型", sonMod = "行业类型", key = "jh_type_Table", isNotice = true, fileName = "行业类型")
    public RestApiResponse<?> excel(@RequestBody ReportEconomicsTypeVo reportEconomicsTypeVo) {
        List<ReportEconomicsType> reportEconomicsTypeList = reportEconomicsTypeService.findByQuery(reportEconomicsTypeVo);
        //这里对数据进行转换处理，如没使用字典的或关联其它表的数据。如在查询中已处理请忽略
        return RestApiResponse.ok(reportEconomicsTypeList);
    }

}
