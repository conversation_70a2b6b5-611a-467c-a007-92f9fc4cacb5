package com.jh.finance.service.fee;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.jh.finance.bean.fee.FeeReductionEvaluation;
import com.jh.finance.bean.fee.vo.FeeReductionEvaluationVo;


import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 减费惠企改革-一指评价Service接口
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
public interface FeeReductionEvaluationService {
    /**
     * 保存或更新减费惠企改革-一指评价
     *
     * @param feeReductionEvaluation 减费惠企改革-一指评价对象
     * @return String 减费惠企改革-一指评价ID
     * <AUTHOR>
     */
    String saveOrUpdateFeeReductionEvaluation(FeeReductionEvaluation feeReductionEvaluation);

    /**
     * 删除减费惠企改革-一指评价
     *
     * @param ids void 减费惠企改革-一指评价ID
     * <AUTHOR>
     */
    void deleteFeeReductionEvaluation(List<String> ids);

    /**
     * 查询减费惠企改革-一指评价详情
     *
     * @param id
     * @return FeeReductionEvaluation
     * <AUTHOR>
     */
    FeeReductionEvaluation findById(String id);

    /**
     * 分页查询减费惠企改革-一指评价
     *
     * @param feeReductionEvaluationVo
     * @return PageInfo<FeeReductionEvaluation>
     * <AUTHOR>
     */
    PageInfo<FeeReductionEvaluation> findPageByQuery(FeeReductionEvaluationVo feeReductionEvaluationVo);

    /**
     * 按条件导出查询减费惠企改革-一指评价
     *
     * @param feeReductionEvaluationVo
     * @return PageInfo<FeeReductionEvaluation>
     * <AUTHOR>
     */
    List<FeeReductionEvaluation> findByQuery(FeeReductionEvaluationVo feeReductionEvaluationVo);

    /**
     * 导入减费惠企改革-一指评价
     *
     * @param file
     * @param cover 是否覆盖 1 覆盖 0 不覆盖
     * @return
     * <AUTHOR>
     */
    void importFeeReductionEvaluationAsync(MultipartFile file, Integer cover);
}
