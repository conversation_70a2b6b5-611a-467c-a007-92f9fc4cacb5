package com.jh.finance.controller.report;

import java.util.List;
import java.util.Arrays;
import java.util.stream.Collectors;

import com.jh.finance.bean.report.ReportPlan;
import com.jh.finance.bean.report.vo.ReportPlanVo;
import com.jh.finance.enums.ReportPlanTypeEnum;
import com.jh.finance.service.report.ReportPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.github.pagehelper.PageInfo;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.controller.BaseController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

/**
 * 数据上报-计划管理Controller
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/report/plan")
@Tag(name = "数据上报-计划管理")
public class ReportPlanController extends BaseController {
    @Autowired
    private ReportPlanService reportPlanService;

    private static final String PER_PREFIX = "btn:report:plan:";

    /**
     * 新增计划管理数据
     *
     * @param reportPlan 计划管理数据数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @Operation(summary = "新增计划管理数据")
    @SystemLogAnnotation(type = "计划管理数据", value = "新增计划管理数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveReportPlan(@RequestBody ReportPlan reportPlan) {
        String id = reportPlanService.saveOrUpdateReportPlan(reportPlan);
        return RestApiResponse.ok(id);
    }

    /**
     * 修改计划管理数据
     *
     * @param reportPlan 计划管理数据数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @Operation(summary = "修改计划管理数据")
    @SystemLogAnnotation(type = "计划管理数据", value = "修改计划管理数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateReportPlan(@RequestBody ReportPlan reportPlan) {
        String id = reportPlanService.saveOrUpdateReportPlan(reportPlan);
        return RestApiResponse.ok(id);
    }

    /**
     * 批量删除计划管理数据(判断 关联数据是否可以删除)
     *
     * @param ids
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @Operation(summary = "批量删除计划管理数据")
    @SystemLogAnnotation(type = "计划管理数据", value = "批量删除计划管理数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteReportPlan(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        reportPlanService.deleteReportPlan(ids);
        return RestApiResponse.ok();
    }

    /**
     * 查询计划管理数据详情
     *
     * @param id
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findById")
    @Operation(summary = "查询计划管理数据详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        ReportPlan reportPlan = reportPlanService.findById(id);
        return RestApiResponse.ok(reportPlan);
    }

    /**
     * 分页查询计划管理数据
     *
     * @param reportPlanVo 计划管理数据 查询条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/findPageByQuery")
    @Operation(summary = "分页查询计划管理数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody ReportPlanVo reportPlanVo) {
        PageInfo<ReportPlan> reportPlan = reportPlanService.findPageByQuery(reportPlanVo);
        return RestApiResponse.ok(reportPlan);
    }

    /**
     * 查询政策类型枚举
     *
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/getPolicyTypes")
    @Operation(summary = "查询政策类型枚举")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "getPolicyTypes')")
    public RestApiResponse<?> getPolicyTypes() {
        List<Object> policyTypes = Arrays.stream(ReportPlanTypeEnum.values())
                .map(type -> {
                    java.util.Map<String, String> map = new java.util.HashMap<>();
                    map.put("code", type.getCode());
                    map.put("value", type.getValue());
                    return map;
                })
                .collect(Collectors.toList());
        return RestApiResponse.ok(policyTypes);
    }

}