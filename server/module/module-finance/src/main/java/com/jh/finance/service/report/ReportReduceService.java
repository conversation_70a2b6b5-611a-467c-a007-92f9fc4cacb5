package com.jh.finance.service.report;

import java.util.List;

import com.github.pagehelper.PageInfo;

import com.jh.finance.bean.report.ReportReduce;
import com.jh.finance.bean.report.vo.ReportReduceVo;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 减负降本数据Service接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface ReportReduceService {
    /**
     * 保存或更新减负降本数据
     *
     * @param reportReduce 减负降本数据对象
     * @return String 减负降本数据ID
     * <AUTHOR>
     */
    String saveOrUpdateReportReduce(ReportReduce reportReduce);

    /**
     * 删除减负降本数据
     *
     * @param ids void 减负降本数据ID
     * <AUTHOR>
     */
    void deleteReportReduce(List<String> ids);

    /**
     * 查询减负降本数据详情
     *
     * @param id
     * @return ReportReduce
     * <AUTHOR>
     */
    ReportReduce findById(String id);

    /**
     * 分页查询减负降本数据
     *
     * @param reportReduceVo
     * @return PageInfo<ReportReduce>
     * <AUTHOR>
     */
    PageInfo<ReportReduce> findPageByQuery(ReportReduceVo reportReduceVo);

    /**
     * 按条件导出查询减负降本数据
     *
     * @param reportReduceVo
     * @return PageInfo<ReportReduce>
     * <AUTHOR>
     */
    List<ReportReduce> findByQuery(ReportReduceVo reportReduceVo);

    /**
     * 导入减负降本数据
     *
     * @param file
     * @param cover 是否覆盖 1 覆盖 0 不覆盖
     * @return
     * <AUTHOR>
     */
    void importReportReduceAsync(MultipartFile file, Integer cover);
    /**
     * 提交减负降本数据
     * 提交当前用户所有未提交状态的数据
     */
    void submitReportReduce();

    /**
     * 提交指定的减负降本数据
     *
     * @param id 减负降本数据ID
     * <AUTHOR>
     */
    void submitReportReduceById(String id);

    /**
     * 审核减负降本数据
     *
     * @param reportReduceVo 包含审核状态和审核意见的VO对象
     * <AUTHOR>
     */
    void auditReportReduce(ReportReduceVo reportReduceVo);
}
