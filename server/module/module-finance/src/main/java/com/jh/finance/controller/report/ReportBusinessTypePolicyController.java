package com.jh.finance.controller.report;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.finance.bean.report.ReportBusinessTypePolicy;
import com.jh.finance.bean.report.vo.ReportBusinessTypePolicyVo;
import com.jh.finance.service.report.ReportBusinessTypePolicyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;


import com.jh.common.bean.RestApiResponse;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.controller.BaseController;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

/**
 * 业务类型政策Controller
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@RestController
@RequestMapping("/report/policy/type")
@Tag(name = "业务类型政策")
public class ReportBusinessTypePolicyController extends BaseController {
    @Autowired
    private ReportBusinessTypePolicyService reportBusinessTypePolicyService;

    private static final String PER_PREFIX = "btn:report:policy:type:";

    /**
     * 新增业务类型政策
     *
     * @param reportBusinessTypePolicy 业务类型政策数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @Operation(summary = "新增业务类型政策")
    @SystemLogAnnotation(type = "业务类型政策", value = "新增业务类型政策")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveReportBusinessTypePolicy(@RequestBody ReportBusinessTypePolicy reportBusinessTypePolicy) {
        String id = reportBusinessTypePolicyService.saveOrUpdateReportBusinessTypePolicy(reportBusinessTypePolicy);
        return RestApiResponse.ok(id);
    }

    /**
     * 修改业务类型政策
     *
     * @param reportBusinessTypePolicy 业务类型政策数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @Operation(summary = "修改业务类型政策")
    @SystemLogAnnotation(type = "业务类型政策", value = "修改业务类型政策")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateReportBusinessTypePolicy(@RequestBody ReportBusinessTypePolicy reportBusinessTypePolicy) {
        String id = reportBusinessTypePolicyService.saveOrUpdateReportBusinessTypePolicy(reportBusinessTypePolicy);
        return RestApiResponse.ok(id);
    }

    /**
     * 批量删除业务类型政策(判断 关联数据是否可以删除)
     *
     * @param ids
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @Operation(summary = "批量删除业务类型政策")
    @SystemLogAnnotation(type = "业务类型政策", value = "批量删除业务类型政策")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteReportBusinessTypePolicy(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        reportBusinessTypePolicyService.deleteReportBusinessTypePolicy(ids);
        return RestApiResponse.ok();
    }

    /**
     * 查询业务类型政策详情
     *
     * @param id
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findById")
    @Operation(summary = "查询业务类型政策详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        ReportBusinessTypePolicy reportBusinessTypePolicy = reportBusinessTypePolicyService.findById(id);
        return RestApiResponse.ok(reportBusinessTypePolicy);
    }

    /**
     * 分页查询业务类型政策
     *
     * @param reportBusinessTypePolicyVo 业务类型政策 查询条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/findPageByQuery")
    @Operation(summary = "分页查询业务类型政策")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody ReportBusinessTypePolicyVo reportBusinessTypePolicyVo) {
        PageInfo<ReportBusinessTypePolicy> reportBusinessTypePolicy = reportBusinessTypePolicyService.findPageByQuery(reportBusinessTypePolicyVo);
        return RestApiResponse.ok(reportBusinessTypePolicy);
    }

    /**
     * 根据业务类型ID查询政策并按TYPE分组
     *
     * @param businessTypeId 业务类型ID
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findByBusinessTypeId")
    @Operation(summary = "根据业务类型ID查询政策并按TYPE分组")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findByBusinessTypeId')")
    public RestApiResponse<?> findByBusinessTypeId(@RequestParam("businessTypeId") String businessTypeId) {
        ReportBusinessTypePolicyVo vo = reportBusinessTypePolicyService.findByBusinessTypeIdGrouped(businessTypeId);
        return RestApiResponse.ok(vo);
    }

}
