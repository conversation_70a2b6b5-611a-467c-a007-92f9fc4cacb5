package com.jh.finance.bean.report.vo;


import com.jh.finance.bean.report.ReportReduce;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 减负降本数据Vo
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Schema(description = "ReportReduceVo")
@Data
public class ReportReduceVo extends ReportReduce {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;

    // 角色权限相关字段
    private boolean hasOrgRole = false;
    private boolean hasRegionRole = false;
    private boolean hasAdminRole = false;
    private boolean hasFinanceRole = false;
    private boolean hasScienceRole = false;

    /**
     * 用户行政区划代码
     */
    private String areaCode;

    /**
     * 地区代码，用于筛选
     */
    @Schema(description = "地区代码")
    private String code;


    /**
     * 审核状态 1:通过 0:驳回
     */
    @Schema(description = "审核状态 1:通过 0:驳回")
    private Integer auditStatus;

    /**
     * 审核意见
     */
    @Schema(description = "审核意见")
    private String advice;

}