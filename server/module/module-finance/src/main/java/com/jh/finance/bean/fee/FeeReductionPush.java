package com.jh.finance.bean.fee;

import java.util.Date;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.jh.common.bean.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

/**
 * 减费惠企改革-政策推送对象 fee_reduction_push
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Table(name = "fee_reduction_push")
@Schema(description = "减费惠企改革-政策推送")
@Data
public class FeeReductionPush extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "ORG_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "统一社会信用代码")
    private String orgCode;
    @Column(name = "ENTERPRISE_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "企业名称")
    private String enterpriseName;
    @Column(name = "CONTACT_PERSON")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "联系人")
    private String contactPerson;
    @Column(name = "CONTACT_PHONE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "联系人手机")
    private String contactPhone;
    @Column(name = "EQUIPMENT_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "设备名称")
    private String equipmentName;
    @Column(name = "EQUIPMENT_COUNT")
    @ColumnType(jdbcType = JdbcType.BIGINT)
    @Schema(description = "设备数量")
    private Long equipmentCount;
    @Column(name = "POLICY_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "政策ID")
    private String policyId;
    @Column(name = "IS_PUSH")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "是否推送(1是 0否)")
    private Integer isPush;
    @Column(name = "PUSH_TIME")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @Schema(description = "推送时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date pushTime;

}
