package com.jh.finance.controller.fee;

import java.util.List;

import com.jh.finance.bean.fee.FeeStandard;
import com.jh.finance.bean.fee.vo.FeeStandardVo;
import com.jh.finance.bean.fee.vo.FeeStandardDataPushRequest;
import com.jh.finance.service.fee.FeeStandardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.jh.common.bean.RestApiResponse;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.controller.BaseController;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

/**
 * 收费信息Controller
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@RestController
@RequestMapping("/fee/user/standard")
@Tag(name = "收费信息")
public class FeeStandardController extends BaseController {
    @Autowired
    private FeeStandardService feeStandardService;

    private static final String PER_PREFIX = "btn:fee:standard:";

    /**
     * 新增收费信息
     *
     * @param feeStandard 收费信息数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @Operation(summary = "新增收费信息")
    @SystemLogAnnotation(type = "收费信息", value = "新增收费信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveFeeStandard(@RequestBody FeeStandard feeStandard) {
        String id = feeStandardService.saveOrUpdateFeeStandard(feeStandard);
        return RestApiResponse.ok(id);
    }

    /**
     * 修改收费信息
     *
     * @param feeStandard 收费信息数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @Operation(summary = "修改收费信息")
    @SystemLogAnnotation(type = "收费信息", value = "修改收费信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateFeeStandard(@RequestBody FeeStandard feeStandard) {
        String id = feeStandardService.saveOrUpdateFeeStandard(feeStandard);
        return RestApiResponse.ok(id);
    }

    /**
     * 批量删除收费信息(判断 关联数据是否可以删除)
     *
     * @param ids
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @Operation(summary = "批量删除收费信息")
    @SystemLogAnnotation(type = "收费信息", value = "批量删除收费信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteFeeStandard(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        feeStandardService.deleteFeeStandard(ids);
        return RestApiResponse.ok();
    }

    /**
     * 查询收费信息详情
     *
     * @param id
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findById")
    @Operation(summary = "查询收费信息详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        FeeStandard feeStandard = feeStandardService.findById(id);
        return RestApiResponse.ok(feeStandard);
    }

    /**
     * 分页查询收费信息
     *
     * @param feeStandardVo 收费信息 查询条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/findPageByQuery")
    @Operation(summary = "分页查询收费信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody FeeStandardVo feeStandardVo) {
        PageInfo<FeeStandard> feeStandard = feeStandardService.findPageByQuery(feeStandardVo);
        return RestApiResponse.ok(feeStandard);
    }

    /**
     * 接收外部推送的收费数据
     *
     * @param feeStandardDataPushRequest 外部推送的收费数据
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/receivePushData")
    @Operation(summary = "接收外部推送的收费数据")
    @SystemLogAnnotation(type = "收费信息", value = "接收外部推送的收费数据")
    public RestApiResponse<?> receivePushData(@RequestBody FeeStandardDataPushRequest feeStandardDataPushRequest) {
        String result = feeStandardService.processPushData(feeStandardDataPushRequest);
        return RestApiResponse.ok(result);
    }

    /**
     * 根据机构代码查询检验检测服务项目（标准类）
     *
     * @param orgCode 机构代码
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findStandardByOrgCode")
    @Operation(summary = "根据机构代码查询检验检测服务项目（标准类）")
    public RestApiResponse<?> findStandardByOrgCode(@RequestParam("orgCode") String orgCode) {
        List<FeeStandard> feeStandardList = feeStandardService.findStandardByOrgCode(orgCode);
        return RestApiResponse.ok(feeStandardList);
    }

}
