<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.finance.dao.report.ReportAdviceMapper">

    <!-- 根据申请ID查询审批意见 -->
    <select id="findByApplyId" resultType="com.jh.finance.bean.report.ReportAdvice">
        SELECT *
        FROM report_advice
        WHERE APPLY_ID = #{applyId}
        AND YN = 1
        ORDER BY CREATE_TIME DESC
    </select>
    
    <!-- 根据申请ID和节点查询审批意见 -->
    <select id="findByApplyIdAndStage" resultType="com.jh.finance.bean.report.ReportAdvice">
        SELECT *
        FROM report_advice
        WHERE APPLY_ID = #{applyId}
        AND STAGE = #{stage}
        AND YN = 1
        LIMIT 1
    </select>

</mapper> 