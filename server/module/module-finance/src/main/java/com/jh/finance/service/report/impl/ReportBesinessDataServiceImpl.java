package com.jh.finance.service.report.impl;

import com.jh.common.util.AppUserUtil;
import com.jh.constant.FlowConstant;
import com.jh.finance.bean.report.*;
import com.jh.finance.bean.report.vo.ReportBesinessDataVo;
import com.jh.finance.bean.report.vo.ReportStatisticsQueryVo;
import com.jh.finance.bean.report.vo.ReportSummaryVo;
import com.jh.finance.constant.RoleConstant;
//import com.jh.finance.constant.FlowConstant;
import com.jh.finance.dao.report.*;
import com.jh.finance.enums.FeeReportStatusEnum;
import com.jh.finance.service.report.ReportAdviceService;
import com.jh.finance.service.report.ReportBasinessService;
import com.jh.finance.service.report.ReportBesinessDataService;
import com.jh.utils.ImportService;
import org.springframework.beans.factory.annotation.Value;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Set;
import java.util.HashSet;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;
import com.jh.common.constant.CommonConstant;
import com.jh.common.util.AppUserUtil;
import com.jh.constant.FlowConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import static com.github.pagehelper.page.PageMethod.orderBy;
import static com.jh.common.controller.BaseController.getUserRoleList;

import java.util.Date;

import com.jh.common.bean.SysRole;
import com.jh.common.bean.SysUser;
import com.jh.sys.dao.SysUserMapper;

import java.math.BigDecimal;

import com.jh.finance.bean.report.vo.ReportStatisticsVo;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.Calendar;

/**
 * 收费统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Service
@Transactional(readOnly = true)
public class ReportBesinessDataServiceImpl extends BaseServiceImpl<ReportBesinessDataMapper, ReportBesinessData> implements ReportBesinessDataService {

    private static final Logger logger = LoggerFactory.getLogger(ReportBesinessDataServiceImpl.class);

    @Autowired
    private ReportBesinessDataMapper reportBesinessDataMapper;

    @Autowired
    private ReportBasinessMapper reportBasinessMapper;

    @Autowired
    private ReportAdviceMapper reportAdviceMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private ReportBusinessTypeMapper reportBusinessTypeMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     * 保存或更新收费统计
     *@param reportBesinessData 收费统计对象
     *@return String 收费统计ID
     *<AUTHOR>
     */
    public String saveOrUpdateReportBesinessData(ReportBesinessData reportBesinessData) {
        if (reportBesinessData == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(reportBesinessData.getId())) {
            //新增
            reportBesinessData.setId(UUIDUtils.getUUID());
            reportBesinessDataMapper.insertSelective(reportBesinessData);
        } else {
            //TODO 做判断后方能执行修改 一般判断是否是自己的数据
            //避免页面传入修改
            reportBesinessData.setYn(null);
            reportBesinessDataMapper.updateByPrimaryKeySelective(reportBesinessData);
        }
        return reportBesinessData.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     * 删除收费统计
     *@param ids void 收费统计ID
     *<AUTHOR>
     */
    public void deleteReportBesinessData(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除 一般判断是否是自己的数据
            ReportBesinessData reportBesinessData = reportBesinessDataMapper.selectByPrimaryKey(id);
            if (reportBesinessData == null) {
                throw new ServiceException("非法请求");
            }

            //逻辑删除
            ReportBesinessData temreportBesinessData = new ReportBesinessData();
            temreportBesinessData.setYn(CommonConstant.FLAG_NO);
            temreportBesinessData.setId(reportBesinessData.getId());
            reportBesinessDataMapper.updateByPrimaryKeySelective(temreportBesinessData);
        }
    }

    /**
     * 查询收费统计详情
     *
     * @param id
     * @return ReportBesinessData
     * <AUTHOR>
     */
    @Override
    public ReportBesinessDataVo findById(String id) {
        // TODO 做判断后方能反回数据 一般判断是否是自己的数据
        ReportBesinessData reportBesinessData = reportBesinessDataMapper.selectByPrimaryKey(id);

        if (reportBesinessData != null) {
            // 使用Vo对象返回，包含子表数据
            ReportBesinessDataVo vo = new ReportBesinessDataVo();

            // 复制主表属性
            vo.setId(reportBesinessData.getId());
            vo.setFlowName(reportBesinessData.getFlowName());
            vo.setFlowKey(reportBesinessData.getFlowKey());
            vo.setActNodeKey(reportBesinessData.getActNodeKey());
            vo.setActNodeName(reportBesinessData.getActNodeName());
            vo.setUserId(reportBesinessData.getUserId());
            vo.setTypeId(reportBesinessData.getTypeId());
            vo.setInputTime(reportBesinessData.getInputTime());
            vo.setIsCommit(reportBesinessData.getIsCommit());
            vo.setYear(reportBesinessData.getYear());
            vo.setQuarter(reportBesinessData.getQuarter());
            vo.setMonth(reportBesinessData.getMonth());
            vo.setCountMoney(reportBesinessData.getCountMoney());

            // 获取子表数据
            List<ReportBasiness> reportBasinessList = reportBasinessMapper.findByDataId(id);
            List<ReportAdvice> reportAdviceList = reportAdviceMapper.findByApplyId(id);
            vo.setReportAdviceList(reportAdviceList);

            vo.setReportBasinessList(reportBasinessList);


            return vo;
        }

        return null;
    }


    /**
     * 分页查询收费统计
     *
     * @param vo
     * @return PageInfo<ReportBesinessData>
     * <AUTHOR>
     */
    @Override
    public PageInfo<ReportBesinessData> findPageByQuery(ReportBesinessDataVo vo) {
        // 分页设置
        PageHelper.startPage(vo.getPageNum(), vo.getPageSize());

        // 检查用户角色权限
        checkUserRole(vo);

        List<ReportBesinessData> reportBesinessDataList = reportBesinessDataMapper.query(vo);

        return new PageInfo<ReportBesinessData>(reportBesinessDataList);
    }

    /**
     * 收费统计上报
     *
     * @param vo
     * @return
     */
    @Override
    @Transactional(readOnly = false)
    public void feeReport(ReportBesinessDataVo vo) {

        if (vo.getFlowKey() == null) {
            throw new ServiceException("流程KEY不能为空");
        }

        String flowKey = vo.getFlowKey();
        //根据flowKey匹配flowName
        String flowName = "";
        if (FlowConstant.REPORT_FEE_KEY.equals(flowKey)) {
            flowName = FlowConstant.REPORT_FEE;
        } else if (FlowConstant.REPORT_FEE_POLICY_KEY.equals(flowKey)) {
            flowName = FlowConstant.REPORT_FEE_POLICY;
        } else {
            throw new ServiceException("无效的流程KEY");
        }

        // 校验收费类型是否为空
        if (vo.getTypeId() == null) {
            throw new ServiceException("收费类型不能为空");
        }

        // 校验收费类型是否存在且为父级类型
        ReportBusinessType parentType = reportBusinessTypeMapper.selectByPrimaryKey(vo.getTypeId());
        if (parentType == null) {
            throw new ServiceException("收费类型不存在");
        }
        if (parentType.getParentId() != 0) {
            throw new ServiceException("收费类型必须是一级分类");
        }

        // 校验年份和月份不能为空
        if (vo.getYear() == null) {
            throw new ServiceException("年份不能为空");
        }
        if (vo.getMonth() == null) {
            throw new ServiceException("月份不能为空");
        }

        // 检查月份值是否有效（1-12）
        if (vo.getMonth() < 1 || vo.getMonth() > 12) {
            throw new ServiceException("月份值必须在1到12之间");
        }

        String userId = AppUserUtil.getCurrentUserId();

        // 判断是新增还是更新
        boolean isUpdate = !StringUtils.isEmpty(vo.getId());
        ReportBesinessData existingData = null;

        if (!isUpdate) {
            // 只有新增时才需要检查月份数据是否存在
            // 查询用户本年度已提交的月份数据（用于月份顺序检查）
            List<ReportBesinessData> committedReports = reportBesinessDataMapper.findCommittedReportsByYear(userId, vo.getYear(), vo.getTypeId(), vo.getFlowKey());
            // 查询用户本年度当前月份所有数据（用于判重）
            List<ReportBesinessData> existingMonthData = reportBesinessDataMapper.findReportsByYearAndMonth(userId, vo.getYear(), vo.getMonth(), vo.getTypeId(), vo.getFlowKey());
            if (existingMonthData != null && !existingMonthData.isEmpty()) {
                throw new ServiceException("第" + vo.getMonth() + "月数据已存在，不能重复提交");
            }

            // 如果当前上报的不是第1月，需要检查前面的月份是否已上报
            if (vo.getMonth() > 1) {
                // 获取已上报的所有月份
                Set<Integer> reportedMonths = new HashSet<>();
                for (ReportBesinessData report : committedReports) {
                    reportedMonths.add(report.getMonth());
                }

                // 检查前面的月份是否都已上报
                for (int i = 1; i < vo.getMonth(); i++) {
                    if (!reportedMonths.contains(i)) {
                        throw new ServiceException("必须按月份顺序上报数据，请先上报第" + i + "月数据");
                    }
                }
            }
        } else {
            // 如果是更新，则查询现有数据
            existingData = reportBesinessDataMapper.selectByPrimaryKey(vo.getId());
            if (existingData == null) {
                throw new ServiceException("要更新的数据不存在");
            }

            // 检查数据状态是否允许更新
            if (FeeReportStatusEnum.YCL.getCode().equals(existingData.getActNodeKey())) {
                throw new ServiceException("该数据已处理完成，不能再次修改");
            }
        }

        String actNodeName = vo.getIsCommit() == 1 ? FeeReportStatusEnum.DCL.getValue() : FeeReportStatusEnum.DTC.getValue();
        String actNodeKey = vo.getIsCommit() == 1 ? FeeReportStatusEnum.DCL.getCode() : FeeReportStatusEnum.DTC.getCode();
        String nextFlow = vo.getIsCommit() == 1 ? "提交" : "保存";

        // 计算总金额
        BigDecimal totalAmount = BigDecimal.ZERO;

        // 处理子表数据
        if (vo.getReportBasinessList() != null && !vo.getReportBasinessList().isEmpty()) {

            for (ReportBasiness basiness : vo.getReportBasinessList()) {
                // 校验业务类型ID
                if (basiness.getBusinesstTypeId() == null) {
                    throw new ServiceException("业务类型ID不能为空");
                }

                // 验证金额范围，防止数据库插入错误
                if (basiness.getMoney() != null) {
                    // 假设MONEY字段定义为DECIMAL(10,2)
                    if (basiness.getMoney().compareTo(new BigDecimal("99999999.99")) > 0) {
                        throw new ServiceException("业务金额超出系统允许的范围");
                    }
                    if (basiness.getMoney().compareTo(BigDecimal.ZERO) < 0) {
                        throw new ServiceException("业务金额不能为负数");
                    }
                    totalAmount = totalAmount.add(basiness.getMoney());
                }

                // 设置或更新子表记录信息
                if (StringUtils.isEmpty(basiness.getId())) {
                    // 新增子表记录
                    basiness.setId(UUIDUtils.getUUID());
                }

                // 无论新增还是更新，都需要设置这些字段
                basiness.setCreateUser(AppUserUtil.getCurrentUserName());
                basiness.setCreateUserNickname(AppUserUtil.getCurrentRealName());
                basiness.setUpdateUser(AppUserUtil.getCurrentUserName());
                basiness.setUpdateUserNickname(AppUserUtil.getCurrentRealName());
                basiness.setCreateTime(new Date());
                basiness.setUpdateTime(new Date());
                basiness.setYn(CommonConstant.FLAG_YES);
            }
        }

        ReportBesinessData data;

        if (isUpdate) {
            // 更新现有数据
            data = existingData;

            // 更新主表字段
            data.setFlowName(flowName);
            data.setFlowKey(flowKey);
            data.setActNodeKey(actNodeKey);
            data.setActNodeName(actNodeName);
            data.setIsCommit(vo.getIsCommit());
            data.setCountMoney(totalAmount);
            data.setUpdateTime(new Date());

            // 更新主表
            reportBesinessDataMapper.updateByPrimaryKeySelective(data);

            // 删除原有子表数据
            Example example = new Example(ReportBasiness.class);
            example.createCriteria().andEqualTo("dataId", data.getId());
            reportBasinessMapper.deleteByExample(example);

            // 插入新的子表数据
            if (vo.getReportBasinessList() != null && !vo.getReportBasinessList().isEmpty()) {
                for (ReportBasiness basiness : vo.getReportBasinessList()) {
                    basiness.setDataId(data.getId());
                }
                reportBasinessMapper.insertList(vo.getReportBasinessList());
            }
        } else {
            // 新增数据
            data = new ReportBesinessData();
            data.setId(UUIDUtils.getUUID());
            data.setFlowName(flowName);
            data.setFlowKey(flowKey);
            data.setActNodeKey(actNodeKey);
            data.setActNodeName(actNodeName);
            data.setUserId(AppUserUtil.getCurrentUserId());
            data.setTypeId(vo.getTypeId());
            data.setInputTime(new Date());
            data.setIsCommit(vo.getIsCommit());
            data.setYear(vo.getYear());
            data.setQuarter(vo.getQuarter());
            data.setMonth(vo.getMonth());
            data.setCountMoney(totalAmount);

            // 插入主表数据
            reportBesinessDataMapper.insertSelective(data);

            // 插入子表数据
            if (vo.getReportBasinessList() != null && !vo.getReportBasinessList().isEmpty()) {
                for (ReportBasiness basiness : vo.getReportBasinessList()) {
                    basiness.setDataId(data.getId());
                }
                reportBasinessMapper.insertList(vo.getReportBasinessList());
            }
        }

        // 添加审批流程记录
        ReportAdvice advice = new ReportAdvice();
        advice.setId(UUIDUtils.getUUID());
        advice.setApplyId(data.getId());
        advice.setFlowName(flowName);
        advice.setStage(actNodeKey);
        advice.setOperate(nextFlow);
        advice.setAdvice(nextFlow);
        reportAdviceMapper.insertSelective(advice);
    }

    /**
     * 收费统计审核
     *
     * @param vo 包含审核参数的VO对象
     */
    @Override
    @Transactional(readOnly = false)
    public void feeAudit(ReportBesinessDataVo vo) {

        String flowKey = vo.getFlowKey();
        //根据flowKey匹配flowName
        String flowName = "";
        if (FlowConstant.REPORT_FEE_KEY.equals(flowKey)) {
            flowName = FlowConstant.REPORT_FEE;
        } else if (FlowConstant.REPORT_FEE_POLICY_KEY.equals(flowKey)) {
            flowName = FlowConstant.REPORT_FEE_POLICY;
        } else {
            throw new ServiceException("无效的流程KEY");
        }

        // 1. 验证参数
        if (StringUtils.isEmpty(vo.getId())) {
            throw new ServiceException("主表ID不能为空");
        }
        if (vo.getAuditStatus() == null) {
            throw new ServiceException("审核状态不能为空");
        }

        // 2. 验证数据是否存在
        ReportBesinessDataVo data = findById(vo.getId());
        if (data == null) {
            throw new ServiceException("数据不存在");
        }

        // 3. 验证状态是否正确
        if (!FeeReportStatusEnum.DCL.getCode().equals(data.getActNodeKey())) {
            throw new ServiceException("当前状态不允许进行审核操作");
        }

        String nextNodeKey = vo.getAuditStatus() == 1 ? FeeReportStatusEnum.YCL.getCode() : FeeReportStatusEnum.DTC.getCode();
        String nextNodeName = vo.getAuditStatus() == 1 ? FeeReportStatusEnum.YCL.getValue() : FeeReportStatusEnum.DTC.getValue();
        String operateDesc = vo.getAuditStatus() == 1 ? "通过" : "驳回";

        // 4. 添加审批记录
        ReportAdvice reportAdvice = new ReportAdvice();
        reportAdvice.setId(UUIDUtils.getUUID());
        reportAdvice.setApplyId(vo.getId());

        reportAdvice.setFlowName(flowName);

        reportAdvice.setStage(nextNodeKey);
        reportAdvice.setAuditStatus(vo.getAuditStatus());
        reportAdvice.setOperate(operateDesc);
        reportAdvice.setAdvice(vo.getAdvice());
        reportAdviceMapper.insertSelective(reportAdvice);

        // 5. 更新主表状态
        ReportBesinessData updateData = new ReportBesinessData();
        updateData.setId(vo.getId());
        updateData.setActNodeKey(nextNodeKey);
        updateData.setActNodeName(nextNodeName);
        reportBesinessDataMapper.updateByPrimaryKeySelective(updateData);
    }

    /**
     * 获取收费数据总计
     *
     * @param vo 查询参数
     * @return 统计结果
     */
    @Override
    public List<ReportStatisticsQueryVo> getDataTotal(ReportBesinessDataVo vo) {
        // 检查用户角色权限
        checkUserRole(vo);

        // 设置流程KEY
        if ("1".equals(vo.getQueryType())) {
            vo.setFlowKey(FlowConstant.REPORT_FEE_KEY);
        } else if ("2".equals(vo.getQueryType())) {
            vo.setFlowKey(FlowConstant.REPORT_FEE_POLICY_KEY);
        } else {
            throw new ServiceException("查询类型参数错误");
        }

        List<ReportStatisticsQueryVo> result = reportBesinessDataMapper.getDataTotal(vo);

        return result != null ? result : new ArrayList<>();
    }

    /**
     * 获取减负降本数据总计
     *
     * @param vo 查询参数
     * @return 统计结果
     */
    @Override
    public List<ReportStatisticsQueryVo> getReduceDataTotal(ReportBesinessDataVo vo) {
        // 检查用户角色权限
        checkUserRole(vo);

        List<ReportStatisticsQueryVo> result = reportBesinessDataMapper.getReduceDataTotal(vo);

        return result != null ? result : new ArrayList<>();
    }


    /**
     * 检查用户角色权限
     *
     * @param vo 查询参数对象
     */
    private void checkUserRole(ReportBesinessDataVo vo) {
        // 获取当前用户ID
        String userId = AppUserUtil.getCurrentUserId();
        vo.setUserId(userId);

        // 获取当前用户角色列表
        List<SysRole> roleList = getUserRoleList();

        // 判断用户角色
        boolean hasOrgRole = false;
        boolean hasRegionRole = false;
        boolean hasAdminRole = false;
        boolean hasFinanceRole = false;
        boolean hasScienceRole = false;

        // 检查用户是否有机构角色或地市角色
        if (roleList != null && !roleList.isEmpty()) {
            for (SysRole role : roleList) {
                String roleCode = role.getRoleCode();
                if (RoleConstant.ROLE_ORG.equals(roleCode)) {
                    hasOrgRole = true;
                } else if (RoleConstant.ROLE_REGION.equals(roleCode)) {
                    hasRegionRole = true;
                } else if (RoleConstant.ROLE_ADMIN.equals(roleCode) || RoleConstant.ROLE_SUPERADMIN.equals(roleCode)) {
                    hasAdminRole = true;
                } else if (RoleConstant.ROLE_FINANCE.equals(roleCode)) {
                    hasFinanceRole = true;
                } else if (RoleConstant.ROLE_SCIENCE.equals(roleCode)) {
                    hasScienceRole = true;
                }
            }
        }

        // 设置角色标志
        vo.setHasOrgRole(hasOrgRole);
        vo.setHasRegionRole(hasRegionRole);
        vo.setHasAdminRole(hasAdminRole);
        vo.setHasFinanceRole(hasFinanceRole);
        vo.setHasScienceRole(hasScienceRole);

        // 如果是地市角色、财务处或科技处角色，获取行政区划代码
        if (hasRegionRole || hasFinanceRole || hasScienceRole) {
            SysUser user = new SysUser();
            user.setId(userId);
            SysUser currentUser = sysUserMapper.selectOne(user);
            if (currentUser != null && currentUser.getAreaCode() != null) {
                String areaCode = currentUser.getAreaCode();
                // 如果是以"00"结尾的地市代码，去掉末尾的"00"
                if (areaCode.endsWith("00")) {
                    areaCode = areaCode.substring(0, areaCode.length() - 2);
                }
                vo.setAreaCode(areaCode);
            }
        }
    }

    /**
     * 数据上报情况查询
     * 根据地区代码返回不同层级的数据：
     * - 传入330000：返回浙江省内各市的汇总数据
     * - 传入具体市代码：返回该市下各机构的详细数据（支持分页）
     *
     * @param vo 查询参数（年份、季度、地区代码、分页参数）
     * @return 统计结果
     */
    @Override
    public PageInfo<ReportSummaryVo> getReportSummary(ReportBesinessDataVo vo) {
        // 保存用户传入的地区代码
        String queryAreaCode = vo.getCode();
        
        // 检查用户角色权限
        checkUserRole(vo);

        // 设置默认地区代码为浙江省
        if (StringUtils.isEmpty(queryAreaCode)) {
            queryAreaCode = "330000";
        }

        // 重新设置查询用的地区代码
        vo.setAreaCode(queryAreaCode);

        List<ReportSummaryVo> result = new ArrayList<>();

        // 判断查询层级：330000为省级，查询各市数据；其他为市级，查询各机构数据
        if ("330000".equals(queryAreaCode) || "33".equals(queryAreaCode)) {
            // 查询省内各市的汇总数据（不分页）
            result = reportBesinessDataMapper.getReportSummaryByCity(vo);
            return new PageInfo<>(result);
        } else {
            // 查询具体市下各机构的详细数据（支持分页）
            // 设置分页参数，如果没有传入则使用默认值
            if (vo.getPageNum() == null || vo.getPageNum() <= 0) {
                vo.setPageNum(1);
            }
            if (vo.getPageSize() == null || vo.getPageSize() <= 0) {
                vo.setPageSize(10);
            }
            
            // 开启分页
            PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
            result = reportBesinessDataMapper.getReportSummaryByOrg(vo);
            return new PageInfo<>(result);
        }
    }
}
