package com.jh.finance.bean.report.vo;

import com.jh.finance.bean.report.ReportAdvice;
import com.jh.finance.bean.report.ReportBasiness;
import com.jh.finance.bean.report.ReportBesinessData;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;


/**
 * 收费统计Vo
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Schema(description = "ReportBesinessDataVo")
@Data
public class ReportBesinessDataVo extends ReportBesinessData {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;

    private List<ReportBasiness> reportBasinessList;

    private List<ReportAdvice> reportAdviceList;
    
    /** 按业务类型名称分组的业务数据（用于消费减负流程） */
    @Schema(description = "按业务类型名称分组的业务数据")
    private List<Map<String, Object>> groupedBasinessList;

    /** 是否有机构角色 */
    private Boolean hasOrgRole;
    
    /** 是否有地市角色 */
    private Boolean hasRegionRole;
    
    /** 是否有管理员角色 */
    private Boolean hasAdminRole;
    
    /** 是否有财务处角色 */
    private Boolean hasFinanceRole;
    
    /** 是否有科技处角色 */
    private Boolean hasScienceRole;
    
    /** 用户行政区划代码 */
    private String areaCode;
    
    /** 年份开始值 */
    @Schema(description = "年份开始值")
    private String yearStart;
    
    /** 年份结束值 */
    @Schema(description = "年份结束值")
    private String yearEnd;

    /** 季度开始 */
    @Schema(description = "季度开始")
    private Integer quarterStart;

    /** 季度结束 */
    @Schema(description = "季度结束")
    private Integer quarterEnd;
    
    /** 地区代码，用于筛选 */
    @Schema(description = "地区代码")
    private String code;
    
    /** 审核状态 1:通过 0:驳回 */
    @Schema(description = "审核状态 1:通过 0:驳回")
    private Integer auditStatus;
    
    /** 审核意见 */
    @Schema(description = "审核意见")
    private String advice;
    
    private String queryType;
}