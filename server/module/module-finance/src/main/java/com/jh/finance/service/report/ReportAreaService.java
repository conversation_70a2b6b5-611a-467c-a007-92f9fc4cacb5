package com.jh.finance.service.report;

import java.util.List;
import com.github.pagehelper.PageInfo;
import com.jh.finance.bean.report.ReportArea;
import com.jh.finance.bean.report.vo.ReportAreaVo;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;
/**
 * 浙江省行政区划Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface ReportAreaService{
	/**
	 * 保存或更新浙江省行政区划
	 *@param reportArea 浙江省行政区划对象
	 *@return String 浙江省行政区划ID
	 *<AUTHOR>
	 */
	String saveOrUpdateReportArea(ReportArea reportArea);
	
	/**
	 * 删除浙江省行政区划
	 *@param ids void 浙江省行政区划ID
	 *<AUTHOR>
	 */
	void deleteReportArea(List<String> ids);

	/**
	 * 查询浙江省行政区划详情
	 *@param id
	 *@return ReportArea
	 *<AUTHOR>
	 */
	ReportArea findById(String id);

	/**
	 * 分页查询浙江省行政区划
	 *@param reportAreaVo
	 *@return PageInfo<ReportArea>
	 *<AUTHOR>
	 */
	PageInfo<ReportArea> findPageByQuery(ReportAreaVo reportAreaVo);
	
	/**
     * 查询省级数据
     * @return List<ReportArea>
     */
    List<ReportArea> findProvinceList();
    
    /**
     * 查询市级数据
     * @return List<ReportArea>
     */
    List<ReportArea> findCityList();
    
    /**
     * 查询区县级数据
     * @return List<ReportArea>
     */
    List<ReportArea> findCountyList();
    
    /**
     * 根据父级代码查询下级区划
     * @param parentCode 父级代码
     * @return List<ReportArea>
     */
    List<ReportArea> findByParentCode(String parentCode);
    
    /**
     * 根据级别查询区划
     * @param level 级别
     * @return List<ReportArea>
     */
    List<ReportArea> findByLevel(Long level);
    
    /**
     * 获取省市区县树形结构
     * @return List<ReportArea>
     */
    List<ReportArea> getAreaTree();
    
    /**
     * 根据用户ID获取用户下属的行政区划列表（包含本级和下属的层级结构）
     * @param userId 用户ID
     * @return List<ReportArea>
     */
    List<ReportArea> getUserSubordinateAreas(String userId);

}
