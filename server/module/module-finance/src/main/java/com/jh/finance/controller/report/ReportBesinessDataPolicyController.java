package com.jh.finance.controller.report;

import java.util.List;

import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.constant.FlowConstant;
import com.jh.finance.bean.report.ReportBesinessData;
import com.jh.finance.bean.report.ReportBesinessDataPolicy;
import com.jh.finance.bean.report.vo.ReportBesinessDataPolicyVo;
import com.jh.finance.bean.report.vo.ReportBesinessDataVo;
import com.jh.finance.service.report.ReportBesinessDataPolicyService;
import com.jh.finance.service.report.ReportBesinessDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.jh.common.bean.RestApiResponse;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.controller.BaseController;

import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

/**
 * 收费统计Controller
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@RestController
@RequestMapping("/report/policy")
@Tag(name = "收费统计-消费减负")
public class ReportBesinessDataPolicyController extends BaseController {

    @Autowired
    private ReportBesinessDataService reportBesinessDataService;

    private static final String PER_PREFIX = "btn:report:policy:";

    /**
     * 查询收费统计详情
     *
     * @param id
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findById")
    @Operation(summary = "查询收费统计详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        return RestApiResponse.ok(reportBesinessDataService.findById(id));
    }

    /**
     * 分页查询收费统计
     *
     * @param vo 收费统计 查询条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/findPageByQuery")
    @Operation(summary = "分页查询收费统计")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody ReportBesinessDataVo vo) {
        vo.setFlowKey(FlowConstant.REPORT_FEE_POLICY_KEY);
        PageInfo<ReportBesinessData> reportBesinessData = reportBesinessDataService.findPageByQuery(vo);
        return RestApiResponse.ok(reportBesinessData);
    }

    /**
     * 消费减负上报
     *
     * @param vo
     * @return RestApiResponse<?>
     */
    @PostMapping("/feeReport")
    @Operation(summary = "消费减负上报")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "feeReport')")
    public RestApiResponse<?> feeReport(@RequestBody ReportBesinessDataVo vo) {
        vo.setFlowKey(FlowConstant.REPORT_FEE_POLICY_KEY);
        reportBesinessDataService.feeReport(vo);
        return RestApiResponse.ok();
    }

    /**
     * 收费统计审核
     *
     * @param vo 包含审核参数的VO对象
     * @return RestApiResponse<?>
     */
    @PostMapping("/feeAudit")
    @Operation(summary = "收费统计审核")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "feeAudit')")
    public RestApiResponse<?> feeAudit(@RequestBody ReportBesinessDataVo vo) {
        vo.setFlowKey(FlowConstant.REPORT_FEE_POLICY_KEY);
        reportBesinessDataService.feeAudit(vo);
        return RestApiResponse.ok();
    }



}
