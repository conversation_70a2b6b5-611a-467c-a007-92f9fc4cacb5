package com.jh.finance.service.fee;

import java.util.List;

import com.github.pagehelper.PageInfo;

import com.jh.finance.bean.fee.FeeReductionPush;
import com.jh.finance.bean.fee.vo.FeeReductionPushVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 减费惠企改革-政策推送Service接口
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
public interface FeeReductionPushService {
    /**
     * 保存或更新减费惠企改革-政策推送
     *
     * @param feeReductionPush 减费惠企改革-政策推送对象
     * @return String 减费惠企改革-政策推送ID
     * <AUTHOR>
     */
    String saveOrUpdateFeeReductionPush(FeeReductionPush feeReductionPush);

    /**
     * 删除减费惠企改革-政策推送
     *
     * @param ids void 减费惠企改革-政策推送ID
     * <AUTHOR>
     */
    void deleteFeeReductionPush(List<String> ids);

    /**
     * 查询减费惠企改革-政策推送详情
     *
     * @param id
     * @return FeeReductionPush
     * <AUTHOR>
     */
    FeeReductionPush findById(String id);

    /**
     * 分页查询减费惠企改革-政策推送
     *
     * @param feeReductionPushVo
     * @return PageInfo<FeeReductionPush>
     * <AUTHOR>
     */
    PageInfo<FeeReductionPush> findPageByQuery(FeeReductionPushVo feeReductionPushVo);

    /**
     * 按条件导出查询减费惠企改革-政策推送
     *
     * @param feeReductionPushVo
     * @return PageInfo<FeeReductionPush>
     * <AUTHOR>
     */
    List<FeeReductionPush> findByQuery(FeeReductionPushVo feeReductionPushVo);

    /**
     * 导入减费惠企改革-政策推送
     *
     * @param file
     * @param cover 是否覆盖 1 覆盖 0 不覆盖
     * @return
     * <AUTHOR>
     */
    void importFeeReductionPushAsync(MultipartFile file, Integer cover);
}
