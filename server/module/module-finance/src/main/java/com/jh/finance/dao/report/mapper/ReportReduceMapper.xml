<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.finance.dao.report.ReportReduceMapper">

    <!-- 根据用户角色和区域查询数据 -->
    <select id="query" parameterType="com.jh.finance.bean.report.vo.ReportReduceVo" resultType="com.jh.finance.bean.report.ReportReduce">
        SELECT DISTINCT t.*
        FROM report_reduce t
        INNER JOIN sys_user u ON t.USER_ID = u.ID
        <where>
            t.YN = 1
            <!-- 管理员角色可以查看所有数据 -->
            <if test="hasAdminRole != true">
                <!-- 机构角色只能查看自己的数据 -->
                <if test="hasOrgRole == true and hasRegionRole != true and hasFinanceRole != true and hasScienceRole != true">
                    AND t.USER_ID = #{userId}
                </if>

                <!-- 地市角色可以查看下属区县的待审核和已处理数据 -->
                <if test="hasRegionRole == true and areaCode != null">
                    AND u.AREA_CODE LIKE CONCAT(#{areaCode}, '%')
                    AND (t.ACT_NODE_KEY = '10' OR t.ACT_NODE_KEY = '20')
                </if>

                <!-- 财务处和科技处可以查看下属区域的数据，但不能审核 -->
                <if test="(hasFinanceRole == true or hasScienceRole == true) and areaCode != null">
                    AND u.AREA_CODE LIKE CONCAT(#{areaCode}, '%')
                </if>
            </if>

            <!-- 按流程节点查询 -->
            <if test="actNodeKey != null and actNodeKey != ''">
                AND t.ACT_NODE_KEY = #{actNodeKey}
            </if>

            AND t.is_commit = #{isCommit}

            <if test="companyName != null and companyName != ''">
                AND t.COMPANY_NAME LIKE CONCAT('%', #{companyName}, '%')
            </if>


            <!-- 按地区代码筛选 -->
            <if test="code != null and code != ''">
                <choose>
                    <when test="code.endsWith('00')">
                        AND u.AREA_CODE LIKE CONCAT(SUBSTRING(#{code}, 1, LENGTH(#{code}) - 2), '%')
                    </when>
                    <otherwise>
                        AND u.AREA_CODE = #{code}
                    </otherwise>
                </choose>
            </if>
        </where>
        ORDER BY t.CREATE_TIME DESC
    </select>

    <!-- 根据用户ID和提交状态查询数据 -->
    <select id="findByUserIdAndCommitStatus" resultType="com.jh.finance.bean.report.ReportReduce">
        SELECT *
        FROM report_reduce
        WHERE YN = 1
        AND USER_ID = #{userId}
        AND IS_COMMIT = #{isCommit}
        ORDER BY CREATE_TIME DESC
    </select>


    <!-- 根据用户角色和区域查询数据 -->
    <select id="queryToDo" parameterType="com.jh.finance.bean.report.vo.ReportReduceVo" resultType="com.jh.finance.bean.report.ReportReduce">
        SELECT DISTINCT t.*
        FROM report_reduce t
        INNER JOIN sys_user u ON t.USER_ID = u.ID
        <where>
            t.YN = 1
            <!-- 管理员角色可以查看所有数据 -->
            <if test="hasAdminRole != true">

                <!-- 机构角色只能查看自己的数据 -->
                <if test="hasOrgRole == true and hasRegionRole != true and hasFinanceRole != true and hasScienceRole != true">
                    AND t.USER_ID = #{userId} AND t.ACT_NODE_KEY = #{actNodeKey}
                </if>

                <!-- 地市角色可以查看下属区县的待审核和已处理数据 -->
                <if test="hasRegionRole == true and areaCode != null">
                    AND u.AREA_CODE LIKE CONCAT(#{areaCode}, '%')
                    AND t.ACT_NODE_KEY = #{actNodeKey}
                </if>

            </if>
        </where>
        ORDER BY t.CREATE_TIME DESC
    </select>

</mapper>