<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.finance.dao.report.ReportBusinessTypeMapper">

    <!-- 查询顶级业务类型 -->
    <select id="findParentTypes" resultType="com.jh.finance.bean.report.ReportBusinessType">
        SELECT *
        FROM report_business_type
        WHERE PARENT_ID IS NULL OR PARENT_ID = 0
          AND YN = 1
        ORDER BY CREATE_TIME DESC
    </select>
    
    <!-- 根据父ID查询子业务类型 -->
    <select id="findByParentId" resultType="com.jh.finance.bean.report.ReportBusinessType">
        SELECT *
        FROM report_business_type
        WHERE PARENT_ID = #{parentId}
          AND YN = 1
        ORDER BY ID ASC
    </select>
    
    <!-- 根据组织ID查询业务类型 -->
    <select id="findByOrgId" resultType="com.jh.finance.bean.report.ReportBusinessType">
        SELECT *
        FROM report_business_type
        WHERE ORG_ID = #{orgId}
          AND YN = 1
        ORDER BY CREATE_TIME DESC
    </select>
    
    <!-- 查询业务类型树形结构 -->
    <select id="findTypeTree" resultType="com.jh.finance.bean.report.ReportBusinessType">
        SELECT *
        FROM report_business_type
        WHERE YN = 1
        ORDER BY CREATE_TIME DESC
    </select>

</mapper> 