package com.jh.finance.controller.report;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.List;

import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.finance.bean.report.ReportReduce;
import com.jh.finance.bean.report.vo.ReportReduceVo;
import com.jh.finance.service.report.ReportReduceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;


import com.jh.common.bean.RestApiResponse;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.controller.BaseController;

import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

/**
 * 减负降本数据Controller
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@RestController
@RequestMapping("/report/reduce")
@Tag(name = "减负降本数据")
public class ReportReduceController extends BaseController {
    @Autowired
    private ReportReduceService reportReduceService;

    private static final String PER_PREFIX = "btn:report:reduce:";

    /**
     * 新增减负降本数据
     *
     * @param reportReduce 减负降本数据数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @Operation(summary = "新增减负降本数据")
    @SystemLogAnnotation(type = "减负降本数据", value = "新增减负降本数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveReportReduce(@RequestBody ReportReduce reportReduce) {
        String id = reportReduceService.saveOrUpdateReportReduce(reportReduce);
        return RestApiResponse.ok(id);
    }

    /**
     * 修改减负降本数据
     *
     * @param reportReduce 减负降本数据数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @Operation(summary = "修改减负降本数据")
    @SystemLogAnnotation(type = "减负降本数据", value = "修改减负降本数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateReportReduce(@RequestBody ReportReduce reportReduce) {
        String id = reportReduceService.saveOrUpdateReportReduce(reportReduce);
        return RestApiResponse.ok(id);
    }

    /**
     * 批量删除减负降本数据(判断 关联数据是否可以删除)
     *
     * @param ids
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @Operation(summary = "批量删除减负降本数据")
    @SystemLogAnnotation(type = "减负降本数据", value = "批量删除减负降本数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteReportReduce(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        reportReduceService.deleteReportReduce(ids);
        return RestApiResponse.ok();
    }

    /**
     * 查询减负降本数据详情
     *
     * @param id
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findById")
    @Operation(summary = "查询减负降本数据详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        ReportReduce reportReduce = reportReduceService.findById(id);
        return RestApiResponse.ok(reportReduce);
    }

    /**
     * 分页查询减负降本数据
     *
     * @param reportReduceVo 减负降本数据 查询条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/findPageByQuery")
    @Operation(summary = "分页查询减负降本数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody ReportReduceVo reportReduceVo) {
        PageInfo<ReportReduce> reportReduce = reportReduceService.findPageByQuery(reportReduceVo);
        return RestApiResponse.ok(reportReduce);
    }

    @PostMapping("/excel")
    @Operation(summary = "导出减负降本数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "excel')")
    // 这里的模块名称和子模块名称，请根据实际情况填写
    @ExportRespAnnotation(modType = "减负降本数据", sonMod = "减负降本数据", key = "jh_reduce_Table", isNotice = true, fileName = "减负降本数据")
    public RestApiResponse<?> excel(@RequestBody ReportReduceVo reportReduceVo) {
        List<ReportReduce> reportReduceList = reportReduceService.findByQuery(reportReduceVo);
        //这里对数据进行转换处理，如没使用字典的或关联其它表的数据。如在查询中已处理请忽略
        return RestApiResponse.ok(reportReduceList);
    }

    @PostMapping("/import")
    @Operation(summary = "导入减负降本数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "import')")
    public RestApiResponse<?> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "cover") Integer cover) {
        reportReduceService.importReportReduceAsync(file, cover);
        return RestApiResponse.ok();
    }

    /**
     * 提交减负降本数据
     * 提交当前用户所有未提交状态的数据
     */
    @GetMapping("/submitReportReduce")
    @Operation(summary = "提交减负降本数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "submitReportReduce')")
    public RestApiResponse<?> submit() {
        reportReduceService.submitReportReduce();
        return RestApiResponse.ok();
    }

    /**
     * 提交指定的减负降本数据
     *
     * @param id 减负降本数据ID
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/submitById")
    @Operation(summary = "提交指定的减负降本数据")
    @SystemLogAnnotation(type = "减负降本数据", value = "提交指定的减负降本数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "submitReportReduce')")
    public RestApiResponse<?> submitById(@RequestParam("id") String id) {
        reportReduceService.submitReportReduceById(id);
        return RestApiResponse.ok();
    }

    /**
     * 审核减负降本数据
     *
     * @param reportReduceVo 包含审核状态和审核意见的VO对象
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/audit")
    @Operation(summary = "审核减负降本数据")
    @SystemLogAnnotation(type = "减负降本数据", value = "审核减负降本数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "audit')")
    public RestApiResponse<?> audit(@RequestBody ReportReduceVo reportReduceVo) {
        reportReduceService.auditReportReduce(reportReduceVo);
        return RestApiResponse.ok();
    }

    /**
     * 下载非标类收费模板
     *
     * @return ResponseEntity<byte [ ]>
     * <AUTHOR>
     */
    @PostMapping("/downloadTemplate")
    @Operation(summary = "下载非标类收费模板")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "template')")
    public ResponseEntity<byte[]> downloadTemplate() {
        try {
            ClassPathResource resource = new ClassPathResource("template/减费惠企数据汇总表.xls");
            InputStream inputStream = resource.getInputStream();
            byte[] fileBytes = StreamUtils.copyToByteArray(inputStream);

            // 对中文文件名进行URL编码
            String fileName = "减费惠企数据汇总表.xls";
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.add("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
            headers.setContentLength(fileBytes.length);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(fileBytes);
        } catch (IOException e) {
            throw new RuntimeException("模板文件下载失败", e);
        }
    }
}
