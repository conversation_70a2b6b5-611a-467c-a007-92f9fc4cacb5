package com.jh.finance.bean.report.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 减负降本数据对象 report_reduce
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
public class ReportReduceExcel implements Serializable {
    private static final long serialVersionUID = 1L;
    @ExcelProperty(value = "企业名称", index = 0)
    private String companyName;
    @ExcelProperty(value = "统一社会信用代码", index = 1)
    private String uscc;
    @ExcelProperty(value = "区县code", index = 2)
    private String countyCode;
    @ExcelProperty(value = "区县名称", index = 3)
    private String countyName;
    @ExcelProperty(value = "地市code", index = 4)
    private String cityCode;
    @ExcelProperty(value = "地市名称", index = 5)
    private String cityName;
    @ExcelProperty(value = "产品或设备名称", index = 6)
    private String productName;
    @ExcelProperty(value = "数量", index = 7)
    private String productCount;
    @ExcelProperty(value = "行业大类code", index = 8)
    private String industryClassCode;
    @ExcelProperty(value = "行业大类", index = 9)
    private String industryClassName;
    @ExcelProperty(value = "行业门类code", index = 10)
    private String industryCategoryCode;
    @ExcelProperty(value = "行业门类", index = 11)
    private String industryCategoryName;
    @ExcelProperty(value = "是否符合政策 1：是 0：否", index = 12)
    private String isPolicyCompliant;
    @ExcelProperty(value = "政策名称", index = 13)
    private String policyName;
    @ExcelProperty(value = "政策ID", index = 14)
    private String policyId;
    @ExcelProperty(value = "政策类型id", index = 15)
    private String policyTypeId;
    @ExcelProperty(value = "政策类型", index = 16)
    private String policyType;
    @ExcelProperty(value = "享受时间", index = 17)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date enjoyTime;
    @ExcelProperty(value = "本次减免费用（元）", index = 18)
    private BigDecimal reduceAmount;
    @ExcelProperty(value = "联系人手机", index = 19)
    private String contactPhone;


    /**
     * 导入情况
     */
    @ExcelProperty(value = "导入情况", index = 20)
    private String importSituation;

//    /**
//     * 设置数量 - 字符串转换方法
//     * 用于处理Excel导入时可能的非数字格式
//     * @param productCount 数量字符串
//     */
//    public void setProductCount(String productCount) {
//        if (productCount == null || productCount.trim().isEmpty()) {
//            this.productCount = null;
//            return;
//        }
//        try {
//            this.productCount = Integer.parseInt(productCount.trim());
//        } catch (NumberFormatException e) {
//            // 转换失败时设为null
//            this.productCount = null;
//        }
//    }

    /**
     * 设置是否符合政策 - 字符串转换方法
     * 用于处理Excel导入时"是"或"否"的格式
     * @param isPolicyCompliant 是否符合政策字符串
     */
    public void setIsPolicyCompliant(String isPolicyCompliant) {
        if (isPolicyCompliant == null || isPolicyCompliant.trim().isEmpty()) {
            this.isPolicyCompliant = null;
            return;
        }
        
        String value = isPolicyCompliant.trim();
        // 检查是否为"是"或"否"
        if ("是".equals(value)) {
            this.isPolicyCompliant = "1";
        } else if ("否".equals(value)) {
            this.isPolicyCompliant = "0";
        } else {
            // 如果已经是数字格式，直接使用
            if (value.matches("^[0-9]+$")) {
                this.isPolicyCompliant = value;
            } else {
                // 其他情况设为null
                this.isPolicyCompliant = null;
            }
        }
    }

}
