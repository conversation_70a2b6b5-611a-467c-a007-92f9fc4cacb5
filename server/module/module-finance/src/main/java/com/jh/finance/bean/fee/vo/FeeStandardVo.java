package com.jh.finance.bean.fee.vo;

import com.jh.finance.bean.fee.FeeStandard;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 收费信息Vo
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Schema(description = "FeeStandardVo")
@Data
public class FeeStandardVo extends FeeStandard {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;


    @Schema(description = "操作类型：1-更新，2-新增，3-删除")
    private Integer isUpdate;

}