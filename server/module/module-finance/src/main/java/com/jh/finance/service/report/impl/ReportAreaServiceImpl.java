package com.jh.finance.service.report.impl;
import com.jh.finance.bean.report.ReportArea;
import com.jh.finance.bean.report.vo.ReportAreaVo;
import com.jh.finance.dao.report.ReportAreaMapper;
import com.jh.finance.service.report.ReportAreaService;
import com.jh.utils.ImportService;
import com.jh.common.bean.SysUser;
import com.jh.sys.service.SysUserService;
import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;
import com.jh.common.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;
import static com.github.pagehelper.page.PageMethod.orderBy;

/**
 *  浙江省行政区划Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
@Service
@Transactional(readOnly = true)
public class ReportAreaServiceImpl extends BaseServiceImpl<ReportAreaMapper, ReportArea> implements ReportAreaService {
	
	private static final Logger logger = LoggerFactory.getLogger(ReportAreaServiceImpl.class);
    @Autowired
    private ReportAreaMapper reportAreaMapper;

    @Autowired
	private ImportService importService;
	
	@Autowired
	private SysUserService sysUserService;


	@Override
	@Transactional(readOnly = false)
	/**
	 * 保存或更新浙江省行政区划
	 *@param reportArea 浙江省行政区划对象
	 *@return String 浙江省行政区划ID
	 *<AUTHOR>
	 */
	public String saveOrUpdateReportArea(ReportArea reportArea) {
		if(reportArea==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(reportArea.getId())){
			//新增
			reportArea.setId(UUIDUtils.getUUID());
			reportAreaMapper.insertSelective(reportArea);
		}else{
			//TODO 做判断后方能执行修改 一般判断是否是自己的数据
			//避免页面传入修改
			reportArea.setYn(null);
			reportAreaMapper.updateByPrimaryKeySelective(reportArea);
		}
		return reportArea.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 * 删除浙江省行政区划
	 *@param ids void 浙江省行政区划ID
	 *<AUTHOR>
	 */
	public void deleteReportArea(List<String> ids) {
		for(String id:ids){
			//TODO 做判断后方能执行删除 一般判断是否是自己的数据
			ReportArea reportArea=reportAreaMapper.selectByPrimaryKey(id);
			if(reportArea==null){
				throw new ServiceException("非法请求");
			}
			//逻辑删除
			ReportArea temreportArea=new ReportArea();
			temreportArea.setYn(CommonConstant.FLAG_NO);
			temreportArea.setId(reportArea.getId());
			reportAreaMapper.updateByPrimaryKeySelective(temreportArea);
		}
	}

	/**
	 * 查询浙江省行政区划详情
	 *@param id
	 *@return ReportArea
	 *<AUTHOR>
	 */
    @Override
	public ReportArea findById(String id) {
	    // TODO 做判断后方能反回数据 一般判断是否是自己的数据
		return reportAreaMapper.selectByPrimaryKey(id);
	}


	/**
	 * 分页查询浙江省行政区划
	 *@param reportAreaVo
	 *@return PageInfo<ReportArea>
	 *<AUTHOR>
	 */
	@Override
	public PageInfo<ReportArea> findPageByQuery(ReportAreaVo reportAreaVo) {
		// TODO 做判断后方能执行查询 一般判断是否是自己的数据
		PageHelper.startPage(reportAreaVo.getPageNum(),reportAreaVo.getPageSize());
		orderBy(reportAreaVo.getOrderColumn() + " " + reportAreaVo.getOrderValue());
		Example example=getExample(reportAreaVo);
		List<ReportArea> reportAreaList=reportAreaMapper.selectByExample(example);
		return new PageInfo<ReportArea>(reportAreaList);
	}
	private Example getExample(ReportAreaVo reportAreaVo){
		Example example=new Example(ReportArea.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		if(!StringUtils.isEmpty(reportAreaVo.getName())){
			criteria.andLike("name", "%" + reportAreaVo.getName() + "%");
		}
		if(!StringUtils.isEmpty(reportAreaVo.getCode())){
			criteria.andLike("code", reportAreaVo.getCode() + "%");
		}
		if(reportAreaVo.getLevel() != null){
			criteria.andEqualTo("level", reportAreaVo.getLevel());
		}
		if(Boolean.TRUE.equals(reportAreaVo.getOnlyProvince())){
			criteria.andEqualTo("level", 0L);
		}
		if(Boolean.TRUE.equals(reportAreaVo.getOnlyCity())){
			criteria.andEqualTo("level", 1L);
		}
		if(Boolean.TRUE.equals(reportAreaVo.getOnlyCounty())){
			criteria.andEqualTo("level", 2L);
		}
		if(!StringUtils.isEmpty(reportAreaVo.getParentCode())){
			criteria.andLike("code", reportAreaVo.getParentCode() + "%");
			criteria.andNotEqualTo("code", reportAreaVo.getParentCode());
		}
		example.orderBy("sort").asc();
		return example;
	}
	
	/**
     * 查询省级数据
     * @return List<ReportArea>
     */
    @Override
    public List<ReportArea> findProvinceList() {
        return reportAreaMapper.findProvinceList();
    }
    
    /**
     * 查询市级数据
     * @return List<ReportArea>
     */
    @Override
    public List<ReportArea> findCityList() {
        return reportAreaMapper.findCityList();
    }
    
    /**
     * 查询区县级数据
     * @return List<ReportArea>
     */
    @Override
    public List<ReportArea> findCountyList() {
        return reportAreaMapper.findCountyList();
    }
    
    /**
     * 根据父级代码查询下级区划
     * @param parentCode 父级代码
     * @return List<ReportArea>
     */
    @Override
    public List<ReportArea> findByParentCode(String parentCode) {
        if(StringUtils.isEmpty(parentCode)) {
            return new ArrayList<>();
        }
        return reportAreaMapper.findByParentCode(parentCode);
    }
    
    /**
     * 根据级别查询区划
     * @param level 级别
     * @return List<ReportArea>
     */
    @Override
    public List<ReportArea> findByLevel(Long level) {
        if(level == null) {
            return new ArrayList<>();
        }
        return reportAreaMapper.findByLevel(level);
    }
    
    /**
     * 获取省市区县树形结构
     * @return List<ReportArea>
     */
    @Override
    public List<ReportArea> getAreaTree() {
        // 获取所有区划
        Example example = new Example(ReportArea.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
        example.orderBy("sort").asc();
        List<ReportArea> allAreas = reportAreaMapper.selectByExample(example);
        
        // 按级别分组
        Map<Long, List<ReportArea>> levelMap = allAreas.stream()
                .collect(Collectors.groupingBy(ReportArea::getLevel));
        
        // 构建树形结构
        List<ReportArea> provinceList = levelMap.getOrDefault(0L, new ArrayList<>());
        List<ReportArea> cityList = levelMap.getOrDefault(1L, new ArrayList<>());
        List<ReportArea> countyList = levelMap.getOrDefault(2L, new ArrayList<>());
        
        // 构建省市区县关系
        Map<String, List<ReportArea>> cityMap = new HashMap<>();
        Map<String, List<ReportArea>> countyMap = new HashMap<>();
        
        // 市级按省级分组
        for (ReportArea city : cityList) {
            String provinceCode = city.getCode().substring(0, 2) + "0000";
            cityMap.computeIfAbsent(provinceCode, k -> new ArrayList<>()).add(city);
        }
        
        // 区县级按市级分组
        for (ReportArea county : countyList) {
            String cityCode = county.getCode().substring(0, 4) + "00";
            countyMap.computeIfAbsent(cityCode, k -> new ArrayList<>()).add(county);
        }
        
        // 构建省-市-区县的树形结构
        for (ReportArea province : provinceList) {
            // 设置省级的子节点（市级）
            List<ReportArea> cities = cityMap.getOrDefault(province.getCode(), new ArrayList<>());
            province.setChildren(cities);
            
            // 设置市级的子节点（区县级）
            for (ReportArea city : cities) {
                List<ReportArea> counties = countyMap.getOrDefault(city.getCode(), new ArrayList<>());
                city.setChildren(counties);
            }
        }
        
        return provinceList;
    }
    
    /**
     * 根据用户ID获取用户下属的行政区划列表（包含本级和下属的层级结构）
     * @param userId 用户ID
     * @return List<ReportArea>
     */
    @Override
    public List<ReportArea> getUserSubordinateAreas(String userId) {
        try {
            // 获取用户信息
            SysUser user = sysUserService.findById(userId);
            if (user == null) {
                logger.warn("用户不存在，userId: {}", userId);
                return new ArrayList<>();
            }
            
            String areaCode = user.getAreaCode();
            
            // 如果用户的AREA_CODE为空，返回全国区县列表
            if (StringUtils.isEmpty(areaCode)) {
                logger.info("用户AREA_CODE为空，返回全国区县列表，userId: {}", userId);
                return getAreaTree();
            }
            
            // 根据用户的AREA_CODE构建包含本级和下属的层级结构
            logger.info("根据用户AREA_CODE构建层级结构，userId: {}, areaCode: {}", userId, areaCode);
            return buildAreaTreeFromCode(areaCode);
            
        } catch (Exception e) {
            logger.error("获取用户下属行政区划失败，userId: {}", userId, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 根据区划代码构建包含本级和下属的层级结构
     * @param areaCode 区划代码
     * @return List<ReportArea>
     */
    private List<ReportArea> buildAreaTreeFromCode(String areaCode) {
        List<ReportArea> result = new ArrayList<>();
        
        // 根据代码查找本级区划
        Example example = new Example(ReportArea.class);
        example.createCriteria()
                .andEqualTo("yn", CommonConstant.FLAG_YES)
                .andEqualTo("code", areaCode);
        List<ReportArea> currentAreas = reportAreaMapper.selectByExample(example);
        
        if (currentAreas.isEmpty()) {
            logger.warn("未找到区划代码对应的区划信息，areaCode: {}", areaCode);
            return result;
        }
        
        ReportArea currentArea = currentAreas.get(0);
        
        // 获取下属区划并构建层级结构
        List<ReportArea> subordinateAreas = findByParentCode(areaCode);
        if (!subordinateAreas.isEmpty()) {
            // 如果有下属区划，需要进一步构建层级关系
            Map<String, List<ReportArea>> subordinateMap = new HashMap<>();
            
            // 按父级代码分组下属区划
            for (ReportArea area : subordinateAreas) {
                String parentCode = getParentCodeFromAreaCode(area.getCode());
                subordinateMap.computeIfAbsent(parentCode, k -> new ArrayList<>()).add(area);
            }
            
            // 为当前区划设置直接下属
            List<ReportArea> directChildren = subordinateMap.getOrDefault(areaCode, new ArrayList<>());
            
            // 为每个直接下属设置其子级
            for (ReportArea child : directChildren) {
                List<ReportArea> grandChildren = subordinateMap.getOrDefault(child.getCode(), new ArrayList<>());
                child.setChildren(grandChildren);
            }
            
            currentArea.setChildren(directChildren);
        }
        
        result.add(currentArea);
        return result;
    }
    
    /**
     * 根据区划代码获取父级代码
     * @param areaCode 区划代码
     * @return 父级代码
     */
    private String getParentCodeFromAreaCode(String areaCode) {
        if (StringUtils.isEmpty(areaCode) || areaCode.length() < 6) {
            return "";
        }
        
        // 区县级（后两位不为00）-> 市级（后两位改为00）
        if (!areaCode.endsWith("00")) {
            return areaCode.substring(0, 4) + "00";
        }
        // 市级（中间两位不为00，后两位为00）-> 省级（中间两位改为00）
        else if (!areaCode.substring(2, 4).equals("00")) {
            return areaCode.substring(0, 2) + "0000";
        }
        // 省级或其他情况
        return "";
    }
}
