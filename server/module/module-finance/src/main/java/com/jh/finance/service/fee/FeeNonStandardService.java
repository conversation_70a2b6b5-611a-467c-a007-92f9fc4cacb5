package com.jh.finance.service.fee;

import java.util.List;

import com.github.pagehelper.PageInfo;

import com.jh.finance.bean.fee.FeeNonStandard;
import com.jh.finance.bean.fee.vo.FeeNonStandardVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 非标类项目Service接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface FeeNonStandardService {
    /**
     * 保存或更新非标类项目
     *
     * @param feeNonStandard 非标类项目对象
     * @return String 非标类项目ID
     * <AUTHOR>
     */
    String saveOrUpdateFeeNonStandard(FeeNonStandard feeNonStandard);

    /**
     * 删除非标类项目
     *
     * @param ids void 非标类项目ID
     * <AUTHOR>
     */
    void deleteFeeNonStandard(List<String> ids);

    /**
     * 查询非标类项目详情
     *
     * @param id
     * @return FeeNonStandard
     * <AUTHOR>
     */
    FeeNonStandard findById(String id);

    /**
     * 分页查询非标类项目
     *
     * @param feeNonStandardVo
     * @return PageInfo<FeeNonStandard>
     * <AUTHOR>
     */
    PageInfo<FeeNonStandard> findPageByQuery(FeeNonStandardVo feeNonStandardVo);

    /**
     * 按条件导出查询非标类项目
     *
     * @param feeNonStandardVo
     * @return PageInfo<FeeNonStandard>
     * <AUTHOR>
     */
    List<FeeNonStandard> findByQuery(FeeNonStandardVo feeNonStandardVo);

    /**
     * 导入非标类项目
     *
     * @param file
     * @param cover 是否覆盖 1 覆盖 0 不覆盖
     * @return
     * <AUTHOR>
     */
    void importFeeNonStandardAsync(MultipartFile file, Integer cover);

    /**
     * 根据机构代码查询检验检测服务项目（非标类）
     *
     * @param orgCode 机构代码
     * @return List<FeeNonStandard> 非标类收费信息列表
     * <AUTHOR>
     */
    List<FeeNonStandard> findNonStandardByOrgCode(String orgCode);
}
