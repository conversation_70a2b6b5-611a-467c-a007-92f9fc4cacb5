package com.jh.finance.bean.report.vo;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * 统计数据查询参数对象
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@Schema(description = "统计数据查询参数")
public class ReportStatisticsVo {

    @Schema(description = "区域代码")
    private String code;

    @Schema(description = "统计类型：1-收费统计，2-清费减负")
    private String type;

    @Schema(description = "年份1")
    private String year1;

    @Schema(description = "年份2")
    private String year2;

    @Schema(description = "季度1")
    private String quarter1;

    @Schema(description = "季度2")
    private String quarter2;

    @Schema(description = "开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM")
    private String startTime;

    @Schema(description = "结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM")
    private String endTime;

    @Schema(description = "年份")
    private String year;

    @Schema(description = "季度")
    private String quarter;

    @Schema(description = "月份")
    private Integer month;

    @Schema(description = "当前用户角色是否为机构")
    private Boolean isOrg;

    @Schema(description = "当前用户角色是否为地市")
    private Boolean isCity;

    @Schema(description = "导出文件名称")
    private String exportFileName;
} 