package com.jh.finance.service.report.impl;

import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.common.bean.LoginUser;
import com.jh.common.util.AppUserUtil;
import com.jh.common.util.io.FileConvertUtils;
import com.jh.common.util.io.FileUtil;
import com.jh.common.util.poi.EasyExcelUtils;
import com.jh.constant.enums.ImportStatusEnum;
import com.jh.finance.bean.report.ReportBusinessTypePolicy;
import com.jh.finance.bean.report.vo.ReportBusinessTypePolicyVo;
import com.jh.finance.dao.report.ReportBusinessTypePolicyMapper;
import com.jh.finance.service.report.ReportBusinessTypePolicyService;
import com.jh.utils.ImportService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.CollectionUtils;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import java.util.List;
import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;


import com.jh.common.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;
import static com.github.pagehelper.page.PageMethod.orderBy;
import org.springframework.web.multipart.MultipartFile;

/**
 *  业务类型政策Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Service
@Transactional(readOnly = true)
public class ReportBusinessTypePolicyServiceImpl extends BaseServiceImpl<ReportBusinessTypePolicyMapper, ReportBusinessTypePolicy> implements ReportBusinessTypePolicyService {
	
	private static final Logger logger = LoggerFactory.getLogger(ReportBusinessTypePolicyServiceImpl.class);
    @Autowired
    private ReportBusinessTypePolicyMapper reportBusinessTypePolicyMapper;

    @Autowired
	private ImportService importService;

	@Value("${file.temp.path}")
	private String tempPath;

	@Override
	@Transactional(readOnly = false)
	/**
	 * 保存或更新业务类型政策
	 *@param reportBusinessTypePolicy 业务类型政策对象
	 *@return String 业务类型政策ID
	 *<AUTHOR>
	 */
	public String saveOrUpdateReportBusinessTypePolicy(ReportBusinessTypePolicy reportBusinessTypePolicy) {
		if(reportBusinessTypePolicy==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(reportBusinessTypePolicy.getId())){
			//新增
			reportBusinessTypePolicy.setId(UUIDUtils.getUUID());
			reportBusinessTypePolicyMapper.insertSelective(reportBusinessTypePolicy);
		}else{
			//TODO 做判断后方能执行修改 一般判断是否是自己的数据
			//避免页面传入修改
			reportBusinessTypePolicy.setYn(null);
			reportBusinessTypePolicyMapper.updateByPrimaryKeySelective(reportBusinessTypePolicy);
		}
		return reportBusinessTypePolicy.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 * 删除业务类型政策
	 *@param ids void 业务类型政策ID
	 *<AUTHOR>
	 */
	public void deleteReportBusinessTypePolicy(List<String> ids) {
		for(String id:ids){
			//TODO 做判断后方能执行删除 一般判断是否是自己的数据
			ReportBusinessTypePolicy reportBusinessTypePolicy=reportBusinessTypePolicyMapper.selectByPrimaryKey(id);
			if(reportBusinessTypePolicy==null){
				throw new ServiceException("非法请求");
			}
			//逻辑删除
			ReportBusinessTypePolicy temreportBusinessTypePolicy=new ReportBusinessTypePolicy();
			temreportBusinessTypePolicy.setYn(CommonConstant.FLAG_NO);
			temreportBusinessTypePolicy.setId(reportBusinessTypePolicy.getId());
			reportBusinessTypePolicyMapper.updateByPrimaryKeySelective(temreportBusinessTypePolicy);
		}
	}

	/**
	 * 查询业务类型政策详情
	 *@param id
	 *@return ReportBusinessTypePolicy
	 *<AUTHOR>
	 */
    @Override
	public ReportBusinessTypePolicy findById(String id) {
	    // TODO 做判断后方能反回数据 一般判断是否是自己的数据
		return reportBusinessTypePolicyMapper.selectByPrimaryKey(id);
	}


	/**
	 * 分页查询业务类型政策
	 *@param reportBusinessTypePolicyVo
	 *@return PageInfo<ReportBusinessTypePolicy>
	 *<AUTHOR>
	 */
	@Override
	public PageInfo<ReportBusinessTypePolicy> findPageByQuery(ReportBusinessTypePolicyVo reportBusinessTypePolicyVo) {
		// TODO 做判断后方能执行查询 一般判断是否是自己的数据
		PageHelper.startPage(reportBusinessTypePolicyVo.getPageNum(),reportBusinessTypePolicyVo.getPageSize());
		orderBy(reportBusinessTypePolicyVo.getOrderColumn() + " " + reportBusinessTypePolicyVo.getOrderValue());
		Example example=getExample(reportBusinessTypePolicyVo);
		List<ReportBusinessTypePolicy> reportBusinessTypePolicyList=reportBusinessTypePolicyMapper.selectByExample(example);
		return new PageInfo<ReportBusinessTypePolicy>(reportBusinessTypePolicyList);
	}
	private Example getExample(ReportBusinessTypePolicyVo reportBusinessTypePolicyVo){
		Example example=new Example(ReportBusinessTypePolicy.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(reportBusinessTypePolicyVo.getName())){
		//	criteria.andEqualTo(reportBusinessTypePolicyVo.getName());
		//}
		example.orderBy("updateTime").desc();
		return example;
	}



	/**
	 * 根据业务类型ID查询政策
	 * @param businessTypeId 业务类型ID
	 * @return List<ReportBusinessTypePolicy> 政策列表
	 * <AUTHOR>
	 */
	@Override
	public List<ReportBusinessTypePolicy> findByBusinessTypeId(String businessTypeId) {
		if(StringUtils.isEmpty(businessTypeId)){
			throw new ServiceException("业务类型ID不能为空");
		}
		Example example = new Example(ReportBusinessTypePolicy.class);
		Criteria criteria = example.createCriteria();
		criteria.andEqualTo("businessTypeId", businessTypeId);
		criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
		example.orderBy("type").asc().orderBy("sort").asc();
		return reportBusinessTypePolicyMapper.selectByExample(example);
	}

	/**
	 * 根据业务类型ID查询政策并按TYPE分组返回VO对象
	 * @param businessTypeId 业务类型ID
	 * @return ReportBusinessTypePolicyVo 包含分组后的政策数据
	 * <AUTHOR>
	 */
	@Override
	public ReportBusinessTypePolicyVo findByBusinessTypeIdGrouped(String businessTypeId) {
		if(StringUtils.isEmpty(businessTypeId)){
			throw new ServiceException("业务类型ID不能为空");
		}
		
		// 创建返回对象
		ReportBusinessTypePolicyVo vo = new ReportBusinessTypePolicyVo();
		vo.setBusinessTypeId(businessTypeId);
		
		// 获取所有政策
		List<ReportBusinessTypePolicy> policies = findByBusinessTypeId(businessTypeId);
		
		// 按类型分组
		Map<String, List<ReportBusinessTypePolicy>> groupedMap = new HashMap<>();
		for (ReportBusinessTypePolicy policy : policies) {
			String type = policy.getType();
			if (!groupedMap.containsKey(type)) {
				groupedMap.put(type, new ArrayList<>());
			}
			groupedMap.get(type).add(policy);
		}
		
		// 转换为前端需要的格式，并确保每个分组内的数据按SORT字段排序
		List<Map<String, Object>> result = new ArrayList<>();
		for (Map.Entry<String, List<ReportBusinessTypePolicy>> entry : groupedMap.entrySet()) {
			Map<String, Object> group = new HashMap<>();
			group.put("type", entry.getKey());
			
			// 对当前分组内的政策按SORT字段排序
			List<ReportBusinessTypePolicy> sortedPolicies = entry.getValue();
			sortedPolicies.sort((p1, p2) -> {
				Long sort1 = p1.getSort() != null ? p1.getSort() : Long.MAX_VALUE;
				Long sort2 = p2.getSort() != null ? p2.getSort() : Long.MAX_VALUE;
				return sort1.compareTo(sort2);
			});
			
			group.put("policies", sortedPolicies);
			result.add(group);
		}
		
		// 对分组结果按类型排序
		result.sort((g1, g2) -> {
			String type1 = (String) g1.get("type");
			String type2 = (String) g2.get("type");
			return type1.compareTo(type2);
		});
		
		// 设置分组后的数据
		vo.setGroupedPolicies(result);
		
		return vo;
	}

}
