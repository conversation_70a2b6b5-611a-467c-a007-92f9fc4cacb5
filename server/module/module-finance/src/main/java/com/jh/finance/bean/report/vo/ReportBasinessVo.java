package com.jh.finance.bean.report.vo;

import com.jh.finance.bean.report.ReportBasiness;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 收费统计表Vo
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Schema(description = "ReportBasinessVo")
@Data
public class ReportBasinessVo extends ReportBasiness {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;

} 