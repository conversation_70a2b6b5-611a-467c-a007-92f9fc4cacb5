package com.jh.finance.bean.fee.vo;

import java.util.Date;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 减费惠企改革-政策推送对象 fee_reduction_push
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
public class FeeReductionPushExcel implements Serializable {
    private static final long serialVersionUID = 1L;
    @ExcelProperty(value = "统一社会信用代码", index = 0)
    private String orgCode;
    @ExcelProperty(value = "企业名称", index = 1)
    private String enterpriseName;
    @ExcelProperty(value = "联系人", index = 2)
    private String contactPerson;
    @ExcelProperty(value = "联系人手机", index = 3)
    private String contactPhone;
    @ExcelProperty(value = "设备名称", index = 4)
    private String equipmentName;
    @ExcelProperty(value = "设备数量", index = 5)
    private Long equipmentCount;
    @ExcelProperty(value = "政策ID", index = 6)
    private String policyId;
    @ExcelProperty(value = "是否推送(1是 0否)", index = 7)
    private Integer isPush;
    @ExcelProperty(value = "推送时间", index = 8)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date pushTime;
    /**
     * 导入情况
     */
    @ExcelProperty(value = "导入情况", index = 9)
    private String importSituation;

}
