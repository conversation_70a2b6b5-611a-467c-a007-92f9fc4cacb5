package com.jh.finance.controller.report;

import java.util.List;

import com.jh.constant.FlowConstant;
import com.jh.finance.bean.report.ReportBesinessData;
import com.jh.finance.bean.report.vo.ReportBesinessDataVo;
import com.jh.finance.bean.report.vo.ReportStatisticsQueryVo;
import com.jh.finance.bean.report.vo.ReportSummaryVo;
import com.jh.finance.service.report.ReportBesinessDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.controller.BaseController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

/**
 * 收费统计Controller
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@RestController
@RequestMapping("/report/data")
@Tag(name = "收费统计")
public class ReportBesinessDataController extends BaseController {

    @Autowired
    private ReportBesinessDataService reportBesinessDataService;

    private static final String PER_PREFIX = "btn:report:data:";

    /**
     * 新增收费统计
     *
     * @param reportBesinessData 收费统计数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @Operation(summary = "新增收费统计")
    @SystemLogAnnotation(type = "收费统计", value = "新增收费统计")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveReportBesinessData(@RequestBody ReportBesinessData reportBesinessData) {
        String id = reportBesinessDataService.saveOrUpdateReportBesinessData(reportBesinessData);
        return RestApiResponse.ok(id);
    }

    /**
     * 修改收费统计
     *
     * @param reportBesinessData 收费统计数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @Operation(summary = "修改收费统计")
    @SystemLogAnnotation(type = "收费统计", value = "修改收费统计")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateReportBesinessData(@RequestBody ReportBesinessData reportBesinessData) {
        String id = reportBesinessDataService.saveOrUpdateReportBesinessData(reportBesinessData);
        return RestApiResponse.ok(id);
    }

    /**
     * 批量删除收费统计(判断 关联数据是否可以删除)
     *
     * @param ids
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @Operation(summary = "批量删除收费统计")
    @SystemLogAnnotation(type = "收费统计", value = "批量删除收费统计")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteReportBesinessData(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        reportBesinessDataService.deleteReportBesinessData(ids);
        return RestApiResponse.ok();
    }

    /**
     * 查询收费统计详情
     *
     * @param id
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findById")
    @Operation(summary = "查询收费统计详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        ReportBesinessDataVo reportBesinessDataVo = reportBesinessDataService.findById(id);
        return RestApiResponse.ok(reportBesinessDataVo);
    }

    /**
     * 分页查询收费统计
     *
     * @param vo 收费统计 查询条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/findPageByQuery")
    @Operation(summary = "分页查询收费统计")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody ReportBesinessDataVo vo) {
        vo.setFlowKey(FlowConstant.REPORT_FEE_KEY);
        PageInfo<ReportBesinessData> reportBesinessData = reportBesinessDataService.findPageByQuery(vo);
        return RestApiResponse.ok(reportBesinessData);
    }

    /**
     * 收费统计上报
     *
     * @param vo
     * @return RestApiResponse<?>
     */
    @PostMapping("/feeReport")
    @Operation(summary = "收费统计上报")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "feeReport')")
    public RestApiResponse<?> feeReport(@RequestBody ReportBesinessDataVo vo) {
        vo.setFlowKey(FlowConstant.REPORT_FEE_KEY);
        reportBesinessDataService.feeReport(vo);
        return RestApiResponse.ok();
    }
    
    /**
     * 收费统计审核
     *
     * @param vo 包含审核参数的VO对象
     * @return RestApiResponse<?>
     */
    @PostMapping("/feeAudit")
    @Operation(summary = "收费统计审核")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "feeAudit')")
    public RestApiResponse<?> feeAudit(@RequestBody ReportBesinessDataVo vo) {
        vo.setFlowKey(FlowConstant.REPORT_FEE_KEY);
        reportBesinessDataService.feeAudit(vo);
        return RestApiResponse.ok();
    }

    /**
     * 查询收费统计数据
     *
     * @param vo 查询条件
     * @return 统计结果
     */
    @PostMapping("/getDataTotal")
    @Operation(summary = "查询收费统计数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "getDataTotal')")
    public RestApiResponse<?> getDataTotal(@RequestBody ReportBesinessDataVo vo) {
        vo.setQueryType("1");
        List<ReportStatisticsQueryVo> result = reportBesinessDataService.getDataTotal(vo);
        return RestApiResponse.ok(result);
    }

    /**
     * 查询消费减负数据
     *
     * @param vo 查询条件
     * @return 统计结果
     */
    @PostMapping("/getPolicyDataTotal")
    @Operation(summary = "查询消费减负数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "getPolicyDataTotal')")
    public RestApiResponse<?> getPolicyDataTotal(@RequestBody ReportBesinessDataVo vo) {
        vo.setQueryType("2");
        List<ReportStatisticsQueryVo> result = reportBesinessDataService.getDataTotal(vo);
        return RestApiResponse.ok(result);
    }

    /**
     * 查询减负降本数据
     *
     * @param vo 查询条件
     * @return 统计结果
     */
    @PostMapping("/getReduceDataTotal")
    @Operation(summary = "查询减负降本数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "getReduceDataTotal')")
    public RestApiResponse<?> getReduceDataTotal(@RequestBody ReportBesinessDataVo vo) {
        List<ReportStatisticsQueryVo> result = reportBesinessDataService.getReduceDataTotal(vo);
        return RestApiResponse.ok(result);
    }

    /**
     * 数据上报情况查询
     * 根据地区代码返回不同层级的数据：
     * - 传入330000：返回浙江省内各市的汇总数据
     * - 传入具体市代码：返回该市下各机构的详细数据（支持分页）
     *
     * @param vo 查询条件（年份、季度、地区代码、分页参数）
     * @return 统计结果
     */
    @PostMapping("/getReportSummary")
    @Operation(summary = "数据上报情况查询")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "getReportSummary')")
    public RestApiResponse<?> getReportSummary(@RequestBody ReportBesinessDataVo vo) {
        PageInfo<ReportSummaryVo> result = reportBesinessDataService.getReportSummary(vo);
        return RestApiResponse.ok(result);
    }

}
