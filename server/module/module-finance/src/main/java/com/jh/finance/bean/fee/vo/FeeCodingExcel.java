package com.jh.finance.bean.fee.vo;

import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 收费赋码对象 fee_coding
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
public class FeeCodingExcel implements Serializable {
    private static final long serialVersionUID = 1L;
    @ExcelProperty(value = "收费代码", index = 0)
    private String code;
    @ExcelProperty(value = "收费大类", index = 1)
    private String majorCategory;
    @ExcelProperty(value = "收费中类", index = 2)
    private String middleCategory;
    @ExcelProperty(value = "收费小类", index = 3)
    private String minorCategory;
    @ExcelProperty(value = "收费产品（检测对象）", index = 4)
    private String product;
    @ExcelProperty(value = "收费项目/参数", index = 5)
    private String detectionItem;
    @ExcelProperty(value = "收费价格（元）", index = 6)
    private BigDecimal feePrice;
    @ExcelProperty(value = "收费计费单元", index = 7)
    private String billingUnit;
    @ExcelProperty(value = "收费标准代号", index = 8)
    private String standardCode;
    @ExcelProperty(value = "收费备注", index = 9)
    private String remark;
    /**
     * 导入情况
     */
    @ExcelProperty(value = "导入情况", index = 10)
    private String importSituation;
}
