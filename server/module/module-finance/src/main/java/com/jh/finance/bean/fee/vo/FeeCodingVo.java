package com.jh.finance.bean.fee.vo;

import com.jh.finance.bean.fee.FeeCoding;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 收费赋码Vo
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@Schema(description = "FeeCodingVo")
public class FeeCodingVo extends FeeCoding {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;

}