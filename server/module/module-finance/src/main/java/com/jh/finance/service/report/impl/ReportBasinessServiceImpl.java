package com.jh.finance.service.report.impl;

import com.jh.finance.bean.report.ReportBasiness;
import com.jh.finance.bean.report.vo.ReportBasinessVo;
import com.jh.finance.dao.report.ReportBasinessMapper;
import com.jh.finance.service.report.ReportBasinessService;
import com.jh.utils.ImportService;

import java.util.List;
import java.util.Date;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;
import com.jh.common.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;
import static com.github.pagehelper.page.PageMethod.orderBy;

/**
 * 收费统计表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@Service
@Transactional(readOnly = true)
public class ReportBasinessServiceImpl extends BaseServiceImpl<ReportBasinessMapper, ReportBasiness> implements ReportBasinessService {
	
	private static final Logger logger = LoggerFactory.getLogger(ReportBasinessServiceImpl.class);
    @Autowired
    private ReportBasinessMapper reportBasinessMapper;

    @Autowired
	private ImportService importService;

	@Override
	@Transactional(readOnly = false)
	/**
	 * 保存或更新收费统计表
	 *@param reportBasiness 收费统计表对象
	 *@return String 收费统计表ID
	 *<AUTHOR>
	 */
	public String saveOrUpdateReportBasiness(ReportBasiness reportBasiness) {
		if(reportBasiness==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(reportBasiness.getId())){
			//新增
			reportBasiness.setId(UUIDUtils.getUUID());
			reportBasiness.setCreateTime(new Date());
			reportBasiness.setUpdateTime(new Date());
			reportBasiness.setYn(CommonConstant.FLAG_YES);
			reportBasinessMapper.insertSelective(reportBasiness);
		}else{
			//TODO 做判断后方能执行修改 一般判断是否是自己的数据
			//避免页面传入修改
			reportBasiness.setUpdateTime(new Date());
			reportBasinessMapper.updateByPrimaryKeySelective(reportBasiness);
		}
		return reportBasiness.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 * 删除收费统计表
	 *@param ids void 收费统计表ID
	 *<AUTHOR>
	 */
	public void deleteReportBasiness(List<String> ids) {
		for(String id:ids){
			//TODO 做判断后方能执行删除 一般判断是否是自己的数据
			ReportBasiness reportBasiness=reportBasinessMapper.selectByPrimaryKey(id);
			if(reportBasiness==null){
				throw new ServiceException("非法请求");
			}
			//逻辑删除
			ReportBasiness tempReportBasiness=new ReportBasiness();
			tempReportBasiness.setYn(CommonConstant.FLAG_NO);
			tempReportBasiness.setId(reportBasiness.getId());
			reportBasinessMapper.updateByPrimaryKeySelective(tempReportBasiness);
		}
	}

	/**
	 * 查询收费统计表详情
	 *@param id
	 *@return ReportBasiness
	 *<AUTHOR>
	 */
    @Override
	public ReportBasiness findById(String id) {
	    // TODO 做判断后方能反回数据 一般判断是否是自己的数据
		return reportBasinessMapper.selectByPrimaryKey(id);
	}

	/**
	 * 分页查询收费统计表
	 *@param reportBasinessVo
	 *@return PageInfo<ReportBasiness>
	 *<AUTHOR>
	 */
	@Override
	public PageInfo<ReportBasiness> findPageByQuery(ReportBasinessVo reportBasinessVo) {
		// TODO 做判断后方能执行查询 一般判断是否是自己的数据
		PageHelper.startPage(reportBasinessVo.getPageNum(),reportBasinessVo.getPageSize());
		orderBy(reportBasinessVo.getOrderColumn() + " " + reportBasinessVo.getOrderValue());
		Example example=getExample(reportBasinessVo);
		List<ReportBasiness> reportBasinessList=reportBasinessMapper.selectByExample(example);
		return new PageInfo<ReportBasiness>(reportBasinessList);
	}
	
	private Example getExample(ReportBasinessVo reportBasinessVo){
		Example example=new Example(ReportBasiness.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		example.orderBy("createTime").desc();
		return example;
	}
} 