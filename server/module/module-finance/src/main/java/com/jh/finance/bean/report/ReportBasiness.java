package com.jh.finance.bean.report;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.jh.finance.config.StringFWBXssDeserializer2;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.jh.common.bean.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 收费统计表对象 report_basiness
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Table(name = "report_basiness")
@Schema(description = "收费统计表")
@Data
public class ReportBasiness extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "DATA_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "关联主表ID")
    private String dataId;

    @Column(name = "BUSINESST_TYPE_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "类型ID")
    private String businesstTypeId;

    @Column(name = "BUSINESST_TYPE_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "类型名称")
    @JsonDeserialize(using = StringFWBXssDeserializer2.class)
    private String businesstTypeName;

    @Column(name = "COUNT")
    @ColumnType(jdbcType = JdbcType.FLOAT)
    @Schema(description = "业务数量")
    private Float count;

    @Column(name = "POLICY_CONTENT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "减负政策内容")
    private String policyContent;

    @Column(name = "UNIT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "业务单位")
    private String unit;

    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "文件号")
    private String fileNo;


    @Column(name = "EXECUTION_TIME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "执行时间")
    private String executionTime;

    @Column(name = "MONEY")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @Schema(description = "金额")
    private BigDecimal money;

    @Column(name = "REMARK")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "备注")
    private String remark;


    @Column(name = "ROW")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "row")
    private String row;

} 