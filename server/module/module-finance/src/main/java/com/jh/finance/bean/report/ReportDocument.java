package com.jh.finance.bean.report;

import java.util.Date;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.jh.common.bean.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

/**
 * 文件管理对象 report_document
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@Data
@Table(name = "report_document")
@Schema(description = "文件管理")
public class ReportDocument extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "FLOW_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "流程名称")
    private String flowName;
    @Column(name = "FLOW_KEY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "流程KEY")
    private String flowKey;
    @Column(name = "ACT_NODE_KEY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "节点编码")
    private String actNodeKey;
    @Column(name = "ACT_NODE_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "节点名称")
    private String actNodeName;
    @Column(name = "USER_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "用户ID")
    private String userId;
    @Column(name = "TITLE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "文件标题")
    private String title;
    @Column(name = "NUMBER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "文件号")
    private String number;
    @Column(name = "DTYPE")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "文件类型：1-收费文件，2-减负文件")
    private Integer dtype;
    @Column(name = "DGRADE")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "文件级别：1-国家级，2-省级，3-省级以下")
    private Integer dgrade;

    @Column(name = "PUB_DATE")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @Schema(description = "发布日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date pubDate;

    @Column(name = "IS_COMMIT")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "是否提交：0-否，1-是")
    private Integer isCommit;

}
