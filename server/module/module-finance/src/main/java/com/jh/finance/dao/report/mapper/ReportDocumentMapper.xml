<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.finance.dao.report.ReportDocumentMapper" >

    <!-- 根据用户角色和区域查询数据 -->
    <select id="query" parameterType="com.jh.finance.bean.report.vo.ReportDocumentVo" resultType="com.jh.finance.bean.report.ReportDocument">
        SELECT DISTINCT t.*
        FROM report_document t
        INNER JOIN sys_user u ON t.USER_ID = u.ID
        <where>
            t.YN = 1

            <!-- 管理员角色可以查看所有数据 -->
            <if test="hasAdminRole != true">
                <!-- 机构角色只能查看自己的数据 -->
                <if test="hasOrgRole == true and hasFinanceRole != true">
                    AND t.USER_ID = #{userId}
                </if>

                <!-- 财务处可以看到待审核和已审核的所有数据 -->
                <if test="hasFinanceRole == true">
                    AND t.ACT_NODE_KEY IN ('10', '20')
                </if>
            </if>

            <!-- 按流程节点查询 -->
            <if test="actNodeKey != null and actNodeKey != ''">
                AND t.ACT_NODE_KEY = #{actNodeKey}
            </if>

            <!-- 按标题查询 -->
            <if test="title != null and title != ''">
                AND t.TITLE LIKE CONCAT('%', #{title}, '%')
            </if>
        </where>
        ORDER BY t.CREATE_TIME DESC
    </select>

    <select id="queryToDo" resultType="com.jh.finance.bean.report.ReportDocument">
        SELECT DISTINCT t.*
        FROM report_document t
        INNER JOIN sys_user u ON t.USER_ID = u.ID
        <where>
            t.YN = 1

            <!-- 机构角色只能查看自己的数据 -->
            <if test="hasOrgRole == true and hasFinanceRole != true">
                AND t.USER_ID = #{userId} AND t.ACT_NODE_KEY = #{actNodeKey}
            </if>

            <!-- 财务处可以看到待审核和已审核的所有数据 -->
            <if test="hasFinanceRole == true">
                AND t.ACT_NODE_KEY = #{actNodeKey}
            </if>

        </where>
        ORDER BY t.CREATE_TIME DESC
    </select>
</mapper>