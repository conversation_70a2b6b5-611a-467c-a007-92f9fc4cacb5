package com.jh.finance.service.fee;

import com.github.pagehelper.PageInfo;
import com.jh.common.service.BaseService;
import com.jh.finance.bean.fee.FeeNotice;
import com.jh.finance.bean.fee.vo.FeeNoticeVo;

import java.util.List;

/**
 * 收费公示Service接口
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface FeeNoticeService extends BaseService<FeeNotice> {

    /**
     * 保存或更新收费公示
     *
     * @param feeNotice 收费公示对象
     * @return String 收费公示ID
     */
    String saveOrUpdateFeeNotice(FeeNotice feeNotice);

    /**
     * 删除收费公示
     *
     * @param ids 收费公示ID列表
     */
    void deleteFeeNotice(List<String> ids);

    /**
     * 查询收费公示详情
     *
     * @param id 收费公示ID
     * @return FeeNotice 收费公示对象
     */
    FeeNotice findById(String id);

    /**
     * 分页查询收费公示
     *
     * @param vo 查询条件
     * @return PageInfo<FeeNotice> 分页结果
     */
    PageInfo<FeeNotice> findPageByQuery(FeeNoticeVo vo);

    /**
     * 根据机构代码查询最新的已发布收费公示
     *
     * @param orgCode 机构代码
     * @return FeeNotice 最新的已发布收费公示
     */
    FeeNotice findLatestPublishedByOrgCode(String orgCode);

}