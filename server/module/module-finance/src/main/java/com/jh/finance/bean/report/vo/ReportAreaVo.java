package com.jh.finance.bean.report.vo;

import com.jh.finance.bean.report.ReportArea;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 浙江省行政区划Vo
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Schema(description = "ReportAreaVo")
@Data
public class ReportAreaVo extends ReportArea {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;
    
    @Schema(description = "排序列")
    private String orderColumn = "sort";
    
    @Schema(description = "排序方式")
    private String orderValue = "asc";
    
    @Schema(description = "父级区划代码")
    private String parentCode;
    
    @Schema(description = "是否只查询省级")
    private Boolean onlyProvince;
    
    @Schema(description = "是否只查询市级")
    private Boolean onlyCity;
    
    @Schema(description = "是否只查询区县级")
    private Boolean onlyCounty;
}