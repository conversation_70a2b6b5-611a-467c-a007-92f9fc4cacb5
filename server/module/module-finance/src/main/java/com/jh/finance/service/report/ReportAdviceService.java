package com.jh.finance.service.report;

import java.util.List;
import com.github.pagehelper.PageInfo;
import com.jh.finance.bean.report.ReportAdvice;
import com.jh.finance.bean.report.vo.ReportAdviceVo;

/**
 * 数据上报流程-审批意见表Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-10
 */
public interface ReportAdviceService {
	/**
	 * 保存或更新审批意见表
	 *@param reportAdvice 审批意见表对象
	 *@return String 审批意见表ID
	 *<AUTHOR>
	 */
	String saveOrUpdateReportAdvice(ReportAdvice reportAdvice);
	
	/**
	 * 删除审批意见表
	 *@param ids void 审批意见表ID
	 *<AUTHOR>
	 */
	void deleteReportAdvice(List<String> ids);

	/**
	 * 查询审批意见表详情
	 *@param id
	 *@return ReportAdvice
	 *<AUTHOR>
	 */
	ReportAdvice findById(String id);

	/**
	 * 分页查询审批意见表
	 *@param reportAdviceVo
	 *@return PageInfo<ReportAdvice>
	 *<AUTHOR>
	 */
	PageInfo<ReportAdvice> findPageByQuery(ReportAdviceVo reportAdviceVo);
	
	/**
     * 根据申请ID查询审批意见
     * @param applyId 申请ID
     * @return List<ReportAdvice>
     */
    List<ReportAdvice> findByApplyId(String applyId);
    
    /**
     * 根据申请ID和节点查询审批意见
     * @param applyId 申请ID
     * @param stage 节点
     * @return ReportAdvice
     */
    ReportAdvice findByApplyIdAndStage(String applyId, String stage);
} 