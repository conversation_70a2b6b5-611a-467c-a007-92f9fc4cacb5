package com.jh.finance.bean.report;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.jh.common.bean.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

import java.util.List;

/**
 * 浙江省行政区划对象 report_area
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Table(name = "report_area")
@Schema(description = "浙江省行政区划")
@Data
public class ReportArea extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "行政区划代码")
    private String code;

    @Column(name = "NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "行政区划名称")
    private String name;

    @Column(name = "LEVEL")
    @ColumnType(jdbcType = JdbcType.BIGINT)
    @Schema(description = "级别 0省级1市级2区县级")
    private Long level;

    @Column(name = "SORT")
    @ColumnType(jdbcType = JdbcType.BIGINT)
    @Schema(description = "排序")
    private Long sort;
    
    @Transient
    @Schema(description = "子区域列表")
    private List<ReportArea> children;

}
