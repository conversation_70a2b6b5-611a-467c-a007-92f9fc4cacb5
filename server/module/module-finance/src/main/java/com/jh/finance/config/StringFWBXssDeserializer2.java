package com.jh.finance.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.jsoup.Jsoup;
import org.jsoup.safety.Safelist;

import java.io.IOException;

public class StringFWBXssDeserializer2 extends JsonDeserializer<String> {
    @Override
    public String deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
    	if(p==null||p.getText()==null) {
    		return null;
    	}

		String source = p.getText().trim();
		// 把字符串做XSS过滤
		//		source = source.replaceAll("alert", "");
//		source = source.replaceAll("[\\\"\\\'][\\s]*javascript:(.*)[\\\"\\\']", "\"\"");
//		source = source.replaceAll("script", "");
		Safelist list=Safelist.relaxed();
		list.addAttributes("span","style","class");
		list.addAttributes("p","style","class");
		list.addAttributes("div","style","class");
		list.addAttributes("a","style","class");
		list.addAttributes("video","style","class","src","controls","preload","height","width");
		return Jsoup.clean(source,list);
    }

}
