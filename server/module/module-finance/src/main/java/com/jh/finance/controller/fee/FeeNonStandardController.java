package com.jh.finance.controller.fee;

import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;

import com.jh.common.annotation.ExportRespAnnotation;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StreamUtils;
import com.jh.finance.bean.fee.FeeNonStandard;
import com.jh.finance.bean.fee.vo.FeeNonStandardVo;
import com.jh.finance.service.fee.FeeNonStandardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.jh.common.bean.RestApiResponse;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.controller.BaseController;

import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

/**
 * 非标类项目Controller
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@RestController
@RequestMapping("/fee/standard")
@Tag(name = "非标类项目")
public class FeeNonStandardController extends BaseController {
    @Autowired
    private FeeNonStandardService feeNonStandardService;

    private static final String PER_PREFIX = "btn:fee:standard:";

    /**
     * 新增非标类项目
     *
     * @param feeNonStandard 非标类项目数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @Operation(summary = "新增非标类项目")
    @SystemLogAnnotation(type = "非标类项目", value = "新增非标类项目")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveFeeNonStandard(@RequestBody FeeNonStandard feeNonStandard) {
        String id = feeNonStandardService.saveOrUpdateFeeNonStandard(feeNonStandard);
        return RestApiResponse.ok(id);
    }

    /**
     * 修改非标类项目
     *
     * @param feeNonStandard 非标类项目数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @Operation(summary = "修改非标类项目")
    @SystemLogAnnotation(type = "非标类项目", value = "修改非标类项目")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateFeeNonStandard(@RequestBody FeeNonStandard feeNonStandard) {
        String id = feeNonStandardService.saveOrUpdateFeeNonStandard(feeNonStandard);
        return RestApiResponse.ok(id);
    }

    /**
     * 批量删除非标类项目(判断 关联数据是否可以删除)
     *
     * @param ids
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @Operation(summary = "批量删除非标类项目")
    @SystemLogAnnotation(type = "非标类项目", value = "批量删除非标类项目")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteFeeNonStandard(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        feeNonStandardService.deleteFeeNonStandard(ids);
        return RestApiResponse.ok();
    }

    /**
     * 查询非标类项目详情
     *
     * @param id
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findById")
    @Operation(summary = "查询非标类项目详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        FeeNonStandard feeNonStandard = feeNonStandardService.findById(id);
        return RestApiResponse.ok(feeNonStandard);
    }

    /**
     * 分页查询非标类项目
     *
     * @param feeNonStandardVo 非标类项目 查询条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/findPageByQuery")
    @Operation(summary = "分页查询非标类项目")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody FeeNonStandardVo feeNonStandardVo) {
        PageInfo<FeeNonStandard> feeNonStandard = feeNonStandardService.findPageByQuery(feeNonStandardVo);
        return RestApiResponse.ok(feeNonStandard);
    }

    @PostMapping("/excel")
    @Operation(summary = "导出非标类项目")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "excel')")
    // 这里的模块名称和子模块名称，请根据实际情况填写
    @ExportRespAnnotation(modType = "非标类项目", sonMod = "非标类项目", key = "jh_standard_Table", isNotice = true, fileName = "非标类项目")
    public RestApiResponse<?> excel(@RequestBody FeeNonStandardVo feeNonStandardVo) {
        List<FeeNonStandard> feeNonStandardList = feeNonStandardService.findByQuery(feeNonStandardVo);
        //这里对数据进行转换处理，如没使用字典的或关联其它表的数据。如在查询中已处理请忽略
        return RestApiResponse.ok(feeNonStandardList);
    }

    @PostMapping("/import")
    @Operation(summary = "导入非标类项目")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "import')")
    public RestApiResponse<?> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "cover") Integer cover) {
        feeNonStandardService.importFeeNonStandardAsync(file, cover);
        return RestApiResponse.ok();
    }

    /**
     * 下载非标类收费模板
     *
     * @return ResponseEntity<byte [ ]>
     * <AUTHOR>
     */
    @PostMapping("/downloadTemplate")
    @Operation(summary = "下载非标类收费模板")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "template')")
    public ResponseEntity<byte[]> downloadTemplate() {
        try {
            ClassPathResource resource = new ClassPathResource("template/非标类收费模板.xls");
            InputStream inputStream = resource.getInputStream();
            byte[] fileBytes = StreamUtils.copyToByteArray(inputStream);

            // 对中文文件名进行URL编码
            String fileName = "非标类收费模板.xls";
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.add("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
            headers.setContentLength(fileBytes.length);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(fileBytes);
        } catch (IOException e) {
            throw new RuntimeException("模板文件下载失败", e);
        }
    }

    /**
     * 根据机构代码查询检验检测服务项目（非标类）
     *
     * @param orgCode 机构代码
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findNonStandardByOrgCode")
    @Operation(summary = "根据机构代码查询检验检测服务项目（非标类）")
    public RestApiResponse<?> findNonStandardByOrgCode(@RequestParam("orgCode") String orgCode) {
        List<FeeNonStandard> feeNonStandardList = feeNonStandardService.findNonStandardByOrgCode(orgCode);
        return RestApiResponse.ok(feeNonStandardList);
    }
}
