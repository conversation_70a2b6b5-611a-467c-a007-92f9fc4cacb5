package com.jh.finance.bean.report.vo;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;

@Data
@Schema(description = "收费数据统计查询结果")
public class ReportStatisticsQueryVo {

    @Schema(description = "地区代码")
    private String areaCode;

    @Schema(description = "地区名称")
    private String areaName;

    @Schema(description = "计量")
    private BigDecimal metrology = BigDecimal.ZERO;

    @Schema(description = "特检")
    private BigDecimal specialInspection = BigDecimal.ZERO;

    @Schema(description = "质检")
    private BigDecimal qualityInspection = BigDecimal.ZERO;

    @Schema(description = "市场监管系统学（协）会")
    private BigDecimal marketSupervisionAssociation = BigDecimal.ZERO;

    @Schema(description = "标准化")
    private BigDecimal standardization = BigDecimal.ZERO;

    @Schema(description = "知识产权")
    private BigDecimal intellectualProperty = BigDecimal.ZERO;

    @Schema(description = "市场导报社")
    private BigDecimal marketReportSociety = BigDecimal.ZERO;

    @Schema(description = "广告监测")
    private BigDecimal advertisingMonitoring = BigDecimal.ZERO;

    @Schema(description = "食品")
    private BigDecimal food = BigDecimal.ZERO;

    @Schema(description = "药品")
    private BigDecimal medicine = BigDecimal.ZERO;

    // 月份统计字段（用于减负降本数据）
    @Schema(description = "1月")
    private BigDecimal january = BigDecimal.ZERO;

    @Schema(description = "2月")
    private BigDecimal february = BigDecimal.ZERO;

    @Schema(description = "3月")
    private BigDecimal march = BigDecimal.ZERO;

    @Schema(description = "4月")
    private BigDecimal april = BigDecimal.ZERO;

    @Schema(description = "5月")
    private BigDecimal may = BigDecimal.ZERO;

    @Schema(description = "6月")
    private BigDecimal june = BigDecimal.ZERO;

    @Schema(description = "7月")
    private BigDecimal july = BigDecimal.ZERO;

    @Schema(description = "8月")
    private BigDecimal august = BigDecimal.ZERO;

    @Schema(description = "9月")
    private BigDecimal september = BigDecimal.ZERO;

    @Schema(description = "10月")
    private BigDecimal october = BigDecimal.ZERO;

    @Schema(description = "11月")
    private BigDecimal november = BigDecimal.ZERO;

    @Schema(description = "12月")
    private BigDecimal december = BigDecimal.ZERO;

}
