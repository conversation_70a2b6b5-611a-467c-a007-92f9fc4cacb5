package com.jh.finance.dao.report;

import com.jh.common.mybatis.BaseInfoMapper;
import com.jh.finance.bean.report.ReportDocumentFile;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文件管理附件表Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface ReportDocumentFileMapper extends BaseInfoMapper<ReportDocumentFile> {

    /**
     * 根据文件管理ID查询附件列表
     *
     * @param documentId 文件管理ID
     * @return 附件列表
     */
    List<ReportDocumentFile> selectByDocumentId(@Param("documentId") String documentId);

    /**
     * 根据文件管理ID删除附件
     *
     * @param documentId 文件管理ID
     * @return 删除数量
     */
    int deleteByDocumentId(@Param("documentId") String documentId);

}