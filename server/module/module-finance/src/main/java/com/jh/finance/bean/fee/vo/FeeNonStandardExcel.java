package com.jh.finance.bean.fee.vo;

import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 非标类项目对象 fee_non_standard
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
public class FeeNonStandardExcel implements Serializable {
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "收费编码", index = 0)
    private String feeCode;

    @ExcelProperty(value = "收费项目", index = 1)
    private String feeItem;

    @ExcelProperty(value = "收费标准（元）", index = 2)
    private String feeUnit;


    @ExcelProperty(value = "收费标准（元）", index = 3)
    private BigDecimal feeStandard;

    @ExcelProperty(value = "备注", index = 4)
    private String remark;

    @ExcelProperty(value = "收费依据", index = 5)
    private String feeBasis;
    

    /**
     * 导入情况
     */
    @ExcelProperty(value = "导入情况", index = 6)
    private String importSituation;

}
