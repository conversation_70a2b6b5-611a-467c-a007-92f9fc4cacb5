package com.jh.gen.service;

import com.github.pagehelper.PageInfo;
import com.jh.gen.bean.GenTableColumn;
import com.jh.gen.bean.vo.GenTableColumnVo;

import java.util.List;

/**
 * 代码生成业务表字段
 *
 * <AUTHOR>
 * @date 2021-10-15 09:59:26
 */
public interface GenTableColumnService {

    /**
     * @param genTableColumn 代码生成业务表字段对象
     * @return String 代码生成业务表字段ID
     * 保存或更新代码生成业务表字段
     * <AUTHOR>
     */
    String saveOrUpdateGenTableColumn(GenTableColumn genTableColumn);

    /**
     * @param id void 代码生成业务表字段ID
     *           删除代码生成业务表字段
     * <AUTHOR>
     */
    void deleteGenTableColumn(String id);

    /**
     * @param id
     * @return GenTableColumn
     * 查询代码生成业务表字段详情
     * <AUTHOR>
     */
    GenTableColumn findById(String id);

    /**
     * @param genTableColumnVo
     * @return PageInfo<GenTableColumn>
     * 分页查询代码生成业务表字段
     * <AUTHOR>
     */
    PageInfo<GenTableColumn> findPageByQuery(GenTableColumnVo genTableColumnVo);

    /**
     * 查询业务字段列表
     *
     * @param tableId 业务字段编号
     * @return 业务字段集合
     */
    List<GenTableColumn> findGenTableColumnListByTableId(String tableId);
}
