package com.jh.gen.bean;

import com.jh.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Table;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;

/**
 * 代码生成业务表
 *
 * <AUTHOR>
 * @date 2021-10-15 09:59:19
 */
@Table(name = "GEN_TABLE")
@Schema(description = "代码生成业务表")
public class GenTable extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Column(name = "TABLE_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "表名称")
    private String tableName;

    @Column(name = "TABLE_COMMENT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "表描述")
    private String tableComment;

    @Column(name = "SUB_TABLE_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "关联子表的表名")
    private String subTableName;

    @Column(name = "SUB_TABLE_FK_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "子表关联的外键名")
    private String subTableFkName;

    @Column(name = "CLASS_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "实体类名称")
    private String className;

    @Column(name = "TPL_CATEGORY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "使用的模板（CRUD单表操作 TREE树表操作）")
    private String tplCategory;

    @Column(name = "PACKAGE_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "生成包路径")
    private String packageName;

    @Column(name = "MODULE_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "生成模块名")
    private String moduleName;

    @Column(name = "BUSINESS_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "生成业务名")
    private String businessName;

    @Column(name = "FUNCTION_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "生成功能名")
    private String functionName;

    @Column(name = "FUNCTION_AUTHOR")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "生成功能作者")
    private String functionAuthor;

    @Column(name = "GEN_TYPE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "生成代码方式（0ZIP压缩包 1自定义路径）")
    private String genType;

    @Column(name = "GEN_PATH")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "生成路径（不填默认项目路径）")
    private String genPath;

    @Column(name = "OPTIONS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "其它生成选项")
    private String options;

    @Column(name = "REMARK")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "备注")
    private String remark;

    /**
     * GET 表名称
     *
     * @return tableName
     */
    public String getTableName() {
        return tableName;
    }

    /**
     * SET 表名称
     *
     * @param tableName
     */
    public void setTableName(String tableName) {
        this.tableName = tableName == null ? null : tableName.trim();
    }

    /**
     * GET 表描述
     *
     * @return tableComment
     */
    public String getTableComment() {
        return tableComment;
    }

    /**
     * SET 表描述
     *
     * @param tableComment
     */
    public void setTableComment(String tableComment) {
        this.tableComment = tableComment == null ? null : tableComment.trim();
    }

    /**
     * GET 关联子表的表名
     *
     * @return subTableName
     */
    public String getSubTableName() {
        return subTableName;
    }

    /**
     * SET 关联子表的表名
     *
     * @param subTableName
     */
    public void setSubTableName(String subTableName) {
        this.subTableName = subTableName == null ? null : subTableName.trim();
    }

    /**
     * GET 子表关联的外键名
     *
     * @return subTableFkName
     */
    public String getSubTableFkName() {
        return subTableFkName;
    }

    /**
     * SET 子表关联的外键名
     *
     * @param subTableFkName
     */
    public void setSubTableFkName(String subTableFkName) {
        this.subTableFkName = subTableFkName == null ? null : subTableFkName.trim();
    }

    /**
     * GET 实体类名称
     *
     * @return className
     */
    public String getClassName() {
        return className;
    }

    /**
     * SET 实体类名称
     *
     * @param className
     */
    public void setClassName(String className) {
        this.className = className == null ? null : className.trim();
    }

    /**
     * GET 使用的模板（CRUD单表操作 TREE树表操作）
     *
     * @return tplCategory
     */
    public String getTplCategory() {
        return tplCategory;
    }

    /**
     * SET 使用的模板（CRUD单表操作 TREE树表操作）
     *
     * @param tplCategory
     */
    public void setTplCategory(String tplCategory) {
        this.tplCategory = tplCategory == null ? null : tplCategory.trim();
    }

    /**
     * GET 生成包路径
     *
     * @return packageName
     */
    public String getPackageName() {
        return packageName;
    }

    /**
     * SET 生成包路径
     *
     * @param packageName
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName == null ? null : packageName.trim();
    }

    /**
     * GET 生成模块名
     *
     * @return moduleName
     */
    public String getModuleName() {
        return moduleName;
    }

    /**
     * SET 生成模块名
     *
     * @param moduleName
     */
    public void setModuleName(String moduleName) {
        this.moduleName = moduleName == null ? null : moduleName.trim();
    }

    /**
     * GET 生成业务名
     *
     * @return businessName
     */
    public String getBusinessName() {
        return businessName;
    }

    /**
     * SET 生成业务名
     *
     * @param businessName
     */
    public void setBusinessName(String businessName) {
        this.businessName = businessName == null ? null : businessName.trim();
    }

    /**
     * GET 生成功能名
     *
     * @return functionName
     */
    public String getFunctionName() {
        return functionName;
    }

    /**
     * SET 生成功能名
     *
     * @param functionName
     */
    public void setFunctionName(String functionName) {
        this.functionName = functionName == null ? null : functionName.trim();
    }

    /**
     * GET 生成功能作者
     *
     * @return functionAuthor
     */
    public String getFunctionAuthor() {
        return functionAuthor;
    }

    /**
     * SET 生成功能作者
     *
     * @param functionAuthor
     */
    public void setFunctionAuthor(String functionAuthor) {
        this.functionAuthor = functionAuthor == null ? null : functionAuthor.trim();
    }

    /**
     * GET 生成代码方式（0ZIP压缩包 1自定义路径）
     *
     * @return genType
     */
    public String getGenType() {
        return genType;
    }

    /**
     * SET 生成代码方式（0ZIP压缩包 1自定义路径）
     *
     * @param genType
     */
    public void setGenType(String genType) {
        this.genType = genType == null ? null : genType.trim();
    }

    /**
     * GET 生成路径（不填默认项目路径）
     *
     * @return genPath
     */
    public String getGenPath() {
        return genPath;
    }

    /**
     * SET 生成路径（不填默认项目路径）
     *
     * @param genPath
     */
    public void setGenPath(String genPath) {
        this.genPath = genPath == null ? null : genPath.trim();
    }

    /**
     * GET 其它生成选项
     *
     * @return options
     */
    public String getOptions() {
        return options;
    }

    /**
     * SET 其它生成选项
     *
     * @param options
     */
    public void setOptions(String options) {
        this.options = options == null ? null : options.trim();
    }

    /**
     * GET 备注
     *
     * @return remark
     */
    public String getRemark() {
        return remark;
    }

    /**
     * SET 备注
     *
     * @param remark
     */
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }
}