package com.jh.gen.dao;


import com.jh.common.mybatis.BaseInfoMapper;
import com.jh.gen.bean.GenTableColumn;
import com.jh.gen.bean.vo.GenTableColumnVo;

import java.util.List;

/**
 * 代码生成业务表字段
 *
 * <AUTHOR>
 * @date 2021-10-15 09:59:26
 */
public interface GenTableColumnMapper extends BaseInfoMapper<GenTableColumn> {
    /**
     * 根据表名称查询列信息
     *
     * @param tableName 表名称
     * @return 列信息
     */
    List<GenTableColumnVo> findDbTableColumnsByName(String tableName);
}
