package com.jh.gen.bean.vo;

import com.jh.common.util.txt.StringUtils;
import com.jh.gen.bean.GenTable;
import com.jh.gen.bean.GenTableColumn;
import com.jh.gen.constant.GenConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.ArrayUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 代码生成业务表Vo
 *
 * <AUTHOR>
 * @date 2021-10-15 09:59:19
 */
@Schema(description = "代码生成业务表Vo")
public class GenTableVo extends GenTable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;
    /**
     * 数据源
     */
    private String dataSource = "master";

    private String dataType = "mysql";

    /**
     * 主键信息
     */
    private GenTableColumn pkColumn;

    /**
     * 子表信息
     */
    private GenTableVo subTable;

    /**
     * 表列信息
     */
    private List<GenTableColumnVo> columns;
    private Integer pageNum = 1;
    private Integer pageSize = 10;

    /**
     * 树编码字段
     */
    private String treeCode;

    /**
     * 树父编码字段
     */
    private String treeParentCode;

    /**
     * 树名称字段
     */
    private String treeName;

    /**
     * 上级菜单ID字段
     */
    private String parentMenuId;

    /**
     * 上级菜单名称字段
     */
    private String parentMenuName;

    /**
     * 请求参数
     */
    private Map<String, Object> params;

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public static boolean isSub(String tplCategory) {
        return tplCategory != null && StringUtils.equals(GenConstants.TPL_SUB, tplCategory);
    }

    public static boolean isTree(String tplCategory) {
        return tplCategory != null && StringUtils.equals(GenConstants.TPL_TREE, tplCategory);
    }

    public static boolean isCrud(String tplCategory) {
        return tplCategory != null && StringUtils.equals(GenConstants.TPL_CRUD, tplCategory);
    }

    public static boolean isSuperColumn(String tplCategory, String javaField) {
        if (isTree(tplCategory)) {
            return StringUtils.equalsAnyIgnoreCase(javaField,
                    ArrayUtils.addAll(GenConstants.BASE_ENTITY.
                            toArray(new String[GenConstants.BASE_ENTITY.size()])));
        }
        return StringUtils.equalsAnyIgnoreCase(javaField, GenConstants.BASE_ENTITY
                .toArray(new String[GenConstants.BASE_ENTITY.size()]));
    }

    public Map<String, Object> getParams() {
        if (params == null) {
            params = new HashMap<>();
        }
        return params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }

    public String getTreeCode() {
        return treeCode;
    }

    public void setTreeCode(String treeCode) {
        this.treeCode = treeCode;
    }

    public String getTreeParentCode() {
        return treeParentCode;
    }

    public void setTreeParentCode(String treeParentCode) {
        this.treeParentCode = treeParentCode;
    }

    public String getTreeName() {
        return treeName;
    }

    public void setTreeName(String treeName) {
        this.treeName = treeName;
    }

    public String getParentMenuId() {
        return parentMenuId;
    }

    public void setParentMenuId(String parentMenuId) {
        this.parentMenuId = parentMenuId;
    }

    public String getParentMenuName() {
        return parentMenuName;
    }

    public void setParentMenuName(String parentMenuName) {
        this.parentMenuName = parentMenuName;
    }

    public GenTableColumn getPkColumn() {
        return pkColumn;
    }

    public void setPkColumn(GenTableColumn pkColumn) {
        this.pkColumn = pkColumn;
    }

    public GenTableVo getSubTable() {
        return subTable;
    }

    public void setSubTable(GenTableVo subTable) {
        this.subTable = subTable;
    }

    public List<GenTableColumnVo> getColumns() {
        return columns;
    }

    public void setColumns(List<GenTableColumnVo> columns) {
        this.columns = columns;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public boolean isSub() {
        return isSub(getTplCategory());
    }

    public boolean isTree() {
        return isTree(getTplCategory());
    }

    public boolean isCrud() {
        return isCrud(getTplCategory());
    }

    public boolean isSuperColumn(String javaField) {
        return isSuperColumn(getTplCategory(), javaField);
    }

}
