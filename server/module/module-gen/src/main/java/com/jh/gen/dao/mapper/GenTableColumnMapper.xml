<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.gen.dao.GenTableColumnMapper" >
    <resultMap id="CommonBaseResultMap" type="com.jh.common.bean.BaseEntity">
    <result column="ID" property="id" jdbcType="VARCHAR" />
    <result column="YN" property="yn" jdbcType="INTEGER" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR" />
    <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
    <result column="CREATE_USER_NICKNAME" property="createUserNickname" jdbcType="VARCHAR" />
    <result column="UPDATE_USER_NICKNAME" property="updateUserNickname" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 代码生成业务表字段 -->
  <resultMap id="BaseResultMap" type="com.jh.gen.bean.GenTableColumn" extends="CommonBaseResultMap">
    <!--
      WARNING - @mbg.generated
    -->
    <!-- 归属表编号 -->
    <result column="TABLE_ID" property="tableId" jdbcType="VARCHAR" />
    <!-- 列名称 -->
    <result column="COLUMN_NAME" property="columnName" jdbcType="VARCHAR" />
    <!-- 列描述 -->
    <result column="COLUMN_COMMENT" property="columnComment" jdbcType="VARCHAR" />
    <!-- 列类型 -->
    <result column="COLUMN_TYPE" property="columnType" jdbcType="VARCHAR" />
    <!-- JAVA类型 -->
    <result column="JAVA_TYPE" property="javaType" jdbcType="VARCHAR" />
    <!-- JAVA字段名 -->
    <result column="JAVA_FIELD" property="javaField" jdbcType="VARCHAR" />
    <!-- 是否主键（1是） -->
    <result column="IS_PK" property="isPk" jdbcType="VARCHAR" />
    <!-- 是否自增（1是） -->
    <result column="IS_INCREMENT" property="isIncrement" jdbcType="VARCHAR" />
    <!-- 是否必填（1是） -->
    <result column="IS_REQUIRED" property="isRequired" jdbcType="VARCHAR" />
    <!-- 是否为插入字段（1是） -->
    <result column="IS_INSERT" property="isInsert" jdbcType="VARCHAR" />
    <!-- 是否编辑字段（1是） -->
    <result column="IS_EDIT" property="isEdit" jdbcType="VARCHAR" />
    <!-- 是否列表字段（1是） -->
    <result column="IS_LIST" property="isList" jdbcType="VARCHAR" />
    <!-- 是否查询字段（1是） -->
    <result column="IS_QUERY" property="isQuery" jdbcType="VARCHAR" />
    <!-- 查询方式（等于、不等于、大于、小于、范围） -->
    <result column="QUERY_TYPE" property="queryType" jdbcType="VARCHAR" />
    <!-- 显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件） -->
    <result column="HTML_TYPE" property="htmlType" jdbcType="VARCHAR" />
    <!-- 字典类型 -->
    <result column="DICT_TYPE" property="dictType" jdbcType="VARCHAR" />
    <!-- 排序 -->
    <result column="SORT" property="sort" jdbcType="INTEGER" />
  </resultMap>
  
   <select id="findDbTableColumnsByName" parameterType="String" resultType="com.jh.gen.bean.vo.GenTableColumnVo">
		select column_name,
		 (case when (is_nullable = 'no' <![CDATA[ && ]]> column_key != 'PRI') then '1' else null end) as is_required,
		  (case when column_key = 'PRI' then '1' else '0' end) as is_pk,
		   ordinal_position as sort, column_comment,
		    (case when extra = 'auto_increment' then '1' else '0' end) as is_increment,
		     column_type
		from information_schema.columns where table_schema = (select database()) and table_name = (#{tableName})
		order by ordinal_position
	</select>
</mapper>