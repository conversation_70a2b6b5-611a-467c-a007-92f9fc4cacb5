package com.jh.gen.service.impl;

import com.jh.common.util.txt.StringUtils;
import com.jh.gen.bean.vo.GenTableVo;

import java.util.List;

public class GenSql {


    private static final String TYPE_MYSQL = "mysql";
    private static final String TYPE_POSTGRESQL = "postgresql";


    public static String getDbTableListByNames(String[] tableNames,String type) {
        type=type.toLowerCase();
        switch (type) {
            case TYPE_MYSQL: {
                String sql = "select table_name, table_comment, create_time, update_time from information_schema.tables\n" +
                        "\t\twhere table_name NOT LIKE 'act_%' and table_name NOT LIKE 'gen_%' and table_schema = (select database())\n" +
                        "\t\tand table_name in\n (";
                for (String tableName : tableNames) {
                    sql += " ?,";
                }
                sql = sql.substring(0, sql.length() - 1);
                sql += ")";
                return sql;
            }
            case TYPE_POSTGRESQL: {
                String sql = "select tb.table_name, d.description as table_comment " +
                        " FROM information_schema.tables tb\n" +
                        "                 LEFT JOIN pg_class c ON c.relname = tb.table_name\n" +
                        "                 LEFT JOIN pg_description d ON d.objoid = c.oid AND d.objsubid = '0'\n" +
                        "        where table_schema =  current_schema()\n" +
                        "          AND table_name NOT LIKE 'act_%'\n" +
                        "          AND table_name NOT LIKE 'gen_%' " +
                        "\t\tand table_name in\n (";
                for (String tableName : tableNames) {
                    sql += " ?,";
                }
                sql = sql.substring(0, sql.length() - 1);
                sql += ")";
                return sql;
            }
           default:
                return "";
        }
    }

    public static String getGenTableColumn(String type) {
        type=type.toLowerCase();
        switch (type) {
            case TYPE_MYSQL:
                return "select column_name,\n" +
                        "\t\t (case when (is_nullable = 'no'  &&  column_key != 'PRI') then '1' else null end) as is_required,\n" +
                        "\t\t  (case when column_key = 'PRI' then '1' else '0' end) as is_pk,\n" +
                        "\t\t   ordinal_position as sort, column_comment,\n" +
                        "\t\t    (case when extra = 'auto_increment' then '1' else '0' end) as is_increment,\n" +
                        "\t\t     column_type\n" +
                        "\t\tfrom information_schema.columns where table_schema = (select database()) and table_name = (?)\n" +
                        "\t\torder by ordinal_position";
            case TYPE_POSTGRESQL:
                return " select col.column_name,\n" +
                        "              (case when (is_nullable = 'no' and z.ordinal_position != 1) then '1' else null end) as is_required,\n" +
                        "              z.ordinal_position                                                                  as is_pk,\n" +
                        "              col.ordinal_position                                                                as sort,\n" +
                        "              d.description                                                                       as column_comment,\n" +
                        "              (case when (col.is_identity = 'YES') then '1' else null end)                        as is_increment,\n" +
                        "              col.udt_name                                                                        as column_type\n" +
                        "       FROM information_schema.columns col\n" +
                        "                JOIN pg_class c ON c.relname = col.table_name\n" +
                        "                LEFT JOIN pg_description d ON d.objoid = c.oid AND d.objsubid = col.ordinal_position\n" +
                        "\n" +
                        "                LEFT JOIN information_schema.key_column_usage z\n" +
                        "                          on z.column_name = col.column_name and col.table_name = z.table_name and\n" +
                        "                             col.ordinal_position = z.ordinal_position\n" +
                        "\n" +
                        "       where col.table_schema = current_schema()\n" +
                        "         and col.table_name = (?)\n" +
                        "       order by col.ordinal_position";
            default:
                return "";
        }
    }
    private static String convertListToString(List<String> tableNames) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < tableNames.size(); i++) {
            result.append("'").append(tableNames.get(i)).append("'");
            if (i < tableNames.size() - 1) {
                result.append(",");
            }
        }
        if (tableNames.size() == 0) {
            result.append("''");
        }
        return result.toString();
    }
    public static String getCountDbTable(GenTableVo genTable,List<String> tableNames, List<Object> params) {
        String type=genTable.getDataType().toLowerCase();
        switch (type) {
            case TYPE_MYSQL: {
                String countSql = "select count(1) as cc from information_schema.tables";
                String sql = "" +
                        "\t\twhere table_schema = (select database())\n" +
                        "\t\tAND table_name NOT LIKE 'act_%' AND table_name NOT LIKE 'gen_%'\n" +
                        "\t\tAND table_name NOT IN (" + convertListToString(tableNames) + ")\n";
                if (StringUtils.isNotEmpty(genTable.getTableName())) {
                    sql += " AND lower(table_name) like lower(concat('%',?, '%'))\n";
                    params.add(genTable.getTableName());
                }
                if (StringUtils.isNotEmpty(genTable.getTableComment())) {
                    sql += " AND lower(table_comment) like lower(concat('%',?, '%'))\n";
                    params.add(genTable.getTableComment());
                }
                return countSql + sql;
            }
            case TYPE_POSTGRESQL: {
                String countSql="select count(1) as cc ";
                String sql="FROM information_schema.tables tb\n" +
                        "                 LEFT JOIN pg_class c ON c.relname = tb.table_name\n" +
                        "                 LEFT JOIN pg_description d ON d.objoid = c.oid AND d.objsubid = '0'\n" +
                        "        where table_schema =  current_schema()\n" +
                        "          AND table_name NOT LIKE 'act_%'\n" +
                        "          AND table_name NOT LIKE 'gen_%' " +
                        "\t\tAND table_name NOT IN ("+convertListToString(tableNames)+")\n" ;
                if(StringUtils.isNotEmpty(genTable.getTableName())){
                    sql+=" AND lower(table_name) like lower(concat('%',?, '%'))\n";
                    params.add(genTable.getTableName());
                }
                if(StringUtils.isNotEmpty(genTable.getTableComment())){
                    sql+=" AND lower(table_comment) like lower(concat('%',?, '%'))\n";
                    params.add(genTable.getTableComment());
                }
                return countSql+sql;
            }
            default:
                return "";
        }
    }

    public static String getPageDbTable(GenTableVo genTable, List<String> tableNames, List<Object> params) {
        String type=genTable.getDataType().toLowerCase();
        switch (type) {
            case TYPE_MYSQL: {
                String querySql = "select table_name, table_comment, create_time, update_time from information_schema.tables\n";
                String sql = "" +
                        "\t\twhere table_schema = (select database())\n" +
                        "\t\tAND table_name NOT LIKE 'act_%' AND table_name NOT LIKE 'gen_%'\n" +
                        "\t\tAND table_name NOT IN (" + convertListToString(tableNames) + ")\n";
                if (StringUtils.isNotEmpty(genTable.getTableName())) {
                    sql += " AND lower(table_name) like lower(concat('%',?, '%'))\n";
                    params.add(genTable.getTableName());
                }
                if (StringUtils.isNotEmpty(genTable.getTableComment())) {
                    sql += " AND lower(table_comment) like lower(concat('%',?, '%'))\n";
                    params.add(genTable.getTableComment());
                }
                int offset = (genTable.getPageNum() - 1) * genTable.getPageSize();
                sql += " limit " + offset + "," + genTable.getPageSize();
                return querySql + sql;
            }
            case TYPE_POSTGRESQL: {
                String querySql="select tb.table_name, d.description as table_comment ";
                String sql="FROM information_schema.tables tb\n" +
                        "                 LEFT JOIN pg_class c ON c.relname = tb.table_name\n" +
                        "                 LEFT JOIN pg_description d ON d.objoid = c.oid AND d.objsubid = '0'\n" +
                        "        where table_schema =  current_schema()\n" +
                        "          AND table_name NOT LIKE 'act_%'\n" +
                        "          AND table_name NOT LIKE 'gen_%' " +
                        "\t\tAND table_name NOT IN ("+convertListToString(tableNames)+")\n" ;
                if(StringUtils.isNotEmpty(genTable.getTableName())){
                    sql+=" AND lower(table_name) like lower(concat('%',?, '%'))\n";
                    params.add(genTable.getTableName());
                }
                if(StringUtils.isNotEmpty(genTable.getTableComment())){
                    sql+=" AND lower(table_comment) like lower(concat('%',?, '%'))\n";
                    params.add(genTable.getTableComment());
                }
                int offset = (genTable.getPageNum() - 1) * genTable.getPageSize();
                sql += " limit " + offset + "," + genTable.getPageSize();
                return querySql+sql;
            }
            default:
                return "";
        }
    }
}
