package com.jh.gen.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.common.constant.Constant;
import com.jh.common.exception.ServiceException;
import com.jh.common.mybatis.JDBCProperties;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;
import com.jh.common.util.txt.CharsetKit;
import com.jh.common.util.txt.StringUtils;
import com.jh.gen.bean.GenTable;
import com.jh.gen.bean.GenTableColumn;
import com.jh.gen.bean.vo.GenTableColumnVo;
import com.jh.gen.bean.vo.GenTableVo;
import com.jh.gen.config.GenConfig;
import com.jh.gen.constant.GenConstants;
import com.jh.gen.dao.GenTableColumnMapper;
import com.jh.gen.dao.GenTableMapper;
import com.jh.gen.service.GenTableService;
import com.jh.gen.service.util.GenUtils;
import com.jh.gen.service.util.VelocityInitializer;
import com.jh.gen.service.util.VelocityUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.poi.ss.formula.functions.T;
import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ResourceLoader;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.yaml.snakeyaml.Yaml;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.sql.DataSource;
import java.io.*;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 代码生成业务表
 *
 * <AUTHOR>
 * @date 2021-10-15 09:59:19
 */
@Service
@Transactional(readOnly = true)
public class GenTableServiceImpl extends BaseServiceImpl<GenTableMapper, GenTable> implements GenTableService {

    public static final Logger logger = LoggerFactory.getLogger(GenTableServiceImpl.class);

    @Autowired
    private GenTableMapper genTableMapper;
    @Autowired
    private GenTableColumnMapper genTableColumnMapper;

    @Autowired
    private GenConfig genConfig;

    @Autowired
    private Environment environment;
    @Autowired
    private ApplicationContext applicationContext;
    /**
     * 获取代码生成地址
     *
     * @param table    业务表信息
     * @param template 模板文件路径
     * @return 生成地址
     */
    public static String getGenPath(GenTableVo table, String template) {
        String genPath = table.getGenPath();
        if (StringUtils.equals(genPath, "/")) {
            return System.getProperty("user.dir") + File.separator + "src" + File.separator
                    + VelocityUtils.getFileName(template, table);
        }
        return genPath + File.separator + VelocityUtils.getFileName(template, table);
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *  保存或更新代码生成业务表
     * @param genTable 代码生成业务表对象
     * @return String 代码生成业务表ID
     * <AUTHOR>
     */
    public String saveOrUpdateGenTable(GenTable genTable) {
        if (genTable == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(genTable.getId())) {
            // 新增
            genTable.setId(UUIDUtils.getUUID());
            genTableMapper.insertSelective(genTable);
        } else {
            // 避免页面传入修改
            genTable.setYn(null);
            genTableMapper.updateByPrimaryKeySelective(genTable);
        }
        return genTable.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *  删除代码生成业务表
     * @param id void 代码生成业务表ID
     * <AUTHOR>
     */
    public void deleteGenTable(String id) {
        // TODO 做判断后方能执行删除
        GenTable genTable = genTableMapper.selectByPrimaryKey(id);
        if (genTable == null) {
            throw new ServiceException("非法请求");
        }
        // 逻辑删除
        GenTable temgenTable = new GenTable();
        temgenTable.setYn(Constant.NO);
        temgenTable.setId(genTable.getId());
        genTableMapper.updateByPrimaryKeySelective(temgenTable);
    }

    /**
     * @param id
     * @return GenTable
     * 查询代码生成业务表详情
     * <AUTHOR>
     */
    @Override
    public GenTable findById(String id) {
        return genTableMapper.selectByPrimaryKey(id);
    }

    /**
     * @param genTableVo
     * @return PageInfo<GenTable>
     * 分页查询代码生成业务表
     * <AUTHOR>
     */
    @Override
    public PageInfo<GenTable> findPageByQuery(GenTableVo genTableVo) {
        PageHelper.startPage(genTableVo.getPageNum(), genTableVo.getPageSize());
        Example example = new Example(GenTable.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", Constant.YES);
        // 查询条件
        if (!StringUtils.isEmpty(genTableVo.getTableName())) {
            criteria.andLike("tableName", "%" + genTableVo.getTableName() + "%");
        }
        if (!StringUtils.isEmpty(genTableVo.getTableComment())) {
            criteria.andLike("tableComment", "%" + genTableVo.getTableComment() + "%");
        }
        List<GenTable> genTableList = genTableMapper.selectByExample(example);
        return new PageInfo<>(genTableList);
    }


    /**
     * 查询据库列表
     *
     * @param genTable 业务信息
     * @return 数据库表集合
     */
    @Override
    public PageInfo<GenTable> findDbTableList(GenTableVo genTable) {

        Example example = new Example(GenTable.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", Constant.YES);
        List<GenTable> genTableList = genTableMapper.selectByExample(example);
        List<String> tableNames = genTableList.stream().map(GenTable::getTableName).collect(Collectors.toList());

        List<Object> paramsCount = new ArrayList<>();
        String countSql=GenSql.getCountDbTable( genTable,tableNames,paramsCount);
        DataSource dataSource = getDataSource(genTable.getDataSource());
        List<Map<String, Object>> countResult = executeQuery(dataSource, countSql, paramsCount.toArray());
        int total = countResult.isEmpty() ? 0 : countResult.get(0).get("cc") == null ? 0 : Integer.parseInt(countResult.get(0).get("cc").toString());

        List<Object> paramsPage = new ArrayList<>();
        String pageSql=GenSql.getPageDbTable(genTable,tableNames,paramsPage);
        List<Map<String, Object>> list =executeQuery(dataSource,pageSql,paramsPage.toArray());
        List<GenTable> genTables = new ArrayList<>();
        for (Map<String, Object> map : list) {
            GenTable gn = new GenTable();
            gn.setTableName(map.get("table_name").toString());
            gn.setTableComment(map.get("table_comment").toString());
            Object createTimeObj = map.get("create_time");
            if (createTimeObj != null && createTimeObj instanceof LocalDateTime) {
                LocalDateTime createTime = (LocalDateTime) createTimeObj;
                gn.setCreateTime(new Date(java.sql.Timestamp.valueOf(createTime).getTime()));
            }
            // 处理 update_time
            Object updateTimeObj = map.get("update_time");
            if (updateTimeObj != null && updateTimeObj instanceof LocalDateTime) {
                LocalDateTime updateTime = (LocalDateTime) updateTimeObj;
                gn.setUpdateTime(new Date(java.sql.Timestamp.valueOf(updateTime).getTime()));
            }
            genTables.add(gn);
        }

        PageInfo pageInfo=new PageInfo<GenTable>(genTables);
        pageInfo.setPageNum(genTable.getPageNum());
        pageInfo.setPageSize(genTable.getPageSize());
        pageInfo.setTotal(total);
        pageInfo.setPages((int) Math.ceil((double) total / genTable.getPageSize()));

        return pageInfo;
    }
    /**
     * 执行查询
     * @param dataSource 数据源配置
     * @param sql            执行的sql
     * @return 数据
     */
    private List<Map<String, Object>> executeQuery(DataSource dataSource, String sql, Object ... args) {
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        return jdbcTemplate.queryForList(sql,args);
    }
    /**
     * 获取数据源配置
     * @return 数据源
     */
    private static DataSource getDataSource(JDBCProperties jdbcProperties) {
        DriverManagerDataSource dataSource = new DriverManagerDataSource();
        dataSource.setUrl(jdbcProperties.getUrl());
        dataSource.setUsername(jdbcProperties.getUsername());
        dataSource.setPassword(jdbcProperties.getPassword());
        dataSource.setDriverClassName(jdbcProperties.getDriverClassName());
        return dataSource;
    }

    /**
     * 查询据库列表
     *
     * @param tableNames 表名称组
     * @return 数据库表集合
     */
    @Override
    public List<GenTable> findDbTableListByNames(String[] tableNames,String dataSource,String type) {

        DataSource dataSource1 = getDataSource(dataSource);
        if (dataSource1 == null) {
            throw new ServiceException("请先配置数据源");
        }
        if (tableNames.length == 0) {
            throw new ServiceException("请选择要生成的表");
        }
        String sql=GenSql.getDbTableListByNames(tableNames,type);

        List<Map<String, Object>> list =executeQuery(dataSource1,sql,tableNames);
        List<GenTable> genTables = new ArrayList<>();
        for (Map<String, Object> map : list) {
            GenTable gn = new GenTable();
            gn.setTableName(map.get("table_name").toString());
            gn.setTableComment(map.get("table_comment").toString());
            Object createTimeObj = map.get("create_time");
            if (createTimeObj!= null && createTimeObj instanceof LocalDateTime) {
                LocalDateTime createTime = (LocalDateTime) createTimeObj;
                gn.setCreateTime(new Date(java.sql.Timestamp.valueOf(createTime).getTime()));
            }
            // 处理 update_time
            Object updateTimeObj = map.get("update_time");
            if (updateTimeObj!= null && updateTimeObj instanceof LocalDateTime) {
                LocalDateTime updateTime = (LocalDateTime) updateTimeObj;
                gn.setUpdateTime(new Date(java.sql.Timestamp.valueOf(updateTime).getTime()));
            }
            genTables.add(gn);
        }
        return genTables;
    }

    /**
     * 导入表结构
     *
     * @param tableList 导入表列表
     */
    @Override
    @Transactional(readOnly = false)
    public void importGenTable(List<GenTable> tableList, String operName,String dataSource,String type) {
        try {
            DataSource dataSourceConn = getDataSource(dataSource);
            for (GenTable table : tableList) {
                String tableName = table.getTableName();
                GenUtils.initTable(table, operName,genConfig);
                table.setId(UUIDUtils.getUUID());
                int row = genTableMapper.insertSelective(table);
                if (row > 0) {
                    // 保存列信息
                    List<GenTableColumnVo> genTableColumns = getGenTableColumn(tableName,dataSourceConn, type);
                    for (GenTableColumnVo column : genTableColumns) {
                        GenUtils.initColumnField(column, table);
                        column.setId(UUIDUtils.getUUID());
                        genTableColumnMapper.insertSelective(column);
                    }
                }
            }
        } catch (Exception e) {
            throw new ServiceException("导入失败：" + e.getMessage());
        }
    }

    private List<GenTableColumnVo> getGenTableColumn(String tableName,DataSource dataSourceConn,String type) {

        String sql=GenSql.getGenTableColumn(type);
        List<Map<String, Object>> list =executeQuery(dataSourceConn,sql,tableName);
        List<GenTableColumnVo> genTableColumns = new ArrayList<>();
        for (Map<String, Object> map : list) {
            GenTableColumnVo gn = new GenTableColumnVo();
            gn.setColumnName(map.get("column_name")!=null?map.get("column_name").toString():"");
            gn.setColumnComment(map.get("column_comment")!=null?map.get("column_comment").toString():"");
            gn.setIsRequired(map.get("is_required")!=null?map.get("is_required").toString():"");
            gn.setIsPk(map.get("is_pk")!=null?map.get("is_pk").toString():"");
            gn.setSort(map.get("sort")!=null?Integer.parseInt(map.get("sort").toString()):null);
            gn.setIsIncrement(map.get("is_increment")!=null?map.get("is_increment").toString():"");
            gn.setColumnType(map.get("column_type")!=null?map.get("column_type").toString():"");
            genTableColumns.add(gn);
        }
        return genTableColumns;
    }

    /**
     * 预览代码
     *
     * @param tableId 表编号
     * @return 预览数据列表
     */
    @Override
    public Map<String, String> previewCode(String tableId) {
        Map<String, String> dataMap = new LinkedHashMap<>();
        // 查询表信息
        GenTable table = genTableMapper.selectByPrimaryKey(tableId);
        GenTableVo tableVo = new GenTableVo();
        BeanUtils.copyProperties(table, tableVo);
        //设置列信息
        setColumnTable(tableVo);

        // 设置主子表信息
        setSubTable(tableVo);
        // 设置主键列信息
        setPkColumn(tableVo);
        VelocityInitializer.initVelocity();

        VelocityContext context = VelocityUtils.prepareContext(tableVo);

        // 获取模板列表
        List<String> templates = VelocityUtils.getTemplateList(table.getTplCategory());
        for (String template : templates) {
            // 渲染模板
            StringWriter sw = new StringWriter();
            Template tpl = Velocity.getTemplate(template, Constant.UTF8);
            tpl.merge(context, sw);
            dataMap.put(template, sw.toString());
        }
        return dataMap;
    }

    /**
     * 设置列信息
     *
     * @param tableVo
     */
    private void setColumnTable(GenTableVo tableVo) {

        Example example = new Example(GenTableColumn.class);
        example.createCriteria().andEqualTo("tableId", tableVo.getId());
        example.orderBy("sort").asc();
        List<GenTableColumn> genTableColumnList = genTableColumnMapper.selectByExample(example);

        List<GenTableColumnVo> genTableColumnVoList = new ArrayList<>();
        tableVo.setColumns(genTableColumnVoList);
        if (CollectionUtils.isEmpty(genTableColumnList)) {
            return;
        }
        for (GenTableColumn column : genTableColumnList) {
            if(!GenConstants.BASE_ENTITY.contains(column.getJavaField())) {
                GenTableColumnVo columnVo = new GenTableColumnVo();
                BeanUtils.copyProperties(column, columnVo);
                genTableColumnVoList.add(columnVo);
            }
        }
    }

    /**
     * 生成代码（下载方式）
     *
     * @param tableName 表名称
     * @return 数据
     */
    @Override
    public byte[] downloadCode(String tableName) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ZipOutputStream zip = new ZipOutputStream(outputStream);
        generatorCode(tableName, zip);
        IOUtils.closeQuietly(zip);
        return outputStream.toByteArray();
    }

    /**
     * 生成代码（自定义路径）
     *
     * @param tableName 表名称
     */
    @Override
    public void generatorCode(String tableName) {
        GenTable table = findGenTableByName(tableName);
        GenTableVo tableVo = new GenTableVo();
        BeanUtils.copyProperties(table, tableVo);
        //设置列信息
        setColumnTable(tableVo);
        // 设置主子表信息
        setSubTable(tableVo);
        // 设置主键列信息
        setPkColumn(tableVo);

        VelocityInitializer.initVelocity();

        VelocityContext context = VelocityUtils.prepareContext(tableVo);

        // 获取模板列表
        List<String> templates = VelocityUtils.getTemplateList(table.getTplCategory());
        for (String template : templates) {
            if (!StringUtils.containsAny(template, "sql.vm", "api.js.vm", "index.vue.vm", "index-tree.vue.vm")) {
                // 渲染模板
                StringWriter sw = new StringWriter();
                Template tpl = Velocity.getTemplate(template, Constant.UTF8);
                tpl.merge(context, sw);
                try {
                    String path = getGenPath(tableVo, template);
                    FileUtils.writeStringToFile(new File(path), sw.toString(), CharsetKit.UTF_8);
                } catch (IOException e) {
                    throw new ServiceException("渲染模板失败，表名：" + table.getTableName());
                }
            }
        }
    }

    /**
     * 根据表名查询
     *
     * @param tableName
     * @return
     */
    private GenTable findGenTableByName(String tableName) {
        Example example = new Example(GenTable.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", Constant.YES);
        // 查询条件
        criteria.andEqualTo("tableName", tableName);
        List<GenTable> tableList = genTableMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(tableList)) {
            throw new ServiceException("非法请求");
        }
        // 查询表信息
        GenTable table = tableList.get(0);
        return table;
    }

    /**
     * 同步数据库
     *
     * @param tableName 表名称
     */
    @Override
    @Transactional(readOnly = false)
    public void synchDb(String tableName) {
        GenTable table = findGenTableByName(tableName);
        GenTableVo tableVo = new GenTableVo();
        BeanUtils.copyProperties(table, tableVo);
        List<GenTableColumnVo> tableColumns = tableVo.getColumns();
        List<String> tableColumnNames = tableColumns.stream().map(GenTableColumn::getColumnName)
                .collect(Collectors.toList());

        List<GenTableColumnVo> dbTableColumns = genTableColumnMapper.findDbTableColumnsByName(tableName);
        if (StringUtils.isEmpty(dbTableColumns)) {
            throw new ServiceException("同步数据失败，原表结构不存在");
        }
        List<String> dbTableColumnNames = dbTableColumns.stream().map(GenTableColumn::getColumnName)
                .collect(Collectors.toList());

        dbTableColumns.forEach(column -> {
            if (!tableColumnNames.contains(column.getColumnName())) {
                GenUtils.initColumnField(column, table);
                column.setId(UUIDUtils.getUUID());
                genTableColumnMapper.insertSelective(column);
            }
        });

        List<GenTableColumn> delColumns = tableColumns.stream()
                .filter(column -> !dbTableColumnNames.contains(column.getColumnName())).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(delColumns)) {

            List<String> cids = delColumns.stream().map(dcol -> dcol.getId()).collect(Collectors.toList());
            for (String id : cids) {
                genTableColumnMapper.deleteByPrimaryKey(id);
            }
        }
    }

    /**
     * 批量生成代码（下载方式）
     *
     * @param tableNames 表数组
     * @return 数据
     */
    @Override
    public byte[] downloadCode(String[] tableNames) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ZipOutputStream zip = new ZipOutputStream(outputStream);
        for (String tableName : tableNames) {
            generatorCode(tableName, zip);
        }
        IOUtils.closeQuietly(zip);
        return outputStream.toByteArray();
    }

    /**
     * 查询表信息并生成代码
     */
    private void generatorCode(String tableName, ZipOutputStream zip) {
        // 查询表信息
        GenTable table = findGenTableByName(tableName);
        GenTableVo tableVo = new GenTableVo();
        BeanUtils.copyProperties(table, tableVo);
        //设置列信息
        setColumnTable(tableVo);
        // 设置主子表信息
        setSubTable(tableVo);
        // 设置主键列信息
        setPkColumn(tableVo);

        VelocityInitializer.initVelocity();

        VelocityContext context = VelocityUtils.prepareContext(tableVo);

        // 获取模板列表
        List<String> templates = VelocityUtils.getTemplateList(table.getTplCategory());
        for (String template : templates) {
            // 渲染模板
            StringWriter sw = new StringWriter();
            Template tpl = Velocity.getTemplate(template, Constant.UTF8);
            tpl.merge(context, sw);
            try {
                // 添加到zip
                zip.putNextEntry(new ZipEntry(VelocityUtils.getFileName(template, tableVo)));
                IOUtils.write(sw.toString(), zip, Constant.UTF8);
                IOUtils.closeQuietly(sw);
                zip.flush();
                zip.closeEntry();
            } catch (IOException e) {
                logger.error("渲染模板失败，表名：" + table.getTableName(), e);
            }
        }
    }

    /**
     * 修改保存参数校验
     *
     * @param genTable 业务信息
     */
    @Override
    public void validateEdit(GenTableVo genTable) {
        if (GenConstants.TPL_TREE.equals(genTable.getTplCategory())) {
            if (StringUtils.isEmpty(genTable.getTreeCode())) {
                throw new ServiceException("树编码字段不能为空");
            } else if (StringUtils.isEmpty(genTable.getTreeParentCode())) {
                throw new ServiceException("树父编码字段不能为空");
            } else if (StringUtils.isEmpty(genTable.getTreeName())) {
                throw new ServiceException("树名称字段不能为空");
            } else if (GenConstants.TPL_SUB.equals(genTable.getTplCategory())) {
                if (StringUtils.isEmpty(genTable.getSubTableName())) {
                    throw new ServiceException("关联子表的表名不能为空");
                } else if (StringUtils.isEmpty(genTable.getSubTableFkName())) {
                    throw new ServiceException("子表关联的外键名不能为空");
                }
            }
        }
    }

    /**
     * 设置主键列信息
     *
     * @param table 业务表信息
     */
    public void setPkColumn(GenTableVo table) {
        for (GenTableColumnVo column : table.getColumns()) {
            if (column.isPk()) {
                table.setPkColumn(column);
                break;
            }
        }
        if (table.getPkColumn() == null) {
            table.setPkColumn(table.getColumns().get(0));
        }
        if (GenConstants.TPL_SUB.equals(table.getTplCategory())) {
            for (GenTableColumnVo column : table.getSubTable().getColumns()) {
                if (column.isPk()) {
                    table.getSubTable().setPkColumn(column);
                    break;
                }
            }
            if (StringUtils.isNull(table.getSubTable().getPkColumn())) {
                table.getSubTable().setPkColumn(table.getSubTable().getColumns().get(0));
            }
        }
    }

    /**
     * 设置主子表信息
     *
     * @param table 业务表信息
     */
    public void setSubTable(GenTableVo table) {
        String subTableName = table.getSubTableName();
        if (StringUtils.isNotEmpty(subTableName)) {
            GenTable tableSub = findGenTableByName(subTableName);
            GenTableVo tableVo = new GenTableVo();
            BeanUtils.copyProperties(tableSub, tableVo);
            table.setSubTable(tableVo);
        }
    }

    /**
     * 设置代码生成其他选项值
     *
     * @param genTable 设置后的生成对象
     */
    public void setTableFromOptions(GenTableVo genTable) {
        JSONObject paramsObj = JSONObject.parseObject(genTable.getOptions());
        if (StringUtils.isNotNull(paramsObj)) {
            String treeCode = paramsObj.getString(GenConstants.TREE_CODE);
            String treeParentCode = paramsObj.getString(GenConstants.TREE_PARENT_CODE);
            String treeName = paramsObj.getString(GenConstants.TREE_NAME);
            String parentMenuId = paramsObj.getString(GenConstants.PARENT_MENU_ID);
            String parentMenuName = paramsObj.getString(GenConstants.PARENT_MENU_NAME);

            genTable.setTreeCode(treeCode);
            genTable.setTreeParentCode(treeParentCode);
            genTable.setTreeName(treeName);
            genTable.setParentMenuId(parentMenuId);
            genTable.setParentMenuName(parentMenuName);
        }
    }

    @Override
    public List<GenTable> findAll() {
        return genTableMapper.selectAll();
    }

    @Override
    @Transactional(readOnly = false)
    public void deleteGenTableByIds(String[] tableIds) {
        for (String id : tableIds) {
            genTableMapper.deleteByPrimaryKey(id);
        }
    }
    @Autowired
    private ResourceLoader resourceLoader;
    @Override
    public List<JSONObject> dbSourceList() {
        List<JSONObject> result = new ArrayList<>();
        LinkedHashMap<String, Object> datasourceMap = getProperties();
        //循环所有数据源配置
        for (String key : datasourceMap.keySet()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("dataSource", key);
            JDBCProperties jdbcProperties = JSONObject.parseObject(JSONObject.toJSONString(datasourceMap.get(key)),JDBCProperties.class);
            jsonObject.put("dataType", jdbcProperties.getPageType());
            result.add(jsonObject);
        }
        return result;
    }
    private DataSource getDataSource(String beanName) {
        return applicationContext.getBean(beanName+"DataSource", DataSource.class);
    }

    private LinkedHashMap<String, Object> getProperties() {
        String active = environment.getProperty("spring.profiles.active");
        // 读取配置文件
        String configFileName = "application-" + active + ".yml";
        Map<String, Object> ymlConfigs=null;
        try (InputStream inputStream =  resourceLoader.getResource("classpath:"+configFileName).getInputStream()) {
            Yaml yaml = new Yaml();
            ymlConfigs = yaml.load(inputStream);
        }catch (Exception e){
            throw new ServiceException("配置加载失败");
        }
        LinkedHashMap<String, Object> springConfig = Optional.ofNullable((LinkedHashMap<String, Object>) ymlConfigs.get("spring"))
                .orElseThrow(() -> new ServiceException("配置加载失败"));
        LinkedHashMap<String, Object> datasourceMap = Optional.ofNullable((LinkedHashMap<String, Object>) springConfig.get("datasource"))
                .orElseThrow(() -> new ServiceException("配置加载失败"));
        return datasourceMap;
    }

}
