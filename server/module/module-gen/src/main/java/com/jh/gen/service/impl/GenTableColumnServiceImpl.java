package com.jh.gen.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.common.constant.Constant;
import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;
import com.jh.gen.bean.GenTableColumn;
import com.jh.gen.bean.vo.GenTableColumnVo;
import com.jh.gen.dao.GenTableColumnMapper;
import com.jh.gen.service.GenTableColumnService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import java.util.List;

/**
 * 代码生成业务表字段
 *
 * <AUTHOR>
 * @date 2021-10-15 09:59:26
 */
@Service
@Transactional(readOnly = true)
public class GenTableColumnServiceImpl extends BaseServiceImpl<GenTableColumnMapper, GenTableColumn> implements GenTableColumnService {

    public static final Logger logger = LoggerFactory.getLogger(GenTableColumnServiceImpl.class);

    @Autowired
    private GenTableColumnMapper genTableColumnMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     * 保存或更新代码生成业务表字段
     *@param genTableColumn 代码生成业务表字段对象
     *@return String 代码生成业务表字段ID
     *<AUTHOR>
     */
    public String saveOrUpdateGenTableColumn(GenTableColumn genTableColumn) {
        if (genTableColumn == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(genTableColumn.getId())) {
            //新增
            genTableColumn.setId(UUIDUtils.getUUID());
            genTableColumnMapper.insertSelective(genTableColumn);
        } else {
            //避免页面传入修改
            genTableColumn.setYn(null);
            genTableColumnMapper.updateByPrimaryKeySelective(genTableColumn);
        }
        return genTableColumn.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     * 删除代码生成业务表字段
     *@param id void 代码生成业务表字段ID
     *<AUTHOR>
     */
    public void deleteGenTableColumn(String id) {
        //TODO 做判断后方能执行删除
        GenTableColumn genTableColumn = genTableColumnMapper.selectByPrimaryKey(id);
        if (genTableColumn == null) {
            throw new ServiceException("非法请求");
        }
        //逻辑删除
        GenTableColumn temgenTableColumn = new GenTableColumn();
        temgenTableColumn.setYn(Constant.NO);
        temgenTableColumn.setId(genTableColumn.getId());
        genTableColumnMapper.updateByPrimaryKeySelective(temgenTableColumn);
    }

    /**
     * @param id
     * @return GenTableColumn
     * 查询代码生成业务表字段详情
     * <AUTHOR>
     */
    @Override
    public GenTableColumn findById(String id) {
        return genTableColumnMapper.selectByPrimaryKey(id);
    }

    /**
     * @param genTableColumnVo
     * @return PageInfo<GenTableColumn>
     * 分页查询代码生成业务表字段
     * <AUTHOR>
     */
    @Override
    public PageInfo<GenTableColumn> findPageByQuery(GenTableColumnVo genTableColumnVo) {
        PageHelper.startPage(genTableColumnVo.getPageNum(), genTableColumnVo.getPageSize());
        Example example = new Example(GenTableColumn.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", Constant.YES);
        example.orderBy("sort").asc();
        List<GenTableColumn> genTableColumnList = genTableColumnMapper.selectByExample(example);
        return new PageInfo<GenTableColumn>(genTableColumnList);
    }

    @Override
    public List<GenTableColumn> findGenTableColumnListByTableId(String tableId) {
        Example example = new Example(GenTableColumn.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", Constant.YES);
        //查询条件
        if (!StringUtils.isEmpty(tableId)) {
            criteria.andEqualTo("tableId", tableId);
        }
        example.orderBy("sort").asc();
        return genTableColumnMapper.selectByExample(example);
    }

}
