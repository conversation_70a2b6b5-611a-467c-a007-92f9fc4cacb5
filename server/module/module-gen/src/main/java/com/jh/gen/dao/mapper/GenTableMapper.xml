<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.gen.dao.GenTableMapper" >
    <resultMap id="CommonBaseResultMap" type="com.jh.common.bean.BaseEntity">
    <result column="ID" property="id" jdbcType="VARCHAR" />
    <result column="YN" property="yn" jdbcType="INTEGER" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR" />
    <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
    <result column="CREATE_USER_NICKNAME" property="createUserNickname" jdbcType="VARCHAR" />
    <result column="UPDATE_USER_NICKNAME" property="updateUserNickname" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 代码生成业务表 -->
  <resultMap id="BaseResultMap" type="com.jh.gen.bean.GenTable" extends="CommonBaseResultMap">
    <!--
      WARNING - @mbg.generated
    -->
    <!-- 表名称 -->
    <result column="TABLE_NAME" property="tableName" jdbcType="VARCHAR" />
    <!-- 表描述 -->
    <result column="TABLE_COMMENT" property="tableComment" jdbcType="VARCHAR" />
    <!-- 关联子表的表名 -->
    <result column="SUB_TABLE_NAME" property="subTableName" jdbcType="VARCHAR" />
    <!-- 子表关联的外键名 -->
    <result column="SUB_TABLE_FK_NAME" property="subTableFkName" jdbcType="VARCHAR" />
    <!-- 实体类名称 -->
    <result column="CLASS_NAME" property="className" jdbcType="VARCHAR" />
    <!-- 使用的模板（CRUD单表操作 TREE树表操作） -->
    <result column="TPL_CATEGORY" property="tplCategory" jdbcType="VARCHAR" />
    <!-- 生成包路径 -->
    <result column="PACKAGE_NAME" property="packageName" jdbcType="VARCHAR" />
    <!-- 生成模块名 -->
    <result column="MODULE_NAME" property="moduleName" jdbcType="VARCHAR" />
    <!-- 生成业务名 -->
    <result column="BUSINESS_NAME" property="businessName" jdbcType="VARCHAR" />
    <!-- 生成功能名 -->
    <result column="FUNCTION_NAME" property="functionName" jdbcType="VARCHAR" />
    <!-- 生成功能作者 -->
    <result column="FUNCTION_AUTHOR" property="functionAuthor" jdbcType="VARCHAR" />
    <!-- 生成代码方式（0ZIP压缩包 1自定义路径） -->
    <result column="GEN_TYPE" property="genType" jdbcType="VARCHAR" />
    <!-- 生成路径（不填默认项目路径） -->
    <result column="GEN_PATH" property="genPath" jdbcType="VARCHAR" />
    <!-- 其它生成选项 -->
    <result column="OPTIONS" property="options" jdbcType="VARCHAR" />
    <!-- 备注 -->
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
  </resultMap>
  
  
  <select id="findDbTableList"  resultMap="BaseResultMap">
		select table_name, table_comment, create_time, update_time from information_schema.tables
		where table_schema = (select database())
		AND table_name NOT LIKE 'act_%' AND table_name NOT LIKE 'gen_%'
		AND table_name NOT IN (select table_name from gen_table)
		<if test="tableName != null and tableName != ''">
			AND lower(table_name) like lower(concat('%', #{tableName}, '%'))
		</if>
		<if test="tableComment != null and tableComment != ''">
			AND lower(table_comment) like lower(concat('%', #{tableComment}, '%'))
		</if>
	</select>
	
	<select id="findDbTableListByNames" resultMap="BaseResultMap">
		select table_name, table_comment, create_time, update_time from information_schema.tables
		where table_name NOT LIKE 'act_%' and table_name NOT LIKE 'gen_%' and table_schema = (select database())
		and table_name in
	    <foreach collection="array" item="name" open="(" separator="," close=")">
 			#{name}
        </foreach> 
	</select>
</mapper>