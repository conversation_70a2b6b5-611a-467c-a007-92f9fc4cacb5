<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
#foreach($column in $columns)
#if($column.query)
#set($dictType=$column.dictType)
#set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($column.htmlType == "input")
      <el-form-item label="${comment}" prop="${column.javaField}">
        <el-input
          v-model="queryParams.${column.javaField}"
          placeholder="请输入${comment}"
          clearable
          style="width: 240px"
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
#elseif(($column.htmlType == "select" || $column.htmlType == "radio") && "" != $dictType)
      <el-form-item label="${comment}" prop="${column.javaField}">
        <el-select v-model="queryParams.${column.javaField}" placeholder="请选择${comment}" clearable size="small">
          <el-option
            v-for="dict in ${column.javaField}Options"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
#elseif(($column.htmlType == "select" || $column.htmlType == "radio") && $dictType)
      <el-form-item label="${comment}" prop="${column.javaField}">
        <el-select v-model="queryParams.${column.javaField}" placeholder="请选择${comment}" clearable size="small">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
#elseif($column.htmlType == "datetime" && $column.queryType != "BETWEEN")
      <el-form-item label="${comment}" prop="${column.javaField}">
        <el-date-picker clearable size="small"
          v-model="queryParams.${column.javaField}"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择${comment}">
        </el-date-picker>
      </el-form-item>
#elseif($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
      <el-form-item label="${comment}">
        <el-date-picker
          v-model="daterange${AttrName}"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
#end
#end
#end
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

   <InitTable :data="${businessName}List" :total="total" :loading="loading" :initDataKey="excelModelCode" @getList="getList"
      :pageNum="queryParams.pageNum" :pageSize="queryParams.pageSize" :isIndex="true" :isCheck="false"
      @handleSortChange="handleSortChange" @handleSelectionChange="handleSelectionChange" @changePage="changePage"
      width="240" :excelModelCode="excelModelCode"
      :excelPermi="excelPermi">
      <template v-slot:action>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" size="mini" @click="handleAdd"
              v-hasPermi="['btn:${moduleName}:${businessName}:save']">新增</el-button>
            <el-button
                plain
                icon="Download"
                size="default"
                @click="expertExcel"
                v-hasPermi="[excelPermi]"
                >导出清单</el-button
              >

              <el-button
                plain
                icon="Upload"
                size="default"
                @click="touploadExcel"
                v-hasPermi="['btn:${moduleName}:${businessName}:import']"
                >导入</el-button
              >

          </el-col>
        </el-row>
      </template>
      <template #operation="scope">
        <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
          v-hasPermi="['btn:${moduleName}:${businessName}:update']">修改</el-button>
        <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
          v-hasPermi="['btn:${moduleName}:${businessName}:delete']">删除</el-button>
      </template>
    </InitTable>


<!-- 导入弹窗 -->
<el-dialog
      v-model="importDialog"
      :title="importDialogTitle"
      width="500px"
      align-center
      :destroy-on-close="true"
    >
      <el-row>
        <el-form-item label="模板下载">
          <el-link
            type="primary"
            :href="excelTemUrl"
            >下载导入模板
          </el-link>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="覆盖导入">
          <el-radio-group v-model="cover" class="ml-4">
            <el-radio label="1" size="large">覆盖</el-radio>
            <el-radio label="0" size="large">不覆盖</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="导入文件">
          <el-upload
            ref="uploadRef"
            class="upload-demo"
            :action="upload.uploadAction"
            :headers="upload.headers"
            :accept="upload.fileAccept"
            :data="upload.data"
            :limit="1"
            :on-change="handleChange"
            :auto-upload="false"
            :before-upload="beforeUpload"
            :on-error="handleError"
            :on-success="handleSuccess"
          >
            <template #trigger>
              <el-button type="primary">选择文件</el-button>
            </template>
            <template #tip>
              <div class="el-upload__tip text-red">请根据模板上传</div>
            </template>
          </el-upload>
        </el-form-item>
      </el-row>
      <template #footer>
        <span class="dialog-footer">
          <el-button class="ml-3" type="success" @click="handleImportSubmit">
            提交
          </el-button>
          <el-button @click="importDialog = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>


    <!-- 添加或修改${functionName}对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
#foreach($column in $columns)
#set($field=$column.javaField)
#if($column.insert && !$column.pk)
#if(($column.usableColumn) || (!$column.superColumn))
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#set($dictType=$column.dictType)
#if($column.htmlType == "input")
        <el-form-item label="${comment}" prop="${field}">
          <el-input v-model="form.${field}" placeholder="请输入${comment}" />
        </el-form-item>
#elseif($column.htmlType == "imageUpload")
        <el-form-item label="${comment}">
          <imageUpload v-model="form.${field}"/>
        </el-form-item>
#elseif($column.htmlType == "fileUpload")
        <el-form-item label="${comment}">
          <fileUpload v-model="form.${field}"/>
        </el-form-item>
#elseif($column.htmlType == "editor")
        <el-form-item label="${comment}">
          <editor v-model="form.${field}" :min-height="192"/>
        </el-form-item>
#elseif($column.htmlType == "select" && "" != $dictType)
        <el-form-item label="${comment}" prop="${field}">
          <el-select v-model="form.${field}" placeholder="请选择${comment}">
            <el-option
              v-for="dict in ${field}Options"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              #if($column.javaType == "Integer" || $column.javaType == "Long"):value="parseInt(dict.dictValue)"#else:value="dict.dictValue"#end

            ></el-option>
          </el-select>
        </el-form-item>
#elseif($column.htmlType == "select" && $dictType)
        <el-form-item label="${comment}" prop="${field}">
          <el-select v-model="form.${field}" placeholder="请选择${comment}">
            <el-option label="请选择字典生成" value="" />
          </el-select>
        </el-form-item>
#elseif($column.htmlType == "checkbox" && "" != $dictType)
        <el-form-item label="${comment}">
          <el-checkbox-group v-model="form.${field}">
            <el-checkbox
              v-for="dict in ${field}Options"
              :key="dict.dictValue"
              :label="dict.dictValue">
              {{dict.dictLabel}}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
#elseif($column.htmlType == "checkbox" && $dictType)
        <el-form-item label="${comment}">
          <el-checkbox-group v-model="form.${field}">
            <el-checkbox>请选择字典生成</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
#elseif($column.htmlType == "radio" && "" != $dictType)
        <el-form-item label="${comment}">
          <el-radio-group v-model="form.${field}">
            <el-radio
              v-for="dict in ${field}Options"
              :key="dict.dictValue"
              #if($column.javaType == "Integer" || $column.javaType == "Long"):label="parseInt(dict.dictValue)"#else:label="dict.dictValue"#end

            >{{dict.dictLabel}}</el-radio>
          </el-radio-group>
        </el-form-item>
#elseif($column.htmlType == "radio" && $dictType)
        <el-form-item label="${comment}">
          <el-radio-group v-model="form.${field}">
            <el-radio label="1">请选择字典生成</el-radio>
          </el-radio-group>
        </el-form-item>
#elseif($column.htmlType == "datetime")
        <el-form-item label="${comment}" prop="${field}">
          <el-date-picker clearable size="small"
            v-model="form.${field}"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择${comment}">
          </el-date-picker>
        </el-form-item>
#elseif($column.htmlType == "textarea")
        <el-form-item label="${comment}" prop="${field}">
          <el-input v-model="form.${field}" type="textarea" placeholder="请输入内容" />
        </el-form-item>
#end
#end
#end
#end
#if($table.sub)
        <el-divider content-position="center">${subTable.functionName}信息</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd${subClassName}">添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleDelete${subClassName}">删除</el-button>
          </el-col>
        </el-row>
        <el-table :data="${subclassName}List" :row-class-name="row${subClassName}Index" @selection-change="handle${subClassName}SelectionChange" ref="${subclassName}">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="序号" align="center" prop="index" width="50"/>
#foreach($column in $subTable.columns)
#set($javaField=$column.javaField)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($column.pk || $javaField == ${subTableFkclassName})
#elseif($column.list && "" != $javaField)
          <el-table-column label="$comment" prop="${javaField}">
            <template slot-scope="scope">
              <el-input v-model="scope.row.$javaField" placeholder="请输入$comment" />
            </template>
          </el-table-column>
#end
#end
        </el-table>
#end
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { list${BusinessName}, get${BusinessName}, del${BusinessName}, add${BusinessName}, update${BusinessName}, export${BusinessName} } from "@/api/${moduleName}/${businessName}";
import { getToken } from '@/utils/auth';
import {  ElLoading } from 'element-plus';
#foreach($column in $columns)
#if($column.insert && !$column.superColumn && !$column.pk && $column.htmlType == "imageUpload")
import ImageUpload from '@/components/ImageUpload';
#break
#end
#end
#foreach($column in $columns)
#if($column.insert && !$column.superColumn && !$column.pk && $column.htmlType == "fileUpload")
import FileUpload from '@/components/FileUpload';
#break
#end
#end
#foreach($column in $columns)
#if($column.insert && !$column.superColumn && !$column.pk && $column.htmlType == "editor")
import Editor from '@/components/Editor';
#break
#end
#end
const router = useRouter();
const route = useRoute();
const { proxy } = getCurrentInstance();

//导出字段配置、显示列配置
const excelModelCode = ref('${moduleName}_${businessName}_Table');
const excelPermi = ref('btn:${moduleName}:${businessName}:excel');

// 遮罩层
const loading =ref(true)
// 选中数组
const ids=ref([])
#if($table.sub)
// 子表选中数据
const checked${subClassName}= ref([])
#end
// 非单个禁用
const single=ref(true)
// 非多个禁用
const multiple=ref(true)
// 显示搜索条件
const showSearch=ref(true)
// 总条数
const total=ref(0)
// ${functionName}表格数据
const ${businessName}List=ref([])
#if($table.sub)
// ${subTable.functionName}表格数据
const ${subclassName}List=ref([])
#end
// 弹出层标题
const title=ref("")
// 是否显示弹出层
const open=ref(false)

// 导入弹出层
const importDialog = ref(false);
// 导入弹出层-标题
const importDialogTitle = ref("${functionName}导入");
const loadingInstance1 = ref(null);
// 是否覆盖
const cover = ref(null);
const excelTemUrl = ref(import.meta.env.VITE_APP_BASE_API + '/static/xxTODO这里是你的模板后台static目录下.xlsx');

#foreach ($column in $columns)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
#set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
// $comment时间范围
const daterange${AttrName}=ref([])
#end
#end
// 查询参数
const data=reactive({queryParams:{
        pageNum: 1,
        pageSize: 10,
#foreach ($column in $columns)
#if($column.query)
        $column.javaField: null#if($velocityCount != $columns.size()),#end
#end
#end
      },
    // 表单参数
    form: {},
    // 表单校验
    rules: {
#foreach ($column in $columns)
#if($column.required)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
        $column.javaField: [
          { required: true, message: "$comment不能为空", trigger: #if($column.htmlType == "select")"change"#else"blur"#end }
        ]#if($velocityCount != $columns.size()),#end

#end
#end
      },
     upload: {
       headers: { Authorization: 'Bearer ' + getToken() },
       uploadAction: import.meta.env.VITE_APP_BASE_API + '/${moduleName}/${businessName}/import',
       fileAccept: '.xls,.xlsx',
       data: {
         cover: cover,
       },
     }
      })
const { queryParams, form, rules,upload } = toRefs(data);
    /** 查询${functionName}列表 */
function getList() {
    loading.value = true;
      proxy.saveListQueryForm(queryParams.value, route.path);
#foreach ($column in $columns)
#if($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
    queryParams.value.params = {};
#break
#end
#end
#foreach ($column in $columns)
#if($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
#set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
    if (null != daterange${AttrName}.value && '' != daterange${AttrName}.value) {
        queryParams.value.params["begin${AttrName}"] = daterange${AttrName}.value[0];
        queryParams.value.params["end${AttrName}"] = daterange${AttrName}.value[1];
    }
#end
#end
    list${BusinessName}(queryParams.value).then(response => {
        ${businessName}List.value = response.data.list;
        total.value = response.data.total;
        loading.value = false;
    });
}
// 取消按钮
function cancel() {
    open.value = false;
    reset();
}
// 表单重置
function reset() {
    form.value = {
#foreach ($column in $columns)
#if(!$table.isSuperColumn($column.javaField))
#if($column.htmlType == "radio")
        $column.javaField: #if($column.javaType == "Integer" || $column.javaType == "Long")0#else"0"#end#if($velocityCount != $columns.size()),#end
#elseif($column.htmlType == "checkbox")
        $column.javaField: []#if($velocityCount != $columns.size()),#end
#else
        $column.javaField: null#if($velocityCount != $columns.size()),#end
#end
#end
#end
      };
#if($table.sub)
    ${subclassName}List.value = [];
#end
    proxy.resetForm("formRef");
}
/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}
/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryForm");
    handleQuery();
}
// 多选框选中数据
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.${pkColumn.javaField})
    single.value = selection.length!==1
    multiple.value = !selection.length
}
/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加${functionName}";
}
/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    const ${pkColumn.javaField} = row.${pkColumn.javaField} || ids.value
    get${BusinessName}(${pkColumn.javaField}).then(response => {
        form.value = response.data;
#foreach ($column in $columns)
#if($column.htmlType == "checkbox")
        form.value.$column.javaField = form.value.${column.javaField}.split(",");
#end
#end
#if($table.sub)
        ${subclassName}List.value = response.data.${subclassName}List;
#end
        open.value = true;
        title.value = "修改${functionName}";
    });
}
/** 提交按钮 */
function submitForm() {
    proxy.#[[$]]#refs["formRef"].validate(valid => {
        if (valid) {
#foreach ($column in $columns)
#if($column.htmlType == "checkbox")
            form.value.$column.javaField = form.value.${column.javaField}.join(",");
#end
#end
#if($table.sub)
            form.value.${subclassName}List = ${subclassName}List.value;
#end
            if (form.value.${pkColumn.javaField} != null) {
                update${BusinessName}(form.value).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    open.value = false;
                    getList();
                });
            } else {
                add${BusinessName}(form.value).then(response => {
                  proxy.$modal.msgSuccess("新增成功");
                  open.value = false;
                  getList();
                });
            }
        }
    });
}
/** 删除按钮操作 */
function handleDelete(row) {
      const ids = row.id || ids.value;
      proxy.$modal
      .confirm('是否确认删除${functionName}编号为“' + ids + '”的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return del${BusinessName}(ids);
        }).then(() => {
          getList();
          proxy.$modal.msgSuccess("删除成功");
        })
}
#if($table.sub)
/** ${subTable.functionName}序号 */
function row${subClassName}Index({ row, rowIndex }) {
      row.index = rowIndex + 1;
}
/** ${subTable.functionName}添加按钮操作 */
function handleAdd${subClassName}() {
    let obj = {};
#foreach($column in $subTable.columns)
#if($column.pk || $column.javaField == ${subTableFkclassName})
#elseif($column.list && "" != $javaField)
    obj.$column.javaField = "";
#end
#end
    ${subclassName}List.value.push(obj);
}
/** ${subTable.functionName}删除按钮操作 */
function handleDelete${subClassName}() {
  if (checked${subClassName}.value.length == 0) {
    proxy.$modal.$alert("请先选择要删除的${subTable.functionName}数据", "提示", { confirmButtonText: "确定", });
  } else {
    ${subclassName}List.value.splice(checked${subClassName}.value[0].index - 1, 1);
  }
}
    /** 单选框选中数据 */
function handle${subClassName}SelectionChange(selection) {
  if (selection.length > 1) {
    proxy.$refs.${subclassName}.clearSelection();
    proxy.$refs.${subclassName}.toggleRowSelection(selection.pop());
  } else {
    checked${subClassName}.value = selection;
  }
}
#end

function handleSortChange(column) {
  const { prop, order } = column;
  if (order) {
    queryParams.value.orderProp = prop;
    queryParams.value.orderValue = order;
  } else {
    queryParams.value.orderProp = '';
    queryParams.value.orderValue = '';
  }
  getList();
}
//分页操作
const changePage = (page) => {
  queryParams.value.pageNum = page.page;
  queryParams.value.pageSize = page.limit;
  getList();
};
proxy.loadListQueryForm(queryParams.value, route.path);
getList();



const expertExcel = () => {
  let params = { ...queryParams.value };
  export${BusinessName}(params).then((response) => {
    proxy.$modal.msgSuccess("正在导出");
  });
};
const touploadExcel = () => {
  // 显示导入对话框
  importDialog.value = true;
};
function handleImportSubmit() {
  if (cover.value == null) {
    proxy.$modal.msgWarning('请选择是否覆盖导入');
    return false;
  }
  proxy.$refs['uploadRef'].submit();
}

// 上传附件之前
function beforeUpload(file) {
  loadingInstance1.value = ElLoading.service({ fullscreen: true });
  const isLt50M = file.size / 1024 / 1024 < 50;
  if (!isLt50M) {
    this.$message.error('上传附件超过 50MB!');
    return false;
  }
  return isLt50M;
}

// 上传失败
function handleError(err, file, fileList) {
  loadingInstance1.value.close();
}
// 上传成功
function handleSuccess(response, file, fileList, code) {
  importDialog.value = false;
  proxy.$modal.msgSuccess('上传成功');
  loadingInstance1.value.close();
}
</script>
