import request from '@/utils/request'

// 查询${functionName}列表
export function list${BusinessName}(query) {
  return request({
    url: '/${moduleName}/${businessName}/findPageByQuery',
    method: 'post',
    data: query
  })
}

// 查询${functionName}详细
export function get${BusinessName}(id) {
  return request({
    url: '/${moduleName}/${businessName}/findById?id=' + id,
    method: 'get'
  })
}

// 新增${functionName}
export function add${BusinessName}(data) {
  return request({
    url: '/${moduleName}/${businessName}/save',
    method: 'post',
    data: data
  })
}

// 修改${functionName}
export function update${BusinessName}(data) {
  return request({
    url: '/${moduleName}/${businessName}/update',
    method: 'post',
    data: data
  })
}

// 删除${functionName}
export function del${BusinessName}(id) {
  var ids=[];
  if(!Array.isArray(id)){
    ids.push(id);
  }else{
    ids=id;
  }
  return request({
    url: '/${moduleName}/${businessName}/delete',
    method: 'post',
    data:ids
  })
}

// 导出${functionName}
export function export${BusinessName}(query) {
  return request({
    url: '/${moduleName}/${businessName}/excel',
    method: 'post',
    data: query
  })
}