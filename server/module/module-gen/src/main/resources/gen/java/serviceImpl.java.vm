package ${packageName}.${moduleName}.service.impl;

import ${packageName}.${moduleName}.bean.vo.${ClassName}Excel;
import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.common.bean.LoginUser;
import com.jh.common.util.AppUserUtil;
import com.jh.common.util.io.FileConvertUtils;
import com.jh.common.util.io.FileUtil;
import com.jh.common.util.poi.EasyExcelUtils;
import com.jh.constant.enums.ImportStatusEnum;
import com.jh.utils.ImportService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.CollectionUtils;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.concurrent.CompletableFuture;

import java.util.List;
import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import ${packageName}.${moduleName}.bean.${ClassName};
import ${packageName}.${moduleName}.bean.vo.${ClassName}Vo;
import ${packageName}.common.exception.ServiceException;
import ${packageName}.common.service.BaseServiceImpl;
import ${packageName}.common.util.security.UUIDUtils;
import ${packageName}.${moduleName}.dao.${ClassName}Mapper;
import ${packageName}.${moduleName}.service.${ClassName}Service;
import ${packageName}.common.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;
import static com.github.pagehelper.page.PageMethod.orderBy;
import org.springframework.web.multipart.MultipartFile;

/**
 *  ${functionName}Service业务层处理
 * 
 * <AUTHOR>
 * @date ${datetime}
 */
@Service
@Transactional(readOnly = true)
public class ${ClassName}ServiceImpl extends BaseServiceImpl<${ClassName}Mapper, ${ClassName}> implements ${ClassName}Service{
	
	private static final Logger logger = LoggerFactory.getLogger(${ClassName}ServiceImpl.class);
    @Autowired
    private ${ClassName}Mapper ${className}Mapper;

    @Autowired
	private ImportService importService;

	@Value("${file.temp.path}")
	private String tempPath;

	@Override
	@Transactional(readOnly = false)
	/**
	 * 保存或更新${functionName}
	 *@param ${className} ${functionName}对象
	 *@return String ${functionName}ID
	 *<AUTHOR>
	 */
	public String saveOrUpdate${ClassName}(${ClassName} ${className}) {
		if(${className}==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(${className}.getId())){
			//新增
			${className}.setId(UUIDUtils.getUUID());
			${className}Mapper.insertSelective(${className});
		}else{
			//TODO 做判断后方能执行修改 一般判断是否是自己的数据
			//避免页面传入修改
			${className}.setYn(null);
			${className}Mapper.updateByPrimaryKeySelective(${className});
		}
		return ${className}.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 * 删除${functionName}
	 *@param ids void ${functionName}ID
	 *<AUTHOR>
	 */
	public void delete${ClassName}(List<String> ids) {
		for(String id:ids){
			//TODO 做判断后方能执行删除 一般判断是否是自己的数据
			${ClassName} ${className}=${className}Mapper.selectByPrimaryKey(id);
			if(${className}==null){
				throw new ServiceException("非法请求");
			}
			//逻辑删除
			${ClassName} tem${className}=new ${ClassName}();
			tem${className}.setYn(CommonConstant.FLAG_NO);
			tem${className}.setId(${className}.getId());
			${className}Mapper.updateByPrimaryKeySelective(tem${className});
		}
	}

	/**
	 * 查询${functionName}详情
	 *@param id
	 *@return ${ClassName}
	 *<AUTHOR>
	 */
    @Override
	public ${ClassName} findById(String id) {
	    // TODO 做判断后方能反回数据 一般判断是否是自己的数据
		return ${className}Mapper.selectByPrimaryKey(id);
	}


	/**
	 * 分页查询${functionName}
	 *@param ${className}Vo
	 *@return PageInfo<${ClassName}>
	 *<AUTHOR>
	 */
	@Override
	public PageInfo<${ClassName}> findPageByQuery(${ClassName}Vo ${className}Vo) {
		// TODO 做判断后方能执行查询 一般判断是否是自己的数据
		PageHelper.startPage(${className}Vo.getPageNum(),${className}Vo.getPageSize());
		orderBy(${className}Vo.getOrderColumn() + " " + ${className}Vo.getOrderValue());
		Example example=getExample(${className}Vo);
		List<${ClassName}> ${className}List=${className}Mapper.selectByExample(example);
		return new PageInfo<${ClassName}>(${className}List);
	}
	private Example getExample(${ClassName}Vo ${className}Vo){
		Example example=new Example(${ClassName}.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(${className}Vo.getName())){
		//	criteria.andEqualTo(${className}Vo.getName());
		//}
		example.orderBy("updateTime").desc();
		return example;
	}

	/**
     * 按条件导出查询${functionName}
     *@param ${className}Vo
     *@return PageInfo<${ClassName}>
     *<AUTHOR>
     */
    @Override
    public List<${ClassName}> findByQuery(${ClassName}Vo ${className}Vo){
    	orderBy(${className}Vo.getOrderColumn() + " " + ${className}Vo.getOrderValue());
        Example example=getExample(${className}Vo);
        return ${className}Mapper.selectByExample(example);
    }

    /**
     * 导入${functionName}
     *@param file
     *@param cover 是否覆盖 1 覆盖 0 不覆盖
     *@return
     *<AUTHOR>
     */
    @Override
    public void import${ClassName}Async(MultipartFile file, Integer cover) {
        //为后续数据处理线程设置用户信息
        Object user = AppUserUtil.getLoginAppUser();
        LoginUser appLoginUser = (LoginUser) user;
        importService.notification(appLoginUser,"已创建${functionName}导入任务，完成后会通知", ExportRespAnnotation.SocketMessageTypeEnum.NORMAL,"${functionName}",null);
        // 开始时间
        Date startTime = new Date();
        // 验证excel
        importService.verifyExcel(file);
        List<${ClassName}Excel> list;
        // 读取数据
        try(InputStream inputStream = file.getInputStream()){
            list = (List<${ClassName}Excel>) EasyExcelUtils.readExcel(${ClassName}Excel.class, inputStream);
            if (CollectionUtils.isEmpty(list)) {
                throw new ServiceException("模板文件为空或者异常，请使用提供的模板进行导入");
            }
        } catch (IOException e) {
            throw new ServiceException("请检查填写内容，如：日期格式");
        }
        // 复制file 文件
        String pathForSave = tempPath + "/" + UUIDUtils.getUUID()+"&&" + file.getOriginalFilename();
        File copyFile = FileUtil.copyFile(file, pathForSave);
        MultipartFile copyMultipartFile = FileConvertUtils.createFileItem(copyFile);
        //这里写数据处理逻辑
        CompletableFuture.runAsync(() -> {
            try {
                // 设置用户信息 避免异步处理数据时丢失用户信息
                UsernamePasswordAuthenticationToken authentication =
                        new UsernamePasswordAuthenticationToken(appLoginUser, null, appLoginUser.getAuthorities());
                SecurityContextHolder.getContext().setAuthentication(authentication);
                List<${ClassName}> insertList = new ArrayList<>();
                //数据处理
                for(${ClassName}Excel item:list){
                    //这里对数据进行校验 TODO
                    if(StringUtils.isEmpty(item.getCode())){
                        item.setImportSituation(ImportStatusEnum.ERROR.getName()+"编码为空");
                        continue;
                    }

                    ${ClassName} ${className} = new ${ClassName}();
                    BeanUtils.copyProperties(item,${className});
                    ${className}.setId(UUIDUtils.getUUID());
                    ${className}.setYn(CommonConstant.FLAG_YES);
                    insertList.add(${className});
                    item.setImportSituation(ImportStatusEnum.IMPORT_SUCCESS.getName());
                }
                //保存数据
                if(!CollectionUtils.isEmpty(insertList)){
                    //这里处理是否覆盖 TODO
                    ${className}Mapper.insertList(insertList);
                }
                String fileUrl = importService.faultDataAsync(list, ${ClassName}Excel.class, "导入结果.xls",
                        "${functionName}", "${functionName}", startTime, copyMultipartFile,appLoginUser);
                importService.notification(appLoginUser,"${functionName}-导入结果.xls", ExportRespAnnotation.SocketMessageTypeEnum.DOWN,file.getOriginalFilename() + "导入成功",fileUrl);
            }catch (Exception e){
                importService.notification(appLoginUser,e.getMessage(), ExportRespAnnotation.SocketMessageTypeEnum.NORMAL,"${functionName}导入【出现异常】",null);
            }finally {
                try {
                    Files.deleteIfExists(Paths.get(pathForSave));
                } catch (Exception e) {
                    logger.error("删除文件失败", e);
                }
            }
        });
    }
}
