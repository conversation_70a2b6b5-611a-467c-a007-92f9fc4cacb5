package ${packageName}.${moduleName}.bean.vo;

#foreach ($import in $importList)
import ${import};
#end

import com.alibaba.excel.annotation.ExcelProperty;
import java.io.Serializable;

/**
 * ${functionName}对象 ${tableName}
 
 * 
 * <AUTHOR>
 * @date ${datetime}
 */
public class ${ClassName}Excel implements Serializable {
    private static final long serialVersionUID = 1L;
#set($index = 0)
#foreach ($column in $columns)
#if(!$table.isSuperColumn($column.javaField))
    @ExcelProperty(value = "${column.columnComment}", index = ${index})
#if($column.columnType == "date")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @JSONField(format="yyyy-MM-dd")
#end
#set($index = $index + 1)
#if($column.columnType == "datetime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
#end
#if($column.columnType == "timestamp")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
#end
	private $column.javaType $column.javaField;
#end
#end
    /**
     * 导入情况
     */
    @ExcelProperty(value = "导入情况", index = $columns.size())
    private String importSituation;

    public String getImportSituation() {
        return importSituation;
    }

    public void setImportSituation(String importSituation) {
        this.importSituation = importSituation;
    }

#foreach ($column in $columns)
#if(!$table.isSuperColumn($column.javaField))
#if($column.javaField.length() > 2 && $column.javaField.substring(1,2).matches("[A-Z]"))
#set($AttrName=$column.javaField)
#else
#set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
#end
    /**
     * SET $column.columnComment
     * @param $column.javaField
     */
    public void set${AttrName}($column.javaType $column.javaField){
#if($column.javaType=="String")
		this.$column.javaField = $column.javaField == null ? null :$column.javaField.trim();
#else
		this.$column.javaField = $column.javaField;
#end
	}
    /**
     * GET $column.columnComment
     * @return $column.javaField
     */
    public $column.javaType get${AttrName}(){
        return $column.javaField;
    }
#end
#end
}
