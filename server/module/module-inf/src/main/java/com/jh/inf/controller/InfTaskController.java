package com.jh.inf.controller;

import com.github.pagehelper.PageInfo;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.controller.BaseController;
import com.jh.common.util.security.UUIDUtils;
import com.jh.common.util.txt.StringUtils;
import com.jh.inf.bean.InfTask;
import com.jh.inf.bean.vo.InfTaskVo;
import com.jh.inf.dao.InfTaskMapper;
import com.jh.inf.service.InfTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

/**
 * 方法任务Controller
 *
 * <AUTHOR>
 * @date 2022-08-10
 */
@RestController
@RequestMapping("/inf/task")
@Tag(name = "方法任务")
public class InfTaskController extends BaseController {
    @Autowired
    private InfTaskService infTaskService;
    @Autowired
    private InfTaskMapper infTaskMapper;

    private static final String PER_PREFIX = "btn:inf:task:";

    /**
     * @param infTask 方法任务数据 json
     * @return RestApiResponse<?>
     * @Description: 新增方法任务
     * @Author: qiubinbin
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
      @Operation(summary="新增方法任务")
    @SystemLogAnnotation(type = "方法任务", value = "新增方法任务")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveInfTask(@RequestBody InfTask infTask) {
        String id = infTaskService.saveOrUpdateInfTask(infTask);
        return RestApiResponse.ok(id);
    }

    /**
     * @param infTask 方法任务数据 json
     * @return RestApiResponse<?>
     * @Description: 修改方法任务
     * @Author: qiubinbin
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
      @Operation(summary="修改方法任务")
    @SystemLogAnnotation(type = "方法任务", value = "修改方法任务")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateInfTask(@RequestBody InfTask infTask) {
        String id = infTaskService.saveOrUpdateInfTask(infTask);
        return RestApiResponse.ok(id);
    }

    /**
     * @param ids
     * @return RestApiResponse<?>
     * @Description: 批量删除方法任务(判断 关联数据是否可以删除)
     * @Author: qiubinbin
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
      @Operation(summary="批量删除方法任务")
    @SystemLogAnnotation(type = "方法任务", value = "批量删除方法任务")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteInfTask(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        infTaskService.deleteInfTask(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询方法任务详情
     * @Author: qiubinbin
     */
    @GetMapping("/findById")
      @Operation(summary="查询方法任务详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        InfTask infTask = infTaskService.findById(id);
        return RestApiResponse.ok(infTask);
    }

    /**
     * @param infTaskVo 方法任务 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询方法任务
     * @Author: qiubinbin
     */
    @PostMapping("/findPageByQuery")
      @Operation(summary="分页查询方法任务")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody InfTaskVo infTaskVo) {
        PageInfo<InfTask> infTask = infTaskService.findPageByQuery(infTaskVo);
        return RestApiResponse.ok(infTask);
    }

    /**
     * 添加脚本任务
     *
     * @Author: qiubinbin
     */
    @PostMapping("/addScript")
      @Operation(summary="添加脚本任务")
    public RestApiResponse<?> addScript(@RequestBody InfTaskVo vo) {
        List<InfTask> list = new ArrayList<>();
        vo.getStrs().forEach(e -> {
            if (e!=null&& StringUtils.isNotEmpty(e.getScriptCode())) {
            	
                InfTask task = new InfTask();
                task.setId(UUIDUtils.getUUID());
                task.setMethodCode(vo.getMethod());
                task.setScriptCode(
                        new String(Base64.getDecoder().decode(e.getScriptCode()), StandardCharsets.UTF_8)
                        );
                list.add(task);
            }
        });
        infTaskMapper.deleteByMethod(vo.getMethod());
        infTaskMapper.insertList(list);
        return RestApiResponse.ok();
    }

    /**
     * 查看脚本
     *
     * @Author: qiubinbin
     */
    @PostMapping("/getScriptByMethod")
      @Operation(summary="查看脚本")
    public RestApiResponse<?> getScriptByMethod(@RequestBody InfTaskVo infTaskVo) {
        List<InfTask> list = infTaskMapper.getScriptByMethod(infTaskVo.getMethod());
        return RestApiResponse.ok(list);
    }


}
