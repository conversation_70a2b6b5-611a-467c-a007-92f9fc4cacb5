package com.jh.inf.bean.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description:第三方系统访问本系统返回数据格式
 * @date 2020年5月13日
 */
@Schema(description = "第三方系统访问本系统返回数据格式")
public class PlatformResultVo implements Serializable {

    private static final long serialVersionUID = -2394491656313949560L;

    @Schema(description = "业务处理状态码")
    private String status;

    @Schema(description = "提示信息")
    private String message;

    @Schema(description = "返回数据")
    private Object data;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }
}
