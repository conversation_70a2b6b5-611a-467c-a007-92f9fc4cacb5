package com.jh.inf.bean;

import com.jh.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Table;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;

/**
 * 应用令牌秘钥配置对象 inf_app

 *
 * <AUTHOR>
 * @date 2022-03-01 12:54:48
 */
@Table(name = "inf_app")
@Schema(description = "应用令牌秘钥配置")
public class InfApp extends BaseEntity {
    private static final long serialVersionUID = 1L;

	@Column(name = "APP_KEY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="应用KEY")
	private String appKey;
	@Column(name = "APP_PUBLIC_KAY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="应用公钥")
	private String appPublicKay;
    @Column(name = "APP_SECRET")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="应用私钥")
    private String appSecret;
	@Column(name = "APP_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="访问平台名称")
	private String appName;
	@Column(name = "STATUS")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description ="接口方法状态：1代表正常，0代表挂起")
	private Integer status;
	@Column(name = "STATUS_TXT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="接口方法状态文本值：1代表正常，0代表挂起")
	private String statusTxt;
	@Column(name = "REMARK")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="备注信息")
	private String remark;

    public String getAppPublicKay() {
        return appPublicKay;
    }

    public void setAppPublicKay(String appPublicKay) {
        this.appPublicKay = appPublicKay;
    }

    /**
     * SET 应用KEY
     * @param appKey
     */
    public void setAppKey(String appKey){
		this.appKey = appKey == null ? null :appKey;
	}
    /**
     * GET 应用KEY
     * @return appKey
     */
    public String getAppKey(){
        return appKey;
    }
    /**
     * SET 应用私钥
     * @param appSecret
     */
    public void setAppSecret(String appSecret){
		this.appSecret = appSecret == null ? null :appSecret;
	}
    /**
     * GET 应用私钥
     * @return appSecret
     */
    public String getAppSecret(){
        return appSecret;
    }
    /**
     * SET 访问平台名称
     * @param appName
     */
    public void setAppName(String appName){
		this.appName = appName == null ? null :appName;
	}
    /**
     * GET 访问平台名称
     * @return appName
     */
    public String getAppName(){
        return appName;
    }
    /**
     * SET 接口方法状态：1代表正常，0代表挂起
     * @param status
     */
    public void setStatus(Integer status){
		this.status = status;
	}
    /**
     * GET 接口方法状态：1代表正常，0代表挂起
     * @return status
     */
    public Integer getStatus(){
        return status;
    }
    /**
     * SET 接口方法状态文本值：1代表正常，0代表挂起
     * @param statusTxt
     */
    public void setStatusTxt(String statusTxt){
		this.statusTxt = statusTxt == null ? null :statusTxt;
	}
    /**
     * GET 接口方法状态文本值：1代表正常，0代表挂起
     * @return statusTxt
     */
    public String getStatusTxt(){
        return statusTxt;
    }
    /**
     * SET 备注信息
     * @param remark
     */
    public void setRemark(String remark){
		this.remark = remark == null ? null :remark;
	}
    /**
     * GET 备注信息
     * @return remark
     */
    public String getRemark(){
        return remark;
    }
    @Override
	public String toString() {
	    return  ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
	}
}
