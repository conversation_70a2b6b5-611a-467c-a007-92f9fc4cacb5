<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.inf.dao.InfTaskMapper" >
    <resultMap id="CommonBaseResultMap" type="com.jh.common.bean.BaseEntity">
    <result column="ID" property="id" jdbcType="VARCHAR" />
    <result column="YN" property="yn" jdbcType="INTEGER" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR" />
    <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
    <result column="CREATE_USER_NICKNAME" property="createUserNickname" jdbcType="VARCHAR" />
    <result column="UPDATE_USER_NICKNAME" property="updateUserNickname" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 方法任务 -->
  <resultMap id="BaseResultMap" type="com.jh.inf.bean.InfTask" extends="CommonBaseResultMap">
    <!--
      WARNING - @mbg.generated
    -->
    <!-- 名称 -->
    <result column="NAME" property="name" jdbcType="VARCHAR" />
    <!-- 脚本代码 -->
    <result column="SCRIPT_CODE" property="scriptCode" jdbcType="VARCHAR" />
    <!-- 方法code -->
    <result column="METHOD_CODE" property="methodCode" jdbcType="VARCHAR" />
    <!-- 类型 -->
    <result column="TASK_TYPE" property="taskType" jdbcType="VARCHAR" />
  </resultMap>
</mapper>
