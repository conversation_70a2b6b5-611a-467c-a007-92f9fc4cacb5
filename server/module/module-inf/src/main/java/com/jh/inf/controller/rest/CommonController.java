package com.jh.inf.controller.rest;

import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.constant.CommonConstant;
import com.jh.common.controller.BaseController;
import com.jh.common.redis.RedisUtil;
import com.jh.constant.RedisConstant;
import com.jh.inf.bean.InfSqlCode;
import com.jh.inf.dao.CommonMapper;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/common")
@Tag(name = "接口配置")
public class CommonController extends BaseController {

	@Autowired
	private CommonMapper commonMapper;
	@Autowired
	private RedisUtil redisUtil;


	@PostMapping("/inf/{code}")
	public RestApiResponse<?> interfaceController(@PathVariable("code") String code, @RequestBody JSONObject json) {
		// key
		String key = RedisConstant.SYS_REDIS + "inf:" + code + ":" + MD5.create().digestHex(json.toString());

		List<InfSqlCode> infSqlCodeList = commonMapper.findInterface(code);

		InfSqlCode sqlCode = infSqlCodeList.get(0);
		List<String> sql = new ArrayList<>();
		sql.add(sqlCode.getSqlCode());
		// 缓存时间
		Integer cacheTime = sqlCode.getCacheExistTime();

		// 需要缓存的接口
		if (CommonConstant.FLAG_YES.equals(sqlCode.getCacheFlag())) {
			// 从缓存里取出结果
			RestApiResponse<?> data = redisUtil.getCacheObject(key);
			//	如果没有结果
			if (data == null) {
				data = getInterfaceData(sql,code,json);
				// 缓存这次的查询结果
				redisUtil.set(key, data, cacheTime);
			}
			return  data;
		}
		// 不需要缓存的接口
		return getInterfaceData(sql,code,json);
	}

	private RestApiResponse<?> getInterfaceData(List<String> sql,String code, JSONObject json) {
		json.put("myQsql", sql.get(0));
		if (json.get("pageSize") != null && json.get("pageNum") != null) {
			PageHelper.startPage(json.getInteger("pageNum"), json.getInteger("pageSize"));
		}
		if (json.get("pageSize") != null && json.get("pageCount") != null) {
			PageHelper.startPage(json.getInteger("pageNum"), json.getInteger("pageCount"));
		}
		List<JSONObject> list = commonMapper.selectDynamicList(json);
		// SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		// 如果参数包含single参数，则获取单条数据
		if (json.containsKey("single")) {
			if (list == null || list.size() == 0) {
				return RestApiResponse.ok();
			}
			return RestApiResponse.ok(list.get(0));
		}
		list.forEach(item -> {
			item.put("UPDATE_TIME","");
			item.put("CREATE_TIME","");
		});
		if (list instanceof Page) {
			return RestApiResponse.ok(new PageInfo<>(list));
		}
		return RestApiResponse.ok(list);
	}
}
