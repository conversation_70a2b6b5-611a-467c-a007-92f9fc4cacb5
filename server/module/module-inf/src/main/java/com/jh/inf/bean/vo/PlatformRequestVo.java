package com.jh.inf.bean.vo;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.jh.common.xss.JSONObjectFWBXssDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;


@Schema(description = "第三方系统访问本系统请求数据格式")
public class PlatformRequestVo {
	/**
	 * appkey
	 */
	@NotNull(message = "appKey不能为空")
	@Size(min = 0, max = 64, message = "appKey不能超过64个字符")
	private String appKey;
	/**
	 * 签名
	 */
	@NotNull(message = " 签名不能为空")
	private String sign;

	/**
	 * 参数
	 */
	@JsonDeserialize(using  = JSONObjectFWBXssDeserializer.class)
	private JSONObject param;
	
	
	/**
	 * 调用方法
	 */
	private String method;

	
	
	public String getMethod() {
		return method;
	}

	public void setMethod(String method) {
		this.method = method;
	}

	/**
	 * appkey
	 */
	public String getAppKey() {
		return appKey;
	}

	/**
	 * appkey
	 */
	public void setAppKey(String appKey) {
		this.appKey = appKey;
	}

	/**
	 * 签名
	 */
	public String getSign() {
		return sign;
	}

	/**
	 * 签名
	 */
	public void setSign(String sign) {
		this.sign = sign;
	}
	/**
	 * 参数
	 */
	public JSONObject getParam() {
		return param;
	}

	/**
	 * 参数
	 */
	public void setParam(JSONObject param) {
		this.param = param;
	}

}
