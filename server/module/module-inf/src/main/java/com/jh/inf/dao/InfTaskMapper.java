package com.jh.inf.dao;

import com.jh.common.mybatis.BaseInfoMapper;
import com.jh.inf.bean.InfTask;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 方法任务Mapper接口
 * @Author: qiubinbin
 * @Date: 2022-08-10
 */
public interface InfTaskMapper extends BaseInfoMapper<InfTask> {

    /**
     * 删除脚本
     *
     * @param method
     */
    @Delete("DELETE FROM inf_task WHERE METHOD_CODE = #{method}")
    void deleteByMethod(@Param("method") String method);

    /**
     * 查看脚本
     *
     * @param method
     */
    @Select("SELECT * FROM inf_task WHERE METHOD_CODE = #{method}")
    List<InfTask> getScriptByMethod(@Param("method") String method);
}
