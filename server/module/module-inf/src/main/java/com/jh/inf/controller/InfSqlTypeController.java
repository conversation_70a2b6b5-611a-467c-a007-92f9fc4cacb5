package com.jh.inf.controller;

import com.github.pagehelper.PageInfo;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.bean.TreeNode;
import com.jh.common.constant.CommonConstant;
import com.jh.common.controller.BaseController;
import com.jh.common.util.TreeUtil;
import com.jh.common.util.txt.StringUtils;
import com.jh.inf.bean.InfSqlType;
import com.jh.inf.bean.vo.InfSqlTypeVo;
import com.jh.inf.service.InfSqlTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 接口类型树Controller
 *
 * <AUTHOR>
 * @date 2022-08-01
 */
@RestController
@RequestMapping("/inf/type")
@Tag(name ="接口类型树")
public class InfSqlTypeController extends BaseController {
    @Autowired
    private InfSqlTypeService infSqlTypeService;

	private static final String PER_PREFIX = "btn:inf:type:";

	 /**
     * @Description 初始化接口类型树
     * @return List<DictTree>
     */
    @RequestMapping(value = "/tree", method = RequestMethod.GET)
      @Operation(summary="查询接口类型树")
    @PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
    @ResponseBody
    public List<TreeNode> getTree() {
        // 获取字典树
        List<InfSqlType> infTypeList = infSqlTypeService.findAll();
        // 生成树
        List<TreeNode> dictTree = getInfTypeTree(infTypeList, CommonConstant.ROOT);
        return dictTree;
    }

    private List<TreeNode> getInfTypeTree(List<InfSqlType> dicts, String root) {
        List<TreeNode> trees = new ArrayList<TreeNode>();
        for (InfSqlType type : dicts) {
        	TreeNode node = new TreeNode();
            node.setParentCode(type.getParentCode());
            node.setId(type.getId());
            node.setCode(type.getTypeCode());
            node.setName(type.getTypeName());
            trees.add(node);
        }
        if (StringUtils.isEmpty(root)) {
            root = "-1";
        }
        return TreeUtil.bulidNode(trees, root);
    }
	/**
	 *@Description: 新增接口类型树
	 *@param infSqlType 接口类型树数据 json
	 *@return RestApiResponse<?>
	 *@Author: linqiang
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	  @Operation(summary="新增接口类型树")
	@SystemLogAnnotation(type = "接口类型树",value = "新增接口类型树")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveInfSqlType(@RequestBody InfSqlType infSqlType) {
		String id = infSqlTypeService.saveOrUpdateInfSqlType(infSqlType);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 修改接口类型树
	 *@param infSqlType 接口类型树数据 json
	 *@return RestApiResponse<?>
	 *@Author: linqiang
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	  @Operation(summary="修改接口类型树")
	@SystemLogAnnotation(type = "接口类型树",value = "修改接口类型树")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateInfSqlType(@RequestBody InfSqlType infSqlType) {
		String id = infSqlTypeService.saveOrUpdateInfSqlType(infSqlType);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 批量删除接口类型树(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: linqiang
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	  @Operation(summary="批量删除接口类型树")
	@SystemLogAnnotation(type = "接口类型树",value = "批量删除接口类型树")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteInfSqlType(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		infSqlTypeService.deleteInfSqlType(ids);
		return RestApiResponse.ok();
	}

	/**
	 *@Description: 查询接口类型树详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: linqiang
	 */
	@GetMapping("/findById")
	  @Operation(summary="查询接口类型树详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		InfSqlType  infSqlType=infSqlTypeService.findById(id);
		return RestApiResponse.ok(infSqlType);
	}

	/**
	 *@Description: 分页查询接口类型树
	 *@param infSqlTypeVo 接口类型树 查询条件
	 *@return RestApiResponse<?>
	 *@Author: linqiang
	 */
	@PostMapping("/findPageByQuery")
	  @Operation(summary="分页查询接口类型树")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody InfSqlTypeVo infSqlTypeVo) {
		PageInfo<InfSqlType>  infSqlType=infSqlTypeService.findPageByQuery(infSqlTypeVo);
		return RestApiResponse.ok(infSqlType);
	}

}
