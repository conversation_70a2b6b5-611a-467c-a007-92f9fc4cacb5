package com.jh.inf.bean;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.jh.common.bean.BaseEntity;
import com.jh.common.xss.StringFWBXssDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Table;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;

/**
 * 方法任务对象 inf_task

 *
 * <AUTHOR>
 * @date 2022-08-10
 */
@Table(name = "inf_task")
@Schema(description = "方法任务")
public class InfTask extends BaseEntity {
    private static final long serialVersionUID = 1L;

	@Column(name = "NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="名称")
	private String name;
	@Column(name = "SCRIPT_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="脚本代码")
	@JsonDeserialize(using = StringFWBXssDeserializer.class)
	private String scriptCode;
	@Column(name = "METHOD_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="方法code")
	private String methodCode;
	@Column(name = "TASK_TYPE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="类型")
	private String taskType;
    /**
     * SET 名称
     * @param name
     */
    public void setName(String name){
		this.name = name == null ? null :name;
	}
    /**
     * GET 名称
     * @return name
     */
    public String getName(){
        return name;
    }
    /**
     * SET 脚本代码
     * @param scriptCode
     */
    public void setScriptCode(String scriptCode){
		this.scriptCode = scriptCode == null ? null :scriptCode;
	}
    /**
     * GET 脚本代码
     * @return scriptCode
     */
    public String getScriptCode(){
        return scriptCode;
    }
    /**
     * SET 方法code
     * @param methodCode
     */
    public void setMethodCode(String methodCode){
		this.methodCode = methodCode == null ? null :methodCode;
	}
    /**
     * GET 方法code
     * @return methodCode
     */
    public String getMethodCode(){
        return methodCode;
    }
    /**
     * SET 类型
     * @param taskType
     */
    public void setTaskType(String taskType){
		this.taskType = taskType == null ? null :taskType;
	}
    /**
     * GET 类型
     * @return taskType
     */
    public String getTaskType(){
        return taskType;
    }
    @Override
	public String toString() {
	    return  ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
	}
}
