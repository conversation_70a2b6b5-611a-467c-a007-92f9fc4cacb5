package com.jh.inf.service;

import com.github.pagehelper.PageInfo;
import com.jh.inf.bean.InfSqlType;
import com.jh.inf.bean.vo.InfSqlTypeVo;

import java.util.List;
/**
 * 接口类型树Service接口
 * 
 * <AUTHOR>
 * @date 2022-08-01
 */
public interface InfSqlTypeService {
	/**
	 *@Description: 保存或更新接口类型树
	 *@param interfaceSqlType 接口类型树对象
	 *@return String 接口类型树ID
	 *@Author: linqiang
	 */
	String saveOrUpdateInfSqlType(InfSqlType interfaceSqlType);
	
	/**
	 *@Description: 删除接口类型树
	 *@param ids void 接口类型树ID
	 *@Author: linqiang
	 */
	void deleteInfSqlType(List<String> ids);

	/**
	 *@Description: 查询接口类型树详情
	 *@param id
	 *@return InfSqlType
	 *@Author: linqiang
	 */
	InfSqlType findById(String id);

	/**
	 *@Description: 分页查询接口类型树
	 *@param interfaceSqlTypeVo
	 *@return PageInfo<InfSqlType>
	 *@Author: linqiang
	 */
	PageInfo<InfSqlType> findPageByQuery(InfSqlTypeVo interfaceSqlTypeVo);

	/**
	 * 查询所有接口类型
	 * @return
	 */
	List<InfSqlType> findAll();
}
