<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.inf.dao.InfAppMapper" >
    <resultMap id="CommonBaseResultMap" type="com.jh.common.bean.BaseEntity">
    <result column="ID" property="id" jdbcType="VARCHAR" />
    <result column="YN" property="yn" jdbcType="INTEGER" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR" />
    <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
    <result column="CREATE_USER_NICKNAME" property="createUserNickname" jdbcType="VARCHAR" />
    <result column="UPDATE_USER_NICKNAME" property="updateUserNickname" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 应用令牌秘钥配置 -->
  <resultMap id="BaseResultMap" type="com.jh.inf.bean.InfApp" extends="CommonBaseResultMap">
    <!--
      WARNING - @mbg.generated
    -->
    <!-- 应用KEY -->
    <result column="APP_KEY" property="appKey" jdbcType="VARCHAR" />
    <!-- 应用公钥 -->
    <result column="APP_PUBLIC_KAY" property="appPublicKay" jdbcType="VARCHAR" />
    <!-- 应用私钥 -->
    <result column="APP_SECRET" property="appSecret" jdbcType="VARCHAR" />
    <!-- 访问平台名称 -->
    <result column="APP_NAME" property="appName" jdbcType="VARCHAR" />
    <!-- 接口方法状态：1代表正常，0代表挂起 -->
    <result column="STATUS" property="status" jdbcType="INTEGER" />
    <!-- 接口方法状态文本值：1代表正常，0代表挂起 -->
    <result column="STATUS_TXT" property="statusTxt" jdbcType="VARCHAR" />
    <!-- 备注信息 -->
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
  </resultMap>

  <select id="selectCountByAppKeyMethod" resultType="Integer">
  	select count(1) from inf_app_method where APP_KEY= #{appKey} and APP_METHOD= #{method}
  </select>

  <insert id="insertAppKeyMethodList">
   INSERT INTO inf_app_method (APP_KEY,APP_METHOD)
        VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.appKey},#{item.appMethod})
        </foreach>
  </insert>

  <update id="updateAppKey">
  update inf_app_method set APP_KEY =#{newAppKey} where APP_KEY = #{appKey}
  </update>
    <update id="updateAppMethod">
  update inf_app_method set APP_METHOD =#{newAppMethod} where APP_METHOD = #{appMethod}
  </update>
  <delete id="deleteAppMethod">
  	delete from inf_app_method where APP_METHOD= #{method}
  </delete>

    <delete id="deleteAppKey">
  	delete from inf_app_method where APP_KEY= #{appKey}
  </delete>
  <select id="findMethodByAppKey" resultType="String">
  	select APP_METHOD  from inf_app_method where APP_KEY= #{appKey}
  </select>
</mapper>
