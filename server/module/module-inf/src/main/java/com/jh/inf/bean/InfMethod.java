package com.jh.inf.bean;

import com.jh.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Table;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;

/**
 * 应用令牌权限对象 inf_method

 *
 * <AUTHOR>
 * @date 2022-03-01 14:06:26
 */
@Table(name = "inf_method")
@Schema(description = "应用令牌权限")
public class InfMethod extends BaseEntity {
    private static final long serialVersionUID = 1L;

	@Column(name = "METHOD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="方法名称")
	private String method;
	@Column(name = "REMARK")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="方法说明")
	private String remark;
    /**
     * SET 方法名称
     * @param method
     */
    public void setMethod(String method){
		this.method = method == null ? null :method;
	}
    /**
     * GET 方法名称
     * @return method
     */
    public String getMethod(){
        return method;
    }
    /**
     * SET 方法说明
     * @param remark
     */
    public void setRemark(String remark){
		this.remark = remark == null ? null :remark;
	}
    /**
     * GET 方法说明
     * @return remark
     */
    public String getRemark(){
        return remark;
    }
    @Override
	public String toString() {
	    return  ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
	}
}
