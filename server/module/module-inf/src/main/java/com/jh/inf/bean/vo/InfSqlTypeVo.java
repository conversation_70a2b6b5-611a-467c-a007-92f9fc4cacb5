package com.jh.inf.bean.vo;

import com.jh.inf.bean.InfSqlType;
import io.swagger.v3.oas.annotations.media.Schema;


/**
 *@Description: 接口类型树Vo
 *@Author: lin<PERSON><PERSON>
 *@Date: 2022-08-01
 */
@Schema(description = "InfSqlTypeVo")
public class InfSqlTypeVo extends InfSqlType {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private Integer pageNum=1;
	private Integer pageSize=20;
	public Integer getPageNum() {
		return pageNum;
	}
	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}
	public Integer getPageSize() {
		return pageSize;
	}
	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}
}