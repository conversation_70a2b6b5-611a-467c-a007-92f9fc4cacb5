<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.inf.dao.InfLogRecordMapper" >
    <resultMap id="CommonBaseResultMap" type="com.jh.common.bean.BaseEntity">
        <result column="ID" property="id" jdbcType="VARCHAR" />
        <result column="YN" property="yn" jdbcType="INTEGER" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR" />
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
        <result column="CREATE_USER_NICKNAME" property="createUserNickname" jdbcType="VARCHAR" />
        <result column="UPDATE_USER_NICKNAME" property="updateUserNickname" jdbcType="VARCHAR" />
    </resultMap>
  <!-- 接口日志记录 -->
  <resultMap id="BaseResultMap" type="com.jh.inf.bean.InfLogRecord" extends="CommonBaseResultMap">
    <!--
      WARNING - @mbg.generated
    -->
    <!-- 应用KEY -->
    <result column="APP_KEY" property="appKey" jdbcType="VARCHAR" />
    <!-- 方法名称 -->
    <result column="METHOD" property="method" jdbcType="VARCHAR" />
    <!-- 是否成功(1成功，0失败) -->
    <result column="FLAG" property="flag" jdbcType="INTEGER" />
    <!-- 访问参数 -->
    <result column="PARAMS" property="params" jdbcType="VARCHAR" />
    <!-- 耗时s -->
    <result column="USE_TIMES" property="useTimes" jdbcType="BIGINT" />
    <!-- 平台名称 -->
    <result column="APP_NAME" property="appName" jdbcType="VARCHAR" />
    <!-- 错误信息 -->
    <result column="ERROR_MSG" property="errorMsg" jdbcType="VARCHAR" />
  </resultMap>
</mapper>
