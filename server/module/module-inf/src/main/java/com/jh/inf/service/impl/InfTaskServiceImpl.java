package com.jh.inf.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.common.constant.CommonConstant;
import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;
import com.jh.inf.bean.InfTask;
import com.jh.inf.bean.vo.InfTaskVo;
import com.jh.inf.dao.InfTaskMapper;
import com.jh.inf.service.InfTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import java.util.List;

/**
 * @Description: 方法任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-08-10
 */
@Service
@Transactional(readOnly = true)
public class InfTaskServiceImpl extends BaseServiceImpl<InfTaskMapper, InfTask> implements InfTaskService {

	private static final Logger logger = LoggerFactory.getLogger(InfTaskServiceImpl.class);
    @Autowired
    private InfTaskMapper infTaskMapper;

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新方法任务
	 *@param infTask 方法任务对象
	 *@return String 方法任务ID
	 *@Author: qiubinbin
	 */
	public String saveOrUpdateInfTask(InfTask infTask) {
		if(infTask==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(infTask.getId())){
			//新增
			infTask.setId(UUIDUtils.getUUID());
			infTaskMapper.insertSelective(infTask);
		}else{
			//避免页面传入修改
			infTask.setYn(null);
			infTaskMapper.updateByPrimaryKeySelective(infTask);
		}
		return infTask.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除方法任务
	 *@param id void 方法任务ID
	 *@Author: qiubinbin
	 */
	public void deleteInfTask(List<String> ids) {
		for(String id:ids){
			//TODO 做判断后方能执行删除
			InfTask interfaceTask= infTaskMapper.selectByPrimaryKey(id);
			if(interfaceTask==null){
				throw new ServiceException("非法请求");
			}
			//逻辑删除
			InfTask teminterfaceTask=new InfTask();
			teminterfaceTask.setYn(CommonConstant.FLAG_NO);
			teminterfaceTask.setId(interfaceTask.getId());
			infTaskMapper.updateByPrimaryKeySelective(teminterfaceTask);
		}
	}

	/**
	 *@Description: 查询方法任务详情
	 *@param id
	 *@return InterfaceTask
	 *@Author: qiubinbin
	 */
    @Override
	public InfTask findById(String id) {
		return infTaskMapper.selectByPrimaryKey(id);
	}


	/**
	 *@Description: 分页查询方法任务
	 *@param infTaskVo
	 *@return PageInfo<InterfaceTask>
	 *@Author: qiubinbin
	 */
	@Override
	public PageInfo<InfTask> findPageByQuery(InfTaskVo infTaskVo) {
		PageHelper.startPage(infTaskVo.getPageNum(),infTaskVo.getPageSize());
		Example example=new Example(InfTask.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(infTaskVo.getName())){
		//	criteria.andEqualTo(infTaskVo.getName());
		//}
		List<InfTask> interfaceTaskList= infTaskMapper.selectByExample(example);
		return new PageInfo<InfTask>(interfaceTaskList);
	}

	@Override
	public List<InfTask> getScriptByMethod(String method) {
		return infTaskMapper.getScriptByMethod(method);
	}
}
