package com.jh.inf.bean;

import com.jh.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Table;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;

/**
 * 接口类型树对象 inf_sql_type

 *
 * <AUTHOR>
 * @date 2022-08-01
 */
@Table(name = "inf_sql_type")
@Schema(description = "接口类型树")
public class InfSqlType extends BaseEntity {
    private static final long serialVersionUID = 1L;

	@Column(name = "README")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="说明")
	private String readme;
	@Column(name = "PARENT_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="上级编码0表示根")
	private String parentCode;
	@Column(name = "TYPE_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="类型编码")
	private String typeCode;
	@Column(name = "TYPE_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="类型说明")
	private String typeName;
    /**
     * SET 说明
     * @param readme
     */
    public void setReadme(String readme){
		this.readme = readme == null ? null :readme;
	}
    /**
     * GET 说明
     * @return readme
     */
    public String getReadme(){
        return readme;
    }
    /**
     * SET 上级编码0表示根
     * @param parentCode
     */
    public void setParamCode(String parentCode){
		this.parentCode = parentCode == null ? null :parentCode;
	}
    /**
     * GET 上级编码0表示根
     * @return parentCode
     */
    public String getParentCode(){
        return parentCode;
    }
    /**
     * SET 类型编码
     * @param typeCode
     */
    public void setTypeCode(String typeCode){
		this.typeCode = typeCode == null ? null :typeCode;
	}
    /**
     * GET 类型编码
     * @return typeCode
     */
    public String getTypeCode(){
        return typeCode;
    }
    /**
     * SET 类型说明
     * @param typeName
     */
    public void setTypeName(String typeName){
		this.typeName = typeName == null ? null :typeName;
	}
    /**
     * GET 类型说明
     * @return typeName
     */
    public String getTypeName(){
        return typeName;
    }
    @Override
	public String toString() {
	    return  ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
	}
}
