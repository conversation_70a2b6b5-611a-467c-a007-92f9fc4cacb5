package com.jh.inf.bean.vo;

import com.jh.inf.bean.InfMethod;
import io.swagger.v3.oas.annotations.media.Schema;


/**
 *@Description: 应用令牌权限Vo
 *@Author: l<PERSON><PERSON><PERSON>
 *@Date: 2022-03-01 14:06:26
 */
@Schema(description = "应用令牌权限Vo")
public class InfMethodVo extends InfMethod {

	/**
	 *
	 */
	private static final long serialVersionUID = 1L;

	private Integer pageNum=1;
	private Integer pageSize=20;
	public Integer getPageNum() {
		return pageNum;
	}
	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}
	public Integer getPageSize() {
		return pageSize;
	}
	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}
}
