package com.jh.inf.bean;

import com.jh.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Table;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;

/**
 * 接口日志记录对象 inf_log_record

 *
 * <AUTHOR>
 * @date 2022-03-01 12:54:48
 */
@Table(name = "inf_log_record")
@Schema(description = "接口日志记录")
public class InfLogRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

	@Column(name = "APP_KEY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="应用KEY")
	private String appKey;
	@Column(name = "METHOD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="方法名称")
	private String method;
	@Column(name = "FLAG")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description ="是否成功(1成功，0失败)")
	private Integer flag;
	@Column(name = "PARAMS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="访问参数")
	private String params;
	@Column(name = "USE_TIMES")
    @ColumnType(jdbcType = JdbcType.BIGINT)
    @Schema(description ="耗时s")
	private Long useTimes;
	@Column(name = "APP_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="平台名称")
	private String appName;
	@Column(name = "ERROR_MSG")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="错误信息")
	private String errorMsg;
    /**
     * SET 应用KEY
     * @param appKey
     */
    public void setAppKey(String appKey){
		this.appKey = appKey == null ? null :appKey;
	}
    /**
     * GET 应用KEY
     * @return appKey
     */
    public String getAppKey(){
        return appKey;
    }
    /**
     * SET 方法名称
     * @param method
     */
    public void setMethod(String method){
		this.method = method == null ? null :method;
	}
    /**
     * GET 方法名称
     * @return method
     */
    public String getMethod(){
        return method;
    }
    /**
     * SET 是否成功(1成功，0失败)
     * @param flag
     */
    public void setFlag(Integer flag){
		this.flag = flag;
	}
    /**
     * GET 是否成功(1成功，0失败)
     * @return flag
     */
    public Integer getFlag(){
        return flag;
    }
    /**
     * SET 访问参数
     * @param params
     */
    public void setParams(String params){
		this.params = params == null ? null :params;
	}
    /**
     * GET 访问参数
     * @return params
     */
    public String getParams(){
        return params;
    }
    /**
     * SET 耗时s
     * @param useTimes
     */
    public void setUseTimes(Long useTimes){
		this.useTimes = useTimes;
	}
    /**
     * GET 耗时s
     * @return useTimes
     */
    public Long getUseTimes(){
        return useTimes;
    }
    /**
     * SET 平台名称
     * @param appName
     */
    public void setAppName(String appName){
		this.appName = appName == null ? null :appName;
	}
    /**
     * GET 平台名称
     * @return appName
     */
    public String getAppName(){
        return appName;
    }
    /**
     * SET 错误信息
     * @param errorMsg
     */
    public void setErrorMsg(String errorMsg){
		this.errorMsg = errorMsg == null ? null :errorMsg;
	}
    /**
     * GET 错误信息
     * @return errorMsg
     */
    public String getErrorMsg(){
        return errorMsg;
    }
    @Override
	public String toString() {
	    return  ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
	}
}
