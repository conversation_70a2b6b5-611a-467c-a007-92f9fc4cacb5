package com.jh.inf.bean.vo;


import com.jh.inf.bean.InfLogRecord;
import io.swagger.v3.oas.annotations.media.Schema;


/**
 *@Description: 接口日志记录Vo
 *@Author: lin<PERSON><PERSON>
 *@Date: 2022-03-01 12:54:48
 */
@Schema(description = "InfLogRecordVo")
public class InfLogRecordVo extends InfLogRecord {

	/**
	 *
	 */
	private static final long serialVersionUID = 1L;

	private Integer pageNum=1;
	private Integer pageSize=20;
	public Integer getPageNum() {
		return pageNum;
	}
	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}
	public Integer getPageSize() {
		return pageSize;
	}
	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}
}
