package com.jh.inf.service;


import com.github.pagehelper.PageInfo;
import com.jh.inf.bean.InfLogRecord;
import com.jh.inf.bean.vo.InfLogRecordVo;
import com.jh.common.service.BaseService;

/**
 * 接口日志记录Service接口
 *
 * <AUTHOR>
 * @date 2022-03-01 12:54:48
 */
public interface InfLogRecordService extends BaseService<InfLogRecord> {

	/**
	 *@Description: 分页查询接口日志记录
	 *@param infLogRecordVo
	 *@return PageInfo<InfLogRecord>
	 *@Author: linqiang
	 */
	PageInfo<InfLogRecord> findPageByQuery(InfLogRecordVo infLogRecordVo);
}
