package com.jh.sys.controller.rest;


import com.alibaba.fastjson2.JSONObject;

import com.jh.common.bean.SysFileInfo;
import com.jh.sys.service.FileService;
import com.jh.sys.service.impl.FileServiceFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.util.Map;

/**
 *
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/rest/sys/baidu/ueditor")
@Tag(name ="百度富文本")
public class RestBaiDuUeditorController {
	@Autowired
	private FileServiceFactory fileServiceFactory;

	@Value("${file.useFileType}")
	private String useFileType;
	@Value("${file.filePath}")
	private String filePath;

	/**
	 * getconfig
	 *
	 * @return
	 */
	@RequestMapping(value = "/config")
	@Operation(summary= "getconfig")
	@ResponseBody
	public void config(HttpServletRequest request, HttpServletResponse response,@RequestParam("action") String action) {
		try {
			if ("config".equals(action)) { // 如果是初始化
				ClassPathResource classPathResource = new ClassPathResource("config.json");
				response.setContentType("text/javascript");
				String callback = request.getParameter("callback");

				BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(classPathResource.getInputStream()));
				StringBuilder stringBuilder = new StringBuilder();
				String line;
				while ((line = bufferedReader.readLine()) != null) {
					stringBuilder.append(line);
				}

				String exec = stringBuilder.toString();

				PrintWriter writer = response.getWriter();
				writer.write(callback + "(" + exec + ");");
				writer.flush();
				writer.close();
			} else if ("uploadimage".equals(action) || "uploadvideo".equals(action) || "uploadfile".equals(action)) { // 如果是上传图片、视频、和其他文件
				response.setContentType("application/json;charset=utf-8");
				MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
				Map<String, MultipartFile> files = multipartRequest.getFileMap();// 得到文件map对象
				for (MultipartFile pic : files.values()) {
					JSONObject jo = new JSONObject();
					long size = pic.getSize(); // 文件大小
					String originalFilename = pic.getOriginalFilename(); // 原来的文件名
					FileService fileService = fileServiceFactory.getFileService(useFileType);
					SysFileInfo fileInfo = fileService.upload(pic);
					if (fileInfo != null && !"".equals(fileInfo.getUrl())) { // 如果上传成功
						jo.put("state", "SUCCESS");
						jo.put("original", originalFilename);// 原来的文件名
						jo.put("size", size); // 文件大小
						jo.put("title", fileInfo.getUrl()); // 随意，代表的是鼠标经过图片时显示的文字
						jo.put("type", originalFilename.substring(originalFilename.lastIndexOf('.') + 1)); // 文件后缀名
						jo.put("url", fileInfo.getUrl());// 这里的url字段表示的是上传后的图片在图片服务器的完整地址（http://ip:端口/***/***/***.jpg）
					} else { // 如果上传失败
					}
					PrintWriter writer = response.getWriter();
					writer.write(jo.toString());
					writer.flush();
					writer.close();
				}
			}
		} catch (Exception e) {

		}

	}


}
