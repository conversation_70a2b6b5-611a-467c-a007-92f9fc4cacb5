package com.jh.sys.service;

import com.github.pagehelper.PageInfo;

import com.jh.common.bean.SysRole;
import com.jh.common.service.BaseService;
import com.jh.sys.bean.vo.RoleAndRoleTypesVO;
import com.jh.sys.bean.vo.SysRoleExportVo;
import com.jh.sys.bean.vo.SysRoleVo;


import java.util.List;

public interface RoleService extends BaseService<SysRole> {

    /**
     * 保存或更新角色
     *
     * @param sysRole 角色Vo
     * @return 结果
     */
    SysRole saveOrUpdateRole(SysRoleVo sysRole);

    /**
     * 修改角色的状态
     *
     * @param roleIds 角色IDs
     * @param status  状态
     */
    void changeStatus(String roleIds, Integer status);

    /**
     * 批量删除角色
     *
     * @param roleIds 角色Ids
     */
    void deleteRole(String roleIds);

    /**
     * 分页查询
     *
     * @param sysRoleVo 角色Vo
     * @return 结果
     */
    PageInfo<RoleAndRoleTypesVO> listPageRoleAndRoleTypeVo(SysRoleVo sysRoleVo);

    /**
     * 查询当前用户添加的角色列表
     *
     * @param sysRoleVo 角色Vo
     * @return 结果
     */
    List<SysRole> listRoleByLoginUser(SysRoleVo sysRoleVo);

    /**
     * 导出角色
     *
     * @param sysRoleVo 角色Vo
     * @return 结果
     */
    List<SysRoleExportVo> export(SysRoleVo sysRoleVo);

    /**
     * 查询角色详情
     *
     * @param id 角色Id
     * @return 结果
     */
    SysRoleVo findById(String id);

    /**
     * 根据ROLE_CODE返回
     *
     * @param roleCode 角色Code
     * @return 结果
     */
    SysRole findRoleByRoleCode(String roleCode);

    /**
     * 启用/禁用 角色动态验证
     * @param roleIds
     * @param status
     */
    void enableDynamicVer(String roleIds, Integer status);
}
