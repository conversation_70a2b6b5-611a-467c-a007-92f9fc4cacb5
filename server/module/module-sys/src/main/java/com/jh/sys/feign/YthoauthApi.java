package com.jh.sys.feign;

import com.jh.sys.bean.rest.OssUser;
import com.jh.sys.bean.rest.YthResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Component
public class YthoauthApi {

    @Autowired
    private RestTemplate restTemplate;

    @Value("${ythoauth.url}")
    private String ythoauthUrl;

    // /**
    //  * 获取已登录用户信息
    //  *
    //  * @param est 设备唯一标识
    //  * @param appName 应用名称
    //  * @return 返回OssUser对象，包含已登录用户信息
    //  */
    // public OssUser ythOssGetLonginUser(String est, String appName) {
    //     String url = ythoauthUrl + "/ythOssGetLonginUser?est=" + est + "&appName=" + appName;
    //     return restTemplate.getForObject(url, OssUser.class);
    // }

    /**
     * 一体化验证用户
     *
     * @param map 包含登录所需参数的Map对象
     * @return 包含验证结果的YthResponse对象
     */
    public YthResponse<String> ythOssLogin(Map<String, ?> map) {
        String url = ythoauthUrl + "/wsite/service/sso/verify";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        MultiValueMap<String, Object> formData = new LinkedMultiValueMap<>();
        map.forEach((key, value) -> formData.add(key, value));

        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(formData, headers);

        ResponseEntity<YthResponse> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                YthResponse.class
        );

        return response.getBody();
    }

    public OssUser ythOssPostLoginUser(Map<String, ?> map) {
        String url = ythoauthUrl + "/SSOLogin";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<Map<String, ?>> requestEntity = new HttpEntity<>(map, headers);

        ResponseEntity<OssUser> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                OssUser.class
        );

        return response.getBody();
    }
}
