package com.jh.sys.bean;


import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 用户角色关联
 *
 * <AUTHOR>
 * @date 2019-12-27 13:33:46
 */
@Schema(description = "用户角色关联")
public class SysUserRole implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "角色标识")
    private String roleId;

    @Schema(description = "用户标识")
    private String userId;

    /**
     * GET 角色标识
     *
     * @return roleId
     */
    public String getRoleId() {
        return roleId;
    }

    /**
     * SET 角色标识
     *
     * @param roleId
     */
    public void setRoleId(String roleId) {
        this.roleId = roleId == null ? null : roleId.trim();
    }

    /**
     * GET 用户标识
     *
     * @return userId
     */
    public String getUserId() {
        return userId;
    }

    /**
     * SET 用户标识
     *
     * @param userId
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }
}