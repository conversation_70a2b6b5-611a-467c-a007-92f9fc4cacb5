package com.jh.sys.bean.vo;

import com.jh.sys.bean.SysTableSet;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;


/**
 * 用户格设置Vo
 *<AUTHOR>
 *@date 2024-01-31
 */
@Schema(description = "SysTableSetVo")
public class SysTableSetVo extends SysTableSet {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;


	private List<SysTableSetListVo> sysTableSetListVo;

	public List<SysTableSetListVo> getSysTableSetListVo() {
		return sysTableSetListVo;
	}

	public void setSysTableSetListVo(List<SysTableSetListVo> sysTableSetListVo) {
		this.sysTableSetListVo = sysTableSetListVo;
	}

	private Integer pageNum=1;
	private Integer pageSize=20;
	public Integer getPageNum() {
		return pageNum;
	}
	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}
	public Integer getPageSize() {
		return pageSize;
	}
	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}
}