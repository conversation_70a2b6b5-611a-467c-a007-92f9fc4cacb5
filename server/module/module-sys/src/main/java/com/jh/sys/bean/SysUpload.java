package com.jh.sys.bean;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import com.jh.common.bean.BaseEntity;
import com.jh.common.xss.StringNoDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Table;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;

import java.util.Date;

/**
 * 我的上传对象 sys_upload

 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Table(name = "sys_upload")
@Schema(description =  "我的上传")
public class SysUpload extends BaseEntity {
    private static final long serialVersionUID = 1L;

	@Column(name = "user_id")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="所属用户")
	private String userId;
	@Column(name = "upload_time")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @Schema(description ="触发上传的时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
	private Date uploadTime;
	@Column(name = "gre_time")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @Schema(description ="生成文件的时间(结束时间)")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
	private Date greTime;
	@Column(name = "upload_file_url")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="上传的文件下载地址")
    @JsonDeserialize(using = StringNoDeserializer.class)
	private String uploadFileUrl;
	@Column(name = "upload_file_name")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="上传文件名称")
    @JsonDeserialize(using = StringNoDeserializer.class)
	private String uploadFileName;
	@Column(name = "return_file_url")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="返回的文件下载地址")
    @JsonDeserialize(using = StringNoDeserializer.class)
	private String returnFileUrl;
	@Column(name = "return_file_name")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="返回文件名称")
	private String returnFileName;
	@Column(name = "upload_type")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="上传类型 批量上传")
	private String uploadType;
	@Column(name = "mod_type")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="模块")
	private String modType;
	@Column(name = "son_mod")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="子模块")
	private String sonMod;
	@Column(name = "status")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="状态0导入中、1导入完成、2导入失败")
	private String status;
	@Column(name = "redme")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="备注")
	private String redme;
	@Column(name = "total_num")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description ="总条数")
	private Integer totalNum;
	@Column(name = "success_num")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description ="成功条数")
	private Integer successNum;
	@Column(name = "error_num")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description ="失败条数")
	private Integer errorNum;
    /**
     * SET 所属用户
     * @param userId
     */
    public void setUserId(String userId){
		this.userId = userId == null ? null :userId;
	}
    /**
     * GET 所属用户
     * @return userId
     */
    public String getUserId(){
        return userId;
    }
    /**
     * SET 触发上传的时间
     * @param uploadTime
     */
    public void setUploadTime(Date uploadTime){
		this.uploadTime = uploadTime;
	}
    /**
     * GET 触发上传的时间
     * @return uploadTime
     */
    public Date getUploadTime(){
        return uploadTime;
    }
    /**
     * SET 生成文件的时间(结束时间)
     * @param greTime
     */
    public void setGreTime(Date greTime){
		this.greTime = greTime;
	}
    /**
     * GET 生成文件的时间(结束时间)
     * @return greTime
     */
    public Date getGreTime(){
        return greTime;
    }
    /**
     * SET 上传的文件下载地址
     * @param uploadFileUrl
     */
    public void setUploadFileUrl(String uploadFileUrl){
		this.uploadFileUrl = uploadFileUrl == null ? null :uploadFileUrl;
	}
    /**
     * GET 上传的文件下载地址
     * @return uploadFileUrl
     */
    public String getUploadFileUrl(){
        return uploadFileUrl;
    }
    /**
     * SET 上传文件名称
     * @param uploadFileName
     */
    public void setUploadFileName(String uploadFileName){
		this.uploadFileName = uploadFileName == null ? null :uploadFileName;
	}
    /**
     * GET 上传文件名称
     * @return uploadFileName
     */
    public String getUploadFileName(){
        return uploadFileName;
    }
    /**
     * SET 返回的文件下载地址
     * @param returnFileUrl
     */
    public void setReturnFileUrl(String returnFileUrl){
		this.returnFileUrl = returnFileUrl == null ? null :returnFileUrl;
	}
    /**
     * GET 返回的文件下载地址
     * @return returnFileUrl
     */
    public String getReturnFileUrl(){
        return returnFileUrl;
    }
    /**
     * SET 返回文件名称
     * @param returnFileName
     */
    public void setReturnFileName(String returnFileName){
		this.returnFileName = returnFileName == null ? null :returnFileName;
	}
    /**
     * GET 返回文件名称
     * @return returnFileName
     */
    public String getReturnFileName(){
        return returnFileName;
    }
    /**
     * SET 上传类型 批量上传
     * @param uploadType
     */
    public void setUploadType(String uploadType){
		this.uploadType = uploadType == null ? null :uploadType;
	}
    /**
     * GET 上传类型 批量上传
     * @return uploadType
     */
    public String getUploadType(){
        return uploadType;
    }
    /**
     * SET 模块
     * @param modType
     */
    public void setModType(String modType){
		this.modType = modType == null ? null :modType;
	}
    /**
     * GET 模块
     * @return modType
     */
    public String getModType(){
        return modType;
    }
    /**
     * SET 子模块
     * @param sonMod
     */
    public void setSonMod(String sonMod){
		this.sonMod = sonMod == null ? null :sonMod;
	}
    /**
     * GET 子模块
     * @return sonMod
     */
    public String getSonMod(){
        return sonMod;
    }
    /**
     * SET 状态0导入中、1导入完成、2导入失败
     * @param status
     */
    public void setStatus(String status){
		this.status = status == null ? null :status;
	}
    /**
     * GET 状态0导入中、1导入完成、2导入失败
     * @return status
     */
    public String getStatus(){
        return status;
    }
    /**
     * SET 备注
     * @param redme
     */
    public void setRedme(String redme){
		this.redme = redme == null ? null :redme;
	}
    /**
     * GET 备注
     * @return redme
     */
    public String getRedme(){
        return redme;
    }

    public Integer getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Integer totalNum) {
        this.totalNum = totalNum;
    }

    public Integer getSuccessNum() {
        return successNum;
    }

    public void setSuccessNum(Integer successNum) {
        this.successNum = successNum;
    }

    public Integer getErrorNum() {
        return errorNum;
    }

    public void setErrorNum(Integer errorNum) {
        this.errorNum = errorNum;
    }

    @Override
	public String toString() {
	    return  ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
	}
}
