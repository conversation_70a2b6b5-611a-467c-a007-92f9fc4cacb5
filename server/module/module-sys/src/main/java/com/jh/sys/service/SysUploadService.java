package com.jh.sys.service;


import com.github.pagehelper.PageInfo;
import com.jh.sys.bean.SysUpload;
import com.jh.sys.bean.vo.SysUploadVo;

import java.util.List;

/**
 * 我的上传Service接口
 * 
 * <AUTHOR>
 * @date 2023-10-26
 */
public interface SysUploadService {
	/**
	 * 保存或更新我的上传
	 *@param sysUpload 我的上传对象
	 *@return String 我的上传ID
	 *<AUTHOR>
	 */
	String saveOrUpdateSysUpload(SysUpload sysUpload);
	
	/**
	 * 删除我的上传
	 *@param ids void 我的上传ID
	 *<AUTHOR>
	 */
	void deleteSysUpload(List<String> ids);

	/**
	 * 查询我的上传详情
	 *@param id
	 *@return SysUpload
	 *<AUTHOR>
	 */
	SysUpload findById(String id);

	/**
	 * 分页查询我的上传
	 *@param sysUploadVo
	 *@return PageInfo<SysUpload>
	 *<AUTHOR>
	 */
	PageInfo<SysUpload> findPageByQuery(SysUploadVo sysUploadVo);
}
