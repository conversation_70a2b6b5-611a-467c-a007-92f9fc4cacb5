package com.jh.sys.bean.vo;

import com.jh.sys.bean.SysDown;
import io.swagger.v3.oas.annotations.media.Schema;


/**
 * 我的导出Vo
 *<AUTHOR>
 *@date 2023-09-07
 */
@Schema( description = "SysDownVo")
public class SysDownVo extends SysDown {

	/**
	 *
	 */
	private static final long serialVersionUID = 1L;

	private Integer pageNum=1;
	private Integer pageSize=20;

	/**
	 * 触发开始时间
	 */
	private String downTimeStart;

	/**
	 * 触发结束时间
	 */
	private String downTimeEnd;

	public String getDownTimeStart() {
		return downTimeStart;
	}

	public void setDownTimeStart(String downTimeStart) {
		this.downTimeStart = downTimeStart;
	}

	public String getDownTimeEnd() {
		return downTimeEnd;
	}

	public void setDownTimeEnd(String downTimeEnd) {
		this.downTimeEnd = downTimeEnd;
	}

	public Integer getPageNum() {
		return pageNum;
	}
	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}
	public Integer getPageSize() {
		return pageSize;
	}
	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}
}
