package com.jh.sys.bean;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jh.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Table;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;

import java.util.Date;

/**
 * 我的导出对象 sys_down
 
 * 
 * <AUTHOR>
 * @date 2023-09-07
 */
@Table(name = "sys_down")
@Schema(description = "我的导出")
public class SysDown extends BaseEntity {
    private static final long serialVersionUID = 1L;

	@Column(name = "user_id")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description="所属用户")
	private String userId;
	@Column(name = "down_time")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @Schema(description="触发下载的时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
	private Date downTime;
	@Column(name = "gre_time")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @Schema(description="生成文件的时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
	private Date greTime;
	@Column(name = "file_url")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description="文件下载地址")
	private String fileUrl;
	@Column(name = "file_name")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description="文件名称")
	private String fileName;
	@Column(name = "down_type")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description="下载类型 普通下载、批量下载")
	private String downType;
	@Column(name = "mod_type")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description="模块")
	private String modType;
	@Column(name = "son_mod")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description="子模块")
	private String sonMod;
	@Column(name = "status")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description="状态0下载中、1下载完成、2生成文件失败")
	private Integer status;
	@Column(name = "redme")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description="备注")
	private String redme;
	@Column(name = "down_num")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description="下载次数")
	private Integer downNum;
    /**
     * SET 所属用户
     * @param userId
     */
    public void setUserId(String userId){
		this.userId = userId == null ? null :userId;
	}
    /**
     * GET 所属用户
     * @return userId
     */
    public String getUserId(){
        return userId;
    }
    /**
     * SET 触发下载的时间
     * @param downTime
     */
    public void setDownTime(Date downTime){
		this.downTime = downTime;
	}
    /**
     * GET 触发下载的时间
     * @return downTime
     */
    public Date getDownTime(){
        return downTime;
    }
    /**
     * SET 生成文件的时间
     * @param greTime
     */
    public void setGreTime(Date greTime){
		this.greTime = greTime;
	}
    /**
     * GET 生成文件的时间
     * @return greTime
     */
    public Date getGreTime(){
        return greTime;
    }
    /**
     * SET 文件下载地址
     * @param fileUrl
     */
    public void setFileUrl(String fileUrl){
		this.fileUrl = fileUrl == null ? null :fileUrl;
	}
    /**
     * GET 文件下载地址
     * @return fileUrl
     */
    public String getFileUrl(){
        return fileUrl;
    }
    /**
     * SET 文件名称
     * @param fileName
     */
    public void setFileName(String fileName){
		this.fileName = fileName == null ? null :fileName;
	}
    /**
     * GET 文件名称
     * @return fileName
     */
    public String getFileName(){
        return fileName;
    }
    /**
     * SET 下载类型 普通下载、批量下载
     * @param downType
     */
    public void setDownType(String downType){
		this.downType = downType == null ? null :downType;
	}
    /**
     * GET 下载类型 普通下载、批量下载
     * @return downType
     */
    public String getDownType(){
        return downType;
    }
    /**
     * SET 模块
     * @param modType
     */
    public void setModType(String modType){
		this.modType = modType == null ? null :modType;
	}
    /**
     * GET 模块
     * @return modType
     */
    public String getModType(){
        return modType;
    }
    /**
     * SET 子模块
     * @param sonMod
     */
    public void setSonMod(String sonMod){
		this.sonMod = sonMod == null ? null :sonMod;
	}
    /**
     * GET 子模块
     * @return sonMod
     */
    public String getSonMod(){
        return sonMod;
    }
    /**
     * SET 状态0下载中、1下载完成、2生成文件失败
     * @param status
     */
    public void setStatus(Integer status){
		this.status = status;
	}
    /**
     * GET 状态0下载中、1下载完成、2生成文件失败
     * @return status
     */
    public Integer getStatus(){
        return status;
    }
    /**
     * SET 备注
     * @param redme
     */
    public void setRedme(String redme){
		this.redme = redme == null ? null :redme;
	}
    /**
     * GET 备注
     * @return redme
     */
    public String getRedme(){
        return redme;
    }
    /**
     * SET 下载次数
     * @param downNum
     */
    public void setDownNum(Integer downNum){
		this.downNum =downNum;
	}
    /**
     * GET 下载次数
     * @return downNum
     */
    public Integer getDownNum(){
        return downNum;
    }
    @Override
	public String toString() {
	    return  ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
	}
}
