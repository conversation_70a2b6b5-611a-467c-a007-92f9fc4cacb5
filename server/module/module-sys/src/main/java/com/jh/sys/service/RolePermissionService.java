package com.jh.sys.service;

import com.jh.common.service.BaseService;
import com.jh.sys.bean.SysRolePermission;

import java.util.List;

/**
 * 角色service
 *
 * <AUTHOR>
 * @date 2018/03/1
 */
public interface RolePermissionService extends BaseService<SysRolePermission> {

    /**
     * 给角色授权
     *
     * @param roleId
     * @param permissions
     */
    void savePermissions(String roleId, String menuId, List<String> permissions, String type);

    /**
     * 查询角色权限
     *
     * @param roleId
     * @return
     */
    List<SysRolePermission> listPermissions(String roleId);
}
