package com.jh.sys.bean.vo;

import com.alibaba.excel.annotation.ExcelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 导出操作日志
 * @date 2020/07/15
 */
public class SysLogOperExportVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty("操作人")
    private String createUserNickname;

    @ExcelProperty("操作一级模块")
    private String operOneModule;

    @ExcelProperty("操作二级模块")
    private String operTwoModule;

    @ExcelProperty("操作详细说明")
    private String operDetails;

    @ExcelProperty("IP地址")
    private String ip;

    @ExcelProperty("操作时间")
    private Date createTime;

    public String getCreateUserNickname() {
        return createUserNickname;
    }

    public void setCreateUserNickname(String createUserNickname) {
        this.createUserNickname = createUserNickname;
    }

    public String getOperOneModule() {
        return operOneModule;
    }

    public void setOperOneModule(String operOneModule) {
        this.operOneModule = operOneModule;
    }

    public String getOperTwoModule() {
        return operTwoModule;
    }

    public void setOperTwoModule(String operTwoModule) {
        this.operTwoModule = operTwoModule;
    }

    public String getOperDetails() {
        return operDetails;
    }

    public void setOperDetails(String operDetails) {
        this.operDetails = operDetails;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Date getCreateTime() {
        if (createTime != null) {
            return new Date(createTime.getTime());
        }
        return null;
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        }
    }

}
