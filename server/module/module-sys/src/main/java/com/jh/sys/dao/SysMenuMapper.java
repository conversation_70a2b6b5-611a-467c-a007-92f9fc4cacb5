package com.jh.sys.dao;

import com.jh.common.mybatis.BaseInfoMapper;
import com.jh.sys.bean.SysMenu;
import com.jh.sys.bean.vo.SysMenuVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SysMenuMapper extends BaseInfoMapper<SysMenu> {

    /**
     * 根据用户权限关系查找用户可访问菜单
     *
     * @param userId
     * @return
     */
    List<SysMenu> selectAuthorityMenuByUserId(@Param("userId") String userId);



    /**
     * 根据用户和角色编码权限关系查找用户可访问菜单
     *
     * @param roleCode
     * @return
     */
    List<SysMenu> selectAuthorityMenuByRoleUserId(@Param("roleCode") String roleCode);

    /**
     * 根据角色id查询菜单
     *
     * @param roleId
     * @return
     */
    List<SysMenu> findRoleId(@Param("roleId") String roleId);

}