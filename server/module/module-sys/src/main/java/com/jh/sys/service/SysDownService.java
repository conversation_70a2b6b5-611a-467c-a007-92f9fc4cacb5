package com.jh.sys.service;



import com.github.pagehelper.PageInfo;
import com.jh.sys.bean.SysDown;
import com.jh.sys.bean.vo.SysDownVo;

import java.util.List;

/**
 * 我的导出Service接口
 * 
 * <AUTHOR>
 * @date 2023-09-07
 */
public interface SysDownService {
	/**
	 * 保存或更新我的导出
	 *@param sysDown 我的导出对象
	 *@return String 我的导出ID
	 *<AUTHOR>
	 */
	String saveOrUpdateSysDown(SysDown sysDown);
	
	/**
	 * 删除我的导出
	 *@param ids void 我的导出ID
	 *<AUTHOR>
	 */
	void deleteSysDown(List<String> ids);

	/**
	 * 查询我的导出详情
	 *@param id
	 *@return SysDown
	 *<AUTHOR>
	 */
	SysDown findById(String id);

	/**
	 * 分页查询我的导出
	 *@param sysDownVo
	 *@return PageInfo<SysDown>
	 *<AUTHOR>
	 */
	PageInfo<SysDown> findPageByQuery(SysDownVo sysDownVo);
}
