package com.jh.sys.enums;

/**
 * 角色状态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/10 14:43
 */
public enum RoleStatusEnum {
    ENABLED(1, "启用"), DISABLED(0, "禁用");

    private final Integer code;
    private final String value;

    RoleStatusEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 根据code获取枚举对象
     *
     * @param code code
     * @return 枚举对象
     */
    public static RoleStatusEnum getRoleStatusEnum(Integer code) {
        for (RoleStatusEnum e : RoleStatusEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return DISABLED;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
