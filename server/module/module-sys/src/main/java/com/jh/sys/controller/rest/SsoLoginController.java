package com.jh.sys.controller.rest;

import com.alibaba.fastjson2.JSONObject;
import com.jh.common.bean.LoginUser;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.controller.BaseController;
import com.jh.common.exception.ServiceException;
import com.jh.common.redis.RedisUtil;
import com.jh.common.util.http.ResponseUtil;
import com.jh.sys.bean.rest.OssUser;
import com.jh.sys.bean.rest.SysUserYthoauth;
import com.jh.sys.bean.rest.ZwwSsoBody;
import com.jh.sys.bean.vo.SysUserVo;

import com.jh.sys.config.SecurityHandlerConfig;
import com.jh.sys.config.properties.SuperviseProperties;
import com.jh.sys.config.properties.YthoauthProperties;
import com.jh.sys.feign.YthoauthApi;
import com.jh.sys.service.SysUserService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;

/**
 * @Description 单点登录
 * @Date 2023/9/13 10:00
 * <AUTHOR>
 **/
@RestController
@RequestMapping("/rest/sso")
//@Api(value = "单点登录", description = "单点登录")
public class SsoLoginController extends BaseController {

    public static final Logger logger = LoggerFactory.getLogger(SsoLoginController.class);
//
    @Autowired
    private SecurityHandlerConfig securityHandlerConfig;


    private static final String EST_REDIS_PREFIX = "EST_FINACE_REPORT_REDIS_PREFIX:";

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    YthoauthProperties ythoauthProperties;

    @Autowired
    YthoauthApi ythoauthApi;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private SuperviseProperties superviseProperties;

//    @Autowired
//    UserDetailsServiceImpl userDetailsServiceImpl;

    /**
     * @return void * @throws
     * @description: 一体化登录
     */
    @GetMapping("/ythLogin")
    public void ythLogin(@RequestParam("est") String est) {
        if (StringUtils.isEmpty(est)) {
            throw new ServiceException("est参数不能为空");
        }
        // 调用老胡的接口消费est并获得用户信息
        OssUser ossUser = null;
        try {
            HashMap<String, String> map = new HashMap<>();
            map.put("appName", ythoauthProperties.getAppName());
            map.put("est", est);
            logger.error("map: {} ", JSONObject.toJSONString(map));
            ossUser = ythoauthApi.ythOssPostLoginUser(map);
            logger.error("ossUser: {} ", JSONObject.toJSONString(ossUser));
            logger.info("登录反馈：结果-{},描述-{}，中台ID-{}", ossUser.getSuccess(), ossUser.getData(), ossUser.getUserId());
        } catch (Exception e) {
            // throw new ServiceException("一体化服务请求失败");
            e.printStackTrace();
            errRedirect("一体化登录失败");
            return;
        }
        try {
            if (ossUser != null) {
                redisUtil.set(EST_REDIS_PREFIX + est, ossUser.getUserId(), (60 * 60 * 24));
                // 按手机号查询库中的用户
                SysUserYthoauth sysUserYthoauth = new SysUserYthoauth();
                sysUserYthoauth.setYthuserId(ossUser.getUserId());
                sysUserYthoauth.setPhone(ossUser.getMobile());
                LoginUser loginUser = sysUserService.ythLogin(sysUserYthoauth);
                // 没有匹配的用户就创建用户
                if (loginUser == null) {
                    SysUserVo sysUserVo = new SysUserVo();
                    sysUserVo.setPhone(ossUser.getMobile());
                    sysUserVo.setUsername(ossUser.getMobile() + "_" + superviseProperties.getUserTypeCode());
                    sysUserVo.setNickname(ossUser.getMobile());
                    sysUserVo.setYthUserId(ossUser.getUserId());
                    sysUserService.createSuperviseUsers(sysUserVo);
                    loginUser = sysUserService.ythLogin(sysUserYthoauth);
                }
                securityHandlerConfig.loginResponse(loginUser, response);
            } else {
                throw new ServiceException("一体化服务请求失败");
            }
        } catch (Exception e) {
            // 登录失败
            logger.error("一体化登录失败:{}", e);
            errRedirect("一体化登录失败");
            return;
            // throw new ServiceException("一体化登录失败");
        }
        return;
    }

    protected void errRedirect(String msg) {
        // 把页面交给一体化登录页面
        HashMap<String, Object> map = new HashMap<>();
        map.put("isRedirect", true);
        RestApiResponse rest = RestApiResponse.error(msg);
        rest.setData(map);
        ResponseUtil.responseJson(response, HttpStatus.OK.value(), rest);
        return;
    }


    /**
     * @return void * @throws
     * @description: 政务网登录 - 企业机构
     */
    @GetMapping("/zwwOrgLoginApi")
    public void zwwOrgLoginApi(@RequestParam("zt") String zt) {
        if (StringUtils.isEmpty(zt)) {
            throw new ServiceException("zt参数不能为空");
        }
        ZwwSsoBody zwwSsoBody = new ZwwSsoBody();
        zwwSsoBody.setToken(zt);
        LoginUser loginUser = sysUserService.zwwOrgLoginApi(zwwSsoBody);
        securityHandlerConfig.loginResponse(loginUser, response);
    }


}
