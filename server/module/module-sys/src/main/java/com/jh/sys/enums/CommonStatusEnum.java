package com.jh.sys.enums;

/**
 * 公共数据状态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/13 14:47
 */
public enum CommonStatusEnum {
    ENABLED(1, "启用"),
    DISABLED(0, "禁用");

    private final Integer code;
    private final String value;

    CommonStatusEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 根据状态code返回状态枚举
     *
     * @param code 状态code
     * @return 枚举
     */
    public static CommonStatusEnum getCommonStatusEnum(Integer code) {
        for (CommonStatusEnum e : CommonStatusEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return CommonStatusEnum.DISABLED;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
