package com.jh.sys.enums;

import java.util.*;

/**
 * 机构类型枚举
 * 用于定义不同机构类型对应的ID和名称
 */
public enum OrgTypeEnum {
    
    MEASUREMENT(1, "计量"),
    SPECIAL_INSPECTION(2, "特检"),
    QUALITY_INSPECTION(3, "质检"),
    ASSOCIATION(4, "学(协)会"),
    STANDARDIZATION(5, "标准化"),
    INTELLECTUAL_PROPERTY(6, "知识产权"),
    MARKET_NEWSPAPER(7, "市场导报社"),
    ADVERTISING_MONITORING(8, "广告监测"),
    FOOD(9, "食品"),
    MEDICINE(10, "药品");
    
    private final Integer id;
    private final String name;
    
    OrgTypeEnum(Integer id, String name) {
        this.id = id;
        this.name = name;
    }
    
    public Integer getId() {
        return id;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 根据机构类型ID列表获取对应的菜单名称
     * @param orgTypeIds 机构类型ID列表
     * @return 对应的菜单名称集合
     */
    public static Set<String> getMenuNamesByIds(List<Integer> orgTypeIds) {
        Set<String> menuNames = new HashSet<>();
        if (orgTypeIds == null || orgTypeIds.isEmpty()) {
            return menuNames;
        }
        
        for (Integer orgTypeId : orgTypeIds) {
            OrgTypeEnum orgType = getById(orgTypeId);
            if (orgType != null) {
                menuNames.add(orgType.getName());
            }
        }
        return menuNames;
    }
    
    /**
     * 根据机构类型字符串列表获取对应的菜单名称
     * @param orgTypeStrings 机构类型字符串列表
     * @return 对应的菜单名称集合
     */
    public static Set<String> getMenuNamesByStrings(List<String> orgTypeStrings) {
        Set<String> menuNames = new HashSet<>();
        if (orgTypeStrings == null || orgTypeStrings.isEmpty()) {
            return menuNames;
        }
        
        for (String orgTypeString : orgTypeStrings) {
            try {
                Integer orgTypeId = Integer.parseInt(orgTypeString.trim());
                OrgTypeEnum orgType = getById(orgTypeId);
                if (orgType != null) {
                    menuNames.add(orgType.getName());
                }
            } catch (NumberFormatException e) {
                // 忽略无法解析的字符串
            }
        }
        return menuNames;
    }
    
    /**
     * 根据ID获取枚举
     * @param id 机构类型ID
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static OrgTypeEnum getById(Integer id) {
        if (id == null) {
            return null;
        }
        for (OrgTypeEnum orgType : values()) {
            if (orgType.getId().equals(id)) {
                return orgType;
            }
        }
        return null;
    }
    
    /**
     * 根据名称获取枚举
     * @param name 机构类型名称
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static OrgTypeEnum getByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }
        for (OrgTypeEnum orgType : values()) {
            if (orgType.getName().equals(name)) {
                return orgType;
            }
        }
        return null;
    }
    
    /**
     * 检查指定名称是否存在于枚举中
     * @param name 机构类型名称
     * @return 如果存在返回true，否则返回false
     */
    public static boolean containsName(String name) {
        return getByName(name) != null;
    }
    
    /**
     * 检查指定ID是否存在于枚举中
     * @param id 机构类型ID
     * @return 如果存在返回true，否则返回false
     */
    public static boolean containsId(Integer id) {
        return getById(id) != null;
    }
}