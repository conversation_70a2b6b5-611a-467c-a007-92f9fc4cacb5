<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.sys.dao.SysUserRoleMapper">
    <!-- 用户角色关联 -->
    <resultMap id="BaseResultMap" type="com.jh.sys.bean.SysUserRole">
        <!-- 角色标识 -->
        <result column="ROLE_ID" property="roleId" jdbcType="VARCHAR"/>
        <!-- 用户标识 -->
        <result column="USER_ID" property="userId" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="SysUserInfoVO" type="com.jh.sys.bean.vo.SysUserInfoVO" extends="BaseResultMap">
    </resultMap>
    <insert id="insertSysUserRole">
        insert into sys_user_role(ROLE_ID,USER_ID) values(#{roleId},#{userId})
    </insert>
    <delete id="deleteByUserId">

        delete from sys_user_role where USER_ID = #{userId}
    </delete>
    <delete id="deleteByRoleId">
        delete from sys_user_role where ROLE_ID = #{roleId}
    </delete>
    <delete id="deleteByUserIdRoleId">
        delete from sys_user_role where USER_ID = #{userId} and ROLE_ID = #{roleId}
    </delete>

    <select id="selectUserByRoleIds" resultMap="BaseResultMap">
        select * from sys_user_role where role_id in
        <foreach collection="roleIds" item="roleId" index="index" open="(" close=")" separator=",">
            #{roleId}
        </foreach>
    </select>

    <select id="findByRoleIdAndName" resultMap="SysUserInfoVO">
        select
        u.*
        from sys_user_role ur
        left join sys_Users u
        on u.ID =
        ur.User_Id
        where ur.Role_Id = #{id}
        and (
        <foreach collection="userNames" item="userName" index="index"
                 open="" close="" separator=" or ">
            <bind name="userNameName" value="'%' + userName + '%'"/>
            u.User_Name like #{userNameName}
        </foreach>
        )
    </select>
    <select id="selectByUserId" resultType="com.jh.sys.bean.SysUserRole">
        select * from sys_user_role where USER_ID = #{userId}
    </select>
    <select id="selectByUserIdRoleId" resultType="com.jh.sys.bean.SysUserRole">
        select * from sys_user_role where USER_ID = #{userId} and ROLE_ID = #{roleId}
    </select>
    <select id="selectByRoleId" resultType="com.jh.sys.bean.SysUserRole">
        select * from sys_user_role where ROLE_ID = #{roleId}
    </select>
</mapper>