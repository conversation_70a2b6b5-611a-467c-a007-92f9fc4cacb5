package com.jh.sys.bean.vo;

import com.jh.sys.bean.SysUpload;
import io.swagger.v3.oas.annotations.media.Schema;


/**
 * 我的上传Vo
 *<AUTHOR>
 *@date 2023-10-26
 */
@Schema(description =  "SysUploadVo")
public class SysUploadVo extends SysUpload {

	/**
	 *
	 */
	private static final long serialVersionUID = 1L;

	private Integer pageNum=1;
	private Integer pageSize=20;

	/**
	 * 触发开始时间
	 */
	private String uploadTimeStart;

	/**
	 * 触发结束时间
	 */
	private String uploadTimeEnd;

	public String getUploadTimeStart() {
		return uploadTimeStart;
	}

	public void setUploadTimeStart(String uploadTimeStart) {
		this.uploadTimeStart = uploadTimeStart;
	}

	public String getUploadTimeEnd() {
		return uploadTimeEnd;
	}

	public void setUploadTimeEnd(String uploadTimeEnd) {
		this.uploadTimeEnd = uploadTimeEnd;
	}

	public Integer getPageNum() {
		return pageNum;
	}
	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}
	public Integer getPageSize() {
		return pageSize;
	}
	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}
}
