package com.jh.sys.controller.rest;

import com.jh.common.bean.SysFileInfo;
import com.jh.common.controller.BaseController;
import com.jh.sys.service.FileService;
import com.jh.sys.service.impl.FileServiceFactory;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 可匿名访问的文件操作接口
 */
@RestController
@RequestMapping("/sys/file/rest")
@Tag(name = "文件上传rest服务")
public class RestFileController extends BaseController {

    @Value("${file.useFileType}")
    private String fileSource;

    @Autowired
    private FileServiceFactory fileServiceFactory;

    /**
     * 文件上传
     *
     * @param file 文件
     * @return 文件信息
     */
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public SysFileInfo upload(MultipartFile file) throws Exception {
        FileService fileService = fileServiceFactory.getFileService(fileSource);
        SysFileInfo sysFileInfo = new SysFileInfo();
        return fileService.upload(file, sysFileInfo);
    }
}
