package com.jh.sys.bean.vo;

import com.jh.common.bean.SysLogRecord;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Arrays;

/**
 * 日志记录表Vo
 *
 * <AUTHOR>
 * @date 2020-07-13 14:04:22
 */
@Schema(description = "日志记录表Vo")
public class SysLogRecordVo extends SysLogRecord {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 10;

    /**
     * 操作时间
     */
    private String[] operTime;
    /**
     * 排序字段
     */
    private String sortField;
    /**
     * 排序方式
     */
    private String sortType;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * GET 操作时间
     *
     * @return
     */
    public String[] getOperTime() {
        if (operTime != null) {
            return Arrays.copyOf(operTime, operTime.length);
        }
        return null;
    }

    /**
     * SET 操作时间
     *
     * @param operTime
     */
    public void setOperTime(String[] operTime) {
        if (operTime != null) {
            this.operTime = Arrays.copyOf(operTime, operTime.length);
        }
    }

    public String getSortField() {
        return sortField;
    }

    public void setSortField(String sortField) {
        this.sortField = sortField;
    }

    public String getSortType() {
        return sortType;
    }

    public void setSortType(String sortType) {
        this.sortType = sortType;
    }

}
