package com.jh.sys.dao;

import com.jh.common.bean.SysUser;
import com.jh.common.mybatis.BaseInfoMapper;
import com.jh.sys.bean.vo.OrgListVo;
import com.jh.sys.bean.vo.SysUserVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 系统用户表
 *
 * <AUTHOR>
 * @date 2020-07-05 16:25:12
 */
public interface SysUserMapper extends BaseInfoMapper<SysUser> {

    /**
     * 根据查询条件分页查询符合条件得数据
     *
     * @param sysUserVo 查询数据
     * @return 用户列表
     */
    List<SysUserVo> findPageByQuery(@Param("vo") SysUserVo sysUserVo);

    /**
     * 根据id更新yn字段
     *
     * @param ids
     */
    void updateYnByIds(@Param("ids") List<String> ids);

    /**
     * 根据morgid取用户信息
     *
     * @param morgId
     */
    SysUserVo getUserByMorgId(@Param("morgId") String morgId);

    /**
     * 查询一个市级用户
     */
    SysUser findOneCityUser();

    /**
     * 查询一个省级用户
     */
    SysUser findOneProvinceUser();

    /**
     * 根据areacode查询用户【监管单位用户】
     */
    SysUser findMorgByAreaCode(@Param("areaCode") String areaCode);

    /**
     * 根据areacode查询用户【科技管理部门用户】
     */
    SysUser findScienceByAreaCode(@Param("areaCode") String areaCode);

    /**
     * 根据Username查询用户
     */
    SysUser findByUsername(@Param("userName") String userName);

    /**
     * 根据scienceOrgId获取列表
     */
    List<SysUser> listByScienceOrgId(@Param("scienceOrgId") String scienceOrgId);

    /**
     * 根据角色CODE查询所有用户
     */
    List<SysUser> findPageByRoleCode(@Param("roleCode") String roleCode);

    /**
     * 查询专家用户
     *
     * @param sysUserVo
     * @return
     */
    List<SysUserVo> findExpertPageByQuery(@Param("vo") SysUserVo sysUserVo);

    /**
     * 根据用户名获取用户信息
     *
     * @param username 用户名
     * @return 用户信息
     */
    SysUser getUserByUsername(String username);

    /**
     * 根据用户名获取用户数量
     *
     * @param username 用户名
     * @return 用户数量
     */
    int countUserByUsername(String username);

    /**
     * 判断手机号是否存在
     *
     * @param phone 手机号
     * @return 结果
     */
    int countUserByPhone(String phone);
    
    /**
     * 更新用户机构信息
     *
     * @param sysUserVo 用户信息
     * @return 更新结果
     */
    int updateOrgInfo(@Param("vo") SysUserVo sysUserVo);

    SysUserVo selectBycurrentUserId(String currentUserId);

    /**
     * 一体化平台登录用户ID
     *
     * @param ythUserId
     * @return
     */
    @Select("SELECT * FROM sys_user WHERE YN = 1 AND (YTH_USER_ID = #{ythUserId} OR PHONE = #{phone}) AND TYPENAME = '监管人员' ORDER BY UPDATE_TIME DESC LIMIT 1")
    SysUserVo findByYthUserid(@Param("ythUserId") String ythUserId, @Param("phone") String phone);

    SysUser getUserByUsernameAndUserType(@Param("phone") String phone, @Param("userTypeCode")String userTypeCode);

    /**
     * 根据行政区划代码查询机构列表
     *
     * @param areaCode 行政区划代码（市级，如3303、3301等）
     * @return 机构账号列表
     */
    List<OrgListVo> findOrgListByAreaCode(@Param("areaCode") String areaCode);
}