package com.jh.sys.config.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = YthoauthProperties.PREFIX)
public class YthoauthProperties {

    public final static String PREFIX = "ythoauth";

    private String appName;

    private String errPage;

    private String bindingPage;

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getErrPage() {
        return errPage;
    }

    public void setErrPage(String errPage) {
        this.errPage = errPage;
    }

    public String getBindingPage() {
        return bindingPage;
    }

    public void setBindingPage(String bindingPage) {
        this.bindingPage = bindingPage;
    }
}
