<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.sys.dao.SysDownMapper" >
    <resultMap id="CommonBaseResultMap" type="com.jh.common.bean.BaseEntity">
    <result column="ID" property="id" jdbcType="VARCHAR" />
    <result column="YN" property="yn" jdbcType="INTEGER" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR" />
    <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
    <result column="CREATE_USER_NICKNAME" property="createUserNickname" jdbcType="VARCHAR" />
    <result column="UPDATE_USER_NICKNAME" property="updateUserNickname" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 我的导出 -->
  <resultMap id="BaseResultMap" type="com.jh.sys.bean.SysDown" extends="CommonBaseResultMap">
    <!--
      WARNING - @mbg.generated
    -->
    <!-- 所属用户 -->
    <result column="user_id" property="userId" jdbcType="VARCHAR" />
    <!-- 触发下载的时间 -->
    <result column="down_time" property="downTime" jdbcType="TIMESTAMP" />
    <!-- 生成文件的时间 -->
    <result column="gre_time" property="greTime" jdbcType="TIMESTAMP" />
    <!-- 文件下载地址 -->
    <result column="file_url" property="fileUrl" jdbcType="VARCHAR" />
    <!-- 文件名称 -->
    <result column="file_name" property="fileName" jdbcType="VARCHAR" />
    <!-- 下载类型 普通下载、批量下载 -->
    <result column="down_type" property="downType" jdbcType="VARCHAR" />
    <!-- 模块 -->
    <result column="mod_type" property="modType" jdbcType="VARCHAR" />
    <!-- 子模块 -->
    <result column="son_mod" property="sonMod" jdbcType="VARCHAR" />
    <!-- 状态0下载中、1下载完成、2生成文件失败 -->
    <result column="status" property="status" jdbcType="VARCHAR" />
    <!-- 备注 -->
    <result column="redme" property="redme" jdbcType="VARCHAR" />
    <!-- 下载次数 -->
    <result column="down_num" property="downNum" jdbcType="VARCHAR" />
  </resultMap>
</mapper>