package com.jh.sys.bean.vo;

import com.jh.common.bean.SysLogOper;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * 用户操作日志表Vo
 *
 * <AUTHOR>
 * @date 2020-07-07 17:51:45
 */

/**
 * <AUTHOR>
 * @date 2020/07/09
 */
@Schema(description = "用户操作日志表Vo")
public class SysLogOperVo extends SysLogOper {

    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 10;

    /**
     * 操作日期
     */
    private List<String> operTime;

    /**
     * 排序字段
     */
    private String sortField;
    /**
     * 排序类型
     */
    private String sortType;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<String> getOperTime() {
        return operTime;
    }

    public void setOperTime(List<String> operTime) {
        this.operTime = operTime;
    }

    public String getSortField() {
        return sortField;
    }

    public void setSortField(String sortField) {
        this.sortField = sortField;
    }

    public String getSortType() {
        return sortType;
    }

    public void setSortType(String sortType) {
        this.sortType = sortType;
    }

}
