package com.jh.sys.controller;

import com.github.pagehelper.PageInfo;
import com.jh.common.annotation.OperLogAnnotation;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.bean.SysUser;
import com.jh.common.controller.BaseController;
import com.jh.common.util.AppUserUtil;
import com.jh.common.util.poi.EasyExcelUtils;
import com.jh.sys.bean.vo.OrgListVo;
import com.jh.sys.bean.vo.SysUserVo;
import com.jh.sys.enums.UserTypeEnum;
import com.jh.sys.service.SysUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户 controller
 *
 * <AUTHOR>
 * @date 2020-07-05 16:25:12
 */
@RestController
@RequestMapping("/sys/user")
@Tag(name = "用户接口")
public class SysUserController extends BaseController {

    private static final String PER_PREFIX = "btn:sys:user:";

    @Autowired
    private SysUserService sysUserService;

    /**
     * 去绑定 google 动态码
     *
     * @param sysUser 用户信息
     * @return 用户ID
     */
    @RepeatSubAnnotation
    @PostMapping("/bindSer")
    @Operation(summary="去绑定 google 动态码")
    @SystemLogAnnotation(type = "去绑定 google 动态码", value = "去绑定 google 动态码")
    public RestApiResponse<?> bindSer(@RequestBody SysUserVo sysUser, HttpServletRequest request) {
        String ip=getCurrentIp(request);
        return RestApiResponse.ok(sysUserService.bindSer(sysUser,ip));
    }

    /**
     * 确认绑定 google 动态码
     *
     * @param sysUser 用户信息
     * @return 用户ID
     */
    @RepeatSubAnnotation
    @PostMapping("/qrbindSer")
     @Operation(summary="确认绑定 google 动态码")
    @SystemLogAnnotation(type = "确认绑定 google 动态码", value = "确认绑定 google 动态码")
    public RestApiResponse<?> qrbindSer(@RequestBody SysUserVo sysUser, HttpServletRequest request) {
        String ip=getCurrentIp(request);
        sysUserService.qrbindSer(sysUser,ip);
        return RestApiResponse.ok();
    }

//
//    /**
//     * 新增用户
//     *
//     * @param sysUser 用户信息
//     * @return 用户ID
//     */
//    @RepeatSubAnnotation
//    @PostMapping("/uppwd")
//      @Operation(summary="修改密码")
//    @SystemLogAnnotation(type = "修改密码", value = "修改密码")
//    public RestApiResponse<?> uppwd(@RequestBody SysUserVo sysUser, HttpServletRequest request) {
//        String ip=getCurrentIp(request);
//        sysUserService.uppwd(sysUser,ip);
//        return RestApiResponse.ok();
//    }

    /**
     * 获取当前登录用户信息
     *
     * @return 用户
     */
    @Operation(summary= "获取当前用户信息")
    @GetMapping("/getCurrentUser")
    public RestApiResponse<?> getCurrentUser() {
        return RestApiResponse.ok(AppUserUtil.getLoginAppUser());
    }

    /**
     * 新增用户
     *
     * @param sysUser 用户信息
     * @return 用户ID
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
      @Operation(summary="新增用户")
    @SystemLogAnnotation(type = "用户管理", value = "新增用户")
    @OperLogAnnotation(value = "'新增用户, 用户名:'+#sysUser.username", module = "系统管理", submodule = "用户管理")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveSysUser(@RequestBody SysUserVo sysUser) {
        // 非管理员禁止添加管理员
        if (!isAdmin()) {
            sysUser.setType(UserTypeEnum.ORDINARY.toString());
        }
        String id = sysUserService.saveOrUpdate(sysUser);
        return RestApiResponse.ok(id);
    }

    /**
     * 用户管理
     *
     * @param sysUser 用户信息
     * @return 用户ID
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
      @Operation(summary="修改用户")
    @SystemLogAnnotation(type = "用户管理", value = "修改用户")
    @OperLogAnnotation(value = "'修改用户, 用户名:'+#sysUser.username", module = "系统管理", submodule = "用户管理")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateSysUser(@RequestBody SysUserVo sysUser) {
        // 非管理员禁止添加管理员角色
        if (!isAdmin()) {
            sysUser.setType(UserTypeEnum.ORDINARY.toString());
        }
        String id = sysUserService.saveOrUpdate(sysUser);
        return RestApiResponse.ok(id);
    }

    /**
     * 删除用户
     *
     * @param userVo 用户IDs
     * @return 结果
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
      @Operation(summary="删除用户")
    @SystemLogAnnotation(type = "用户管理", value = "删除用户")
    @OperLogAnnotation(value = "'删除用户, 用户名Ids:'+#userVo.userIds", module = "系统管理", submodule = "用户管理")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteSysUser(@RequestBody SysUserVo userVo) {
        sysUserService.deleteSysUser(userVo.getUserIds());
        return RestApiResponse.ok();
    }

    /**
     * 获取用户详情
     *
     * @param id 用户ID
     * @return 用户信息
     */
    @GetMapping("/findById")
      @Operation(summary="获取用户详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        SysUser sysUser = sysUserService.findById(id);
        sysUser.setPassword(null);
        return RestApiResponse.ok(sysUser);
    }

    /**
     * 分页查询用户
     *
     * @param sysUserVo 查询列表
     * @return 结果
     */
    @PostMapping("/findPageByQuery")
      @Operation(summary="分页查询用户")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody SysUserVo sysUserVo) {
        PageInfo<SysUserVo> sysUser = sysUserService.findPageByQuery(sysUserVo);
        return RestApiResponse.ok(sysUser);
    }

//    /**
//     * 修改密码
//     *
//     * @param sysUserVo 修改密码
//     * @return RestApiResponse<?>
//     */
//    @PostMapping("/updatePwd")
//      @Operation(summary="修改密码")
//    @SystemLogAnnotation(type = "用户管理", value = "修改密码")
//    @OperLogAnnotation(value = "'修改密码, 用户名Id:'+#sysUserVo.id", module = "系统管理", submodule = "用户管理")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "updatePwd')")
//    public RestApiResponse<?> updatePassword(@RequestBody SysUserVo sysUserVo) {
//        sysUserVo.setId(getCurrentUserId());
//        sysUserService.updatePassword(sysUserVo);
//        return RestApiResponse.ok();
//    }
//
//    /**
//     * 修改用户密码
//     *
//     * @param sysUserVo 查询条件
//     * @return 结果
//     */
//    @PostMapping("/resetPassword")
//      @Operation(summary="通过主键修改对应用户得密码")
//    @SystemLogAnnotation(type = "用户管理", value = "修改密码")
//    @OperLogAnnotation(value = "'修改密码, 用户名Id:'+#sysUserVo.id", module = "系统管理", submodule = "用户管理")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "updatePrimaryPassword')")
//    public RestApiResponse<?> resetPassword(@RequestBody SysUserVo sysUserVo) {
//        sysUserService.resetPassword(sysUserVo);
//        return RestApiResponse.ok();
//    }

    /**
     * 导出用户
     *
     * @param sysUserVo 查询条件
     */
    @PostMapping("/exportExcel")
      @Operation(summary="导出用户")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "export')")
    public void exportExcel(SysUserVo sysUserVo) {
        sysUserVo.setPageNum(99999);
        PageInfo<SysUserVo> sysUser = sysUserService.findPageByQuery(sysUserVo);
        EasyExcelUtils.exportExcel(sysUser.getList(), "系统用户", SysUserVo.class, "系统用户.xlsx", response);
    }

    /**
     * 判断用户名是否已存在
     *
     * @param username 用户名
     * @return 结果
     */
    @GetMapping("/getUserNameIsRepeatable")
      @Operation(summary="用户名是否重复")
    public RestApiResponse<?> getUserNameIsRepeatable(@RequestParam("username") String username) {
        sysUserService.existsUserByUsername(username);
        return RestApiResponse.ok();
    }

    //修改机构信息
    @PostMapping("/updateOrg")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "updateOrg')")
    public RestApiResponse<?> updateOrg(@RequestBody SysUserVo vo) {
    	sysUserService.updateOrg(vo);
    	return RestApiResponse.ok();
    }

    /**
     * 获取用户详情
     *
     * @return 用户信息
     */
    @GetMapping("/findByIds")
    @Operation(summary="获取用户详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "finds')")
    public RestApiResponse<?> findByIds() {
        SysUser sysUser = sysUserService.findByIds(getCurrentUserId());
        return RestApiResponse.ok(sysUser);
    }

    /**
     * 根据行政区划代码查询机构列表
     *
     * @param areaCode 行政区划代码（市级，如3303、3301等）
     * @return 机构账号列表
     */
    @GetMapping("/findOrgListByAreaCode")
    @Operation(summary="根据行政区划代码查询机构列表")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findOrgListByAreaCode(@RequestParam("areaCode") String areaCode) {
        List<OrgListVo> orgList = sysUserService.findOrgListByAreaCode(areaCode);
        return RestApiResponse.ok(orgList);
    }
}
