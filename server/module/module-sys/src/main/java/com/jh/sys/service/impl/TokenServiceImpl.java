package com.jh.sys.service.impl;


import com.jh.common.bean.LoginUser;
import com.jh.common.bean.Token;
import com.jh.common.util.security.JwtUtils;
import com.jh.constant.RedisConstant;
import com.jh.constant.TokenConstants;
import com.jh.sys.service.TokenService;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * token存到redis的实现类<br>
 * 普通token，uuid
 */
@Service
public class TokenServiceImpl implements TokenService {

    /**
     * token过期秒数
     */
    @Value("${token.expire.seconds}")
    private Integer expireSeconds;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public String getToken(HttpServletRequest request) {
        // 头部的Authorization值以Bearer开头
        String auth = request.getHeader("Authorization");
        if (auth != null && !"".equals(auth)) {
            if (auth.startsWith(TokenConstants.PREFIX)) {
                auth = auth.replace(TokenConstants.PREFIX, "");
                auth= JwtUtils.getUserKey(auth);
                return auth.trim();
            }
        }
        auth = request.getParameter("access_token");
        auth=JwtUtils.getUserKey(auth);
        return auth;
    }
    @Override
    public Token saveToken(LoginUser loginUser) {
        String token = UUID.randomUUID().toString();

        loginUser.setToken(token);
        cacheLoginUser(loginUser);
        return new Token(token, loginUser.getLoginTime());
    }

    private void cacheLoginUser(LoginUser loginUser) {
        loginUser.setLoginTime(System.currentTimeMillis());
        loginUser.setExpireTime(loginUser.getLoginTime() + expireSeconds * 1000);
        redisTemplate.boundValueOps(getTokenKey(loginUser.getToken())).set(loginUser, expireSeconds, TimeUnit.SECONDS);
    }

    /**
     * 更新缓存的用户信息
     */
    @Override
    public void refresh(LoginUser loginUser) {
        cacheLoginUser(loginUser);
    }
    @Override
    public Object getLoginUser(String token) {
        return redisTemplate.boundValueOps(getTokenKey(token)).get();
    }
    @Override
    public boolean deleteToken(String token) {
        String key = getTokenKey(token);
        Object loginUser = redisTemplate.opsForValue().get(key);
        if (loginUser != null) {
            redisTemplate.delete(key);
            return true;
        }

        return false;
    }

    private String getTokenKey(String token) {
        return RedisConstant.SYS_REDIS + "tokens:" + token;
    }

}
