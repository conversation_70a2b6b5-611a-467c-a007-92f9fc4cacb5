package com.jh.sys.util;

/**
 * Base62 加解密，浙里检token解密使用
 *
 * <AUTHOR>
 * @date 2024/10/15/ 17:01:00
 */
public class Base62Helper {
    private static final String BASE62_ENCODE_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    private static final int[] BASE62_DECODE_CHARS = {
            -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
            -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
            -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
            52, 53, 54, 55, 56, 57, 58, 59, 60, 61, -1, -1, -1, -1, -1, -1,
            -1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14,
            15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, -1, -1, -1, -1, -1,
            -1, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40,
            41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, -1, -1, -1, -1, -1
    };

    public static String base62Encode(String str) {
        StringBuilder out = new StringBuilder();
        int i = 0, len = str.length();
        char c1, c2, c3;

        while (i < len) {
            c1 = (char) (str.charAt(i++) & 0xff);
            if (i == len) {
                out.append(BASE62_ENCODE_CHARS.charAt(c1 >> 2));
                out.append(BASE62_ENCODE_CHARS.charAt((c1 & 0x3) << 4));
                out.append("XX");  // 补位符号
                break;
            }

            c2 = str.charAt(i++);
            if (i == len) {
                out.append(BASE62_ENCODE_CHARS.charAt(c1 >> 2));
                out.append(BASE62_ENCODE_CHARS.charAt(((c1 & 0x3) << 4) | ((c2 & 0xF0) >> 4)));
                out.append(BASE62_ENCODE_CHARS.charAt((c2 & 0xF) << 2));
                out.append("X");  // 单字符补位
                break;
            }

            c3 = str.charAt(i++);
            out.append(BASE62_ENCODE_CHARS.charAt(c1 >> 2));
            out.append(BASE62_ENCODE_CHARS.charAt(((c1 & 0x3) << 4) | ((c2 & 0xF0) >> 4)));
            out.append(BASE62_ENCODE_CHARS.charAt(((c2 & 0xF) << 2) | ((c3 & 0xC0) >> 6)));
            out.append(BASE62_ENCODE_CHARS.charAt(c3 & 0x3F));
        }

        return out.toString();
    }

    public static String base62Decode(String str) {
        try {
            if (str == null) return "";
            StringBuilder out = new StringBuilder();
            int i = 0, len = str.length();
            int c1, c2, c3, c4;

            while (i < len) {
                do {
                    c1 = BASE62_DECODE_CHARS[str.charAt(i++) & 0xff];
                } while (i < len && c1 == -1);
                if (c1 == -1) break;

                do {
                    c2 = BASE62_DECODE_CHARS[str.charAt(i++) & 0xff];
                } while (i < len && c2 == -1);
                if (c2 == -1) break;

                out.append((char) ((c1 << 2) | ((c2 & 0x30) >> 4)));

                do {
                    if (i >= len) return out.toString();
                    c3 = str.charAt(i++) & 0xff;
                    if (c3 == 'X') return out.toString();  // 补位处理
                    c3 = BASE62_DECODE_CHARS[c3];
                } while (i < len && c3 == -1);
                if (c3 == -1) break;

                out.append((char) (((c2 & 0xF) << 4) | ((c3 & 0x3C) >> 2)));

                do {
                    if (i >= len) return out.toString();
                    c4 = str.charAt(i++) & 0xff;
                    if (c4 == 'X') return out.toString();  // 补位处理
                    c4 = BASE62_DECODE_CHARS[c4];
                } while (i < len && c4 == -1);
                if (c4 == -1) break;

                out.append((char) (((c3 & 0x03) << 6) | c4));
            }

            return out.toString();
        } catch (Exception e) {
            return null;
        }
    }
}
