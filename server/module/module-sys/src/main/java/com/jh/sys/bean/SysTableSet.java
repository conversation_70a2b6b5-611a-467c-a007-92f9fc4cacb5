package com.jh.sys.bean;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import com.jh.common.bean.BaseEntity;
import com.jh.common.xss.StringNoDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Table;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;

/**
 * 用户格设置对象 sys_table_set
 
 * 
 * <AUTHOR>
 * @date 2024-01-31
 */
@Table(name = "sys_table_set")
@Schema(description = "用户格设置")
public class SysTableSet extends BaseEntity {
    private static final long serialVersionUID = 1L;

	@Column(name = "USER_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="用户ID")
	private String userId;
	@Column(name = "TABLE_FLAG")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="表标识")
	private String tableFlag;
	@Column(name = "TABLE_DESC")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="表描述")
	private String tableDesc;
	@Column(name = "LIST_FIELD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="列表字段")
    @JsonDeserialize(using = StringNoDeserializer.class)
	private String listField;
	@Column(name = "EXP_FIELD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="导出字段")
    @JsonDeserialize(using = StringNoDeserializer.class)
	private String expField;
    /**
     * SET 用户ID
     * @param userId
     */
    public void setUserId(String userId){
		this.userId = userId == null ? null :userId;
	}
    /**
     * GET 用户ID
     * @return userId
     */
    public String getUserId(){
        return userId;
    }
    /**
     * SET 表标识
     * @param tableFlag
     */
    public void setTableFlag(String tableFlag){
		this.tableFlag = tableFlag == null ? null :tableFlag;
	}
    /**
     * GET 表标识
     * @return tableFlag
     */
    public String getTableFlag(){
        return tableFlag;
    }
    /**
     * SET 表描述
     * @param tableDesc
     */
    public void setTableDesc(String tableDesc){
		this.tableDesc = tableDesc == null ? null :tableDesc;
	}
    /**
     * GET 表描述
     * @return keyDesc
     */
    public String getTableDesc(){
        return tableDesc;
    }
    /**
     * SET 列表字段
     * @param listField
     */
    public void setListField(String listField){
		this.listField = listField == null ? null :listField;
	}
    /**
     * GET 列表字段
     * @return listField
     */
    public String getListField(){
        return listField;
    }
    /**
     * SET 导出字段
     * @param expField
     */
    public void setExpField(String expField){
		this.expField = expField == null ? null :expField;
	}
    /**
     * GET 导出字段
     * @return expField
     */
    public String getExpField(){
        return expField;
    }
    @Override
	public String toString() {
	    return  ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
	}
}
