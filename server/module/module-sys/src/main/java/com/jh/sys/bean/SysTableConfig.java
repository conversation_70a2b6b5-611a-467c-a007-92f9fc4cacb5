package com.jh.sys.bean;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import com.jh.common.bean.BaseEntity;
import com.jh.common.xss.StringFWBXssDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Table;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;

/**
 * 表格基础配置对象 sys_table_config

 *
 * <AUTHOR>
 * @date 2023-09-14
 */
@Table(name = "sys_table_config")
@Schema(description = "表格基础配置")
public class SysTableConfig extends BaseEntity {
    private static final long serialVersionUID = 1L;

	@Column(name = "table_flag")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="表标识")
	private String tableFlag;
	@Column(name = "mod_type")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="模块名")
	private String modType;
	@Column(name = "son_mod")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="子模块名")
	private String sonMod;
	@Column(name = "table_name")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="表格名称")
	private String tableName;
	@Column(name = "export_json")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="导出json数组配置")
    @JsonDeserialize(using = StringFWBXssDeserializer.class)
	private String exportJson;
	@Column(name = "show_json")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="显示json数组配置")
    @JsonDeserialize(using = StringFWBXssDeserializer.class)
	private String showJson;
    /**
     * SET 表标识
     * @param tableFlag
     */
    public void setTableFlag(String tableFlag){
		this.tableFlag = tableFlag == null ? null :tableFlag;
	}
    /**
     * GET 表标识
     * @return tableFlag
     */
    public String getTableFlag(){
        return tableFlag;
    }
    /**
     * SET 模块名
     * @param modType
     */
    public void setModType(String modType){
		this.modType = modType == null ? null :modType;
	}
    /**
     * GET 模块名
     * @return modType
     */
    public String getModType(){
        return modType;
    }
    /**
     * SET 子模块名
     * @param sonMod
     */
    public void setSonMod(String sonMod){
		this.sonMod = sonMod == null ? null :sonMod;
	}
    /**
     * GET 子模块名
     * @return sonMod
     */
    public String getSonMod(){
        return sonMod;
    }
    /**
     * SET 表格名称
     * @param tableName
     */
    public void setTableName(String tableName){
		this.tableName = tableName == null ? null :tableName;
	}
    /**
     * GET 表格名称
     * @return tableName
     */
    public String getTableName(){
        return tableName;
    }
    /**
     * SET 导出json数组配置
     * @param exportJson
     */
    public void setExportJson(String exportJson){
		this.exportJson = exportJson == null ? null :exportJson;
	}
    /**
     * GET 导出json数组配置
     * @return exportJson
     */
    public String getExportJson(){
        return exportJson;
    }
    /**
     * SET 显示json数组配置
     * @param showJson
     */
    public void setShowJson(String showJson){
		this.showJson = showJson == null ? null :showJson;
	}
    /**
     * GET 显示json数组配置
     * @return showJson
     */
    public String getShowJson(){
        return showJson;
    }
    @Override
	public String toString() {
	    return  ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
	}
}
