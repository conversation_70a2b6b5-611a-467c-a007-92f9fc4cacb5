package com.jh.sys.service.impl;

import com.jh.sys.enums.FileSource;
import com.jh.sys.service.FileService;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * FileService工厂<br>
 * 将各个实现类放入map
 *
 * <AUTHOR>
 */
@Configuration
public class FileServiceFactory {

    private final Map<FileSource, FileService> map = new HashMap<>();

    @Autowired
    private FileService localFileServiceImpl;
    @Autowired
    private FileService aliyunFileServiceImpl;
    @Autowired
    private FileService minioFileServiceImpl;

    @PostConstruct
    public void init() {
        map.put(FileSource.LOCAL, localFileServiceImpl);
        map.put(FileSource.ALIYUN, aliyunFileServiceImpl);
        map.put(FileSource.MINIO, minioFileServiceImpl);
    }

    /**
     * 根据文件源获取具体的实现类
     *
     * @param fileSource
     * @return
     */
    public FileService getFileService(String fileSource) {
        if (StringUtils.isBlank(fileSource)) {// 默认用本地存储
            return localFileServiceImpl;
        }
        FileService fileService = map.get(FileSource.valueOf(fileSource));
        if (fileService == null) {
            throw new IllegalArgumentException("请检查FileServiceFactory类的init方法，看是否有" + fileSource + "对应的实现类");
        }
        return fileService;
    }
}
