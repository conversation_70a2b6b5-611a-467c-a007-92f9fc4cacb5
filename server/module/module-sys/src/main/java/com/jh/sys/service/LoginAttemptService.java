package com.jh.sys.service;

import com.jh.common.redis.RedisUtil;
import com.jh.constant.RedisConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class LoginAttemptService {

    private static final int MAX_ATTEMPT = 10;
    private static final String LOGINATTEMPT = RedisConstant.SYS_REDIS+"login:";
    @Autowired
    private RedisUtil redisUtil;

    public void loginSucceeded(String key) {
        redisUtil.del(LOGINATTEMPT + key);
    }

    public boolean loginFailed(String key) {
        int attempts = 0;
        try {
            attempts = (Integer) redisUtil.get(LOGINATTEMPT + key);
        } catch (Exception e) {
            attempts = 0;
        }
        attempts++;
        redisUtil.set(LOGINATTEMPT + key, attempts, RedisConstant.REDIS_EXPIRE_TEN_MIN);
        return false;
    }

    public boolean isBlocked(String key) {
        try {
            return (Integer) redisUtil.get(LOGINATTEMPT + key) >= MAX_ATTEMPT;
        } catch (Exception e) {
            return false;
        }
    }
}