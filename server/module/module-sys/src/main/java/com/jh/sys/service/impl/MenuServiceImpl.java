package com.jh.sys.service.impl;

import com.jh.common.constant.Constant;
import com.jh.common.service.BaseServiceImpl;
import com.jh.sys.bean.SysMenu;
import com.jh.sys.bean.vo.*;
import com.jh.sys.enums.OrgTypeEnum;

import com.jh.common.exception.ServiceException;
import com.jh.sys.dao.SysMenuMapper;
import com.jh.common.util.ExampleUtil;
import com.jh.common.util.security.UUIDUtils;
import com.jh.common.util.txt.StringUtils;
import com.jh.sys.dao.SysUserMapper;
import com.jh.sys.service.MenuService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 菜单service实现类
 *
 * <AUTHOR>
 * @date 2018/03/01
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class MenuServiceImpl extends BaseServiceImpl<SysMenuMapper, SysMenu> implements MenuService {

    public static final Logger logger = LoggerFactory.getLogger(MenuServiceImpl.class);

    @Autowired
    SysMenuMapper sysMenuMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 获取菜单列表
     *
     * @param menu   查询条件
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public List<SysMenuVo> selectMenuList(SysMenuVo menu, String userId) {
        List<SysMenu> menuList = menu.isAdmin()
                ? listMenuByQuery(menu)
                : sysMenuMapper.selectAuthorityMenuByUserId(userId);
        if (CollectionUtils.isEmpty(menuList)) {
            return Collections.emptyList();
        }

        return menuList.stream()
                .map(aMenu -> {
                    SysMenuVo vo = new SysMenuVo();
                    BeanUtils.copyProperties(aMenu, vo);
                    return vo;
                }).collect(Collectors.toList());
    }

    /**
     * 获取菜单列表
     *
     * @param menu 查询条件
     * @return 结果
     */
    private List<SysMenu> listMenuByQuery(SysMenuVo menu) {
        Example example = new Example(SysMenu.class);
        Criteria criteria = example.createCriteria();
        ExampleUtil.and(criteria, "menuName", menu.getMenuName(), ExampleUtil.LIKE);
        ExampleUtil.and(criteria, "visible", menu.getVisible(), ExampleUtil.EQUAL);
        ExampleUtil.and(criteria, "status", menu.getVisible(), ExampleUtil.EQUAL);
        example.orderBy("orderVal").asc();
        return sysMenuMapper.selectByExample(example);
    }

    /**
     * 删除菜单
     *
     * @param id
     */
    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public void deleteMenuById(String id) {
        SysMenu dbSysMenu = sysMenuMapper.selectByPrimaryKey(id);
        if (dbSysMenu == null) {
            throw new ServiceException("当前菜单不存在，操作失败");
        }

        Example example = new Example(SysMenu.class);
        example.createCriteria().andEqualTo("parentId", id);
        int count = this.mapper.selectCountByExample(example);
        if (count > 0) {
            throw new ServiceException("有子菜单，操作失败");
        }
        this.mapper.deleteByPrimaryKey(id);

    }

    @Override
    public List<SysMenu> selectList(SysMenu sysMenu) {
        return sysMenuMapper.select(sysMenu);
    }

    @Override
    public SysMenu selectById(Object id) {
        return sysMenuMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<SysMenu> selectListAll() {
        return this.sysMenuMapper.selectAll();
    }

    @Override
    // @CacheClear(keys={"permission:menu","permission"})
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public void insertSelective(SysMenu entity) {
        if (!StringUtils.isEmpty(entity.getId())) {
            if ("0".equals(entity.getId())) {
                entity.setId(null);
            }
        }

        entity.setId(UUIDUtils.getUUID());
        // 校验CODE重复
        if (!checkCode(entity.getPerms(), entity.getId()) && !"M".equals(entity.getMenuType())) {
            throw new ServiceException("菜单权限编码已经存在");
        }
        sysMenuMapper.insertSelective(entity);
    }

    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public int updateById(SysMenuVo vo) {
        SysMenu dbMenu = sysMenuMapper.selectByPrimaryKey(vo.getId());
        if (dbMenu == null) {
            throw new ServiceException("当前菜单不存在");
        }
        // 校验CODE重复
        if (!checkCode(vo.getPerms(), vo.getId()) && !"M".equals(vo.getMenuType())) {
            throw new ServiceException("菜单权限编码已经存在");
        }
        return sysMenuMapper.updateByPrimaryKeySelective(vo);
    }

    boolean checkCode(String code, String id) {
        Example example = new Example(SysMenu.class);
        example.createCriteria().andEqualTo("perms", code);
        List<SysMenu> list = sysMenuMapper.selectByExample(example);
        if (list.isEmpty()) {
            return true;
        }
        // 编辑
        if (id != null) {
            for (SysMenu bean : list) {
                if (!bean.getId().equals(id)) {
                    return false;
                }
            }
        } else {
            return false;
        }

        return true;
    }

    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public int updateSelectiveById(SysMenu entity) {
        return sysMenuMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * 获取用户可以访问的菜单
     *
     * @param userId
     * @return
     */
    @Override
    public List<SysMenu> getUserAuthorityMenuByUserId(String userId) {
        return sysMenuMapper.selectAuthorityMenuByUserId(userId);
    }

    @Override
    public List<SysMenu> getUserAuthorityMenuByRoleUserId(String roleCode) {
        return sysMenuMapper.selectAuthorityMenuByRoleUserId(roleCode);
    }

    @Override
    public List<SysMenu> selectByExample(Object example) {
        return sysMenuMapper.selectByExample(example);
    }

    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public void addSysMenu(SysMenuVo vo) {

        boolean isAdmin = vo.isAdmin();

        logger.info("<addSysMenu><sysMenu>" + vo);
        String menuId = vo.getId();
        if (!StringUtils.isEmpty(vo.getPerms())) {
            Example example = new Example(SysMenu.class);
            example.createCriteria().andEqualTo("perms", vo.getPerms());
            int count = this.mapper.selectCountByExample(example);
            if (count > 0) {
                throw new ServiceException("权限编码重复");
            }
        }

        String parentId = vo.getParentId();
        if (parentId == null || Constant.ROOT.equals(parentId)) {
            parentId = Constant.ROOT;
            if (!isAdmin) {
                throw new ServiceException("无新增权限");
            }
            vo.setParentId(parentId);
        }

        if (StringUtils.isEmpty(menuId) || Constant.ROOT.equals(menuId)) {
            vo.setId(UUIDUtils.getUUID());
            this.insertSelective(vo);
        } else {
            this.sysMenuMapper.updateByPrimaryKeySelective(vo);
        }
    }

    /**
     * 根据角色id查询菜单
     */
    @Override
    public List<SysMenu> findRoleId(String roleId) {

        return sysMenuMapper.findRoleId(roleId);
    }

    @Override
    public List<TreeSelect> buildMenuTreeSelect(List<SysMenuVo> menus) {
        List<SysMenuVo> menuTrees = buildMenuTree(menus);
        return menuTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    private List<SysMenuVo> buildMenuTree(List<SysMenuVo> menus) {
        List<SysMenuVo> returnList = new ArrayList<SysMenuVo>();
        List<String> tempList = new ArrayList<String>();
        for (SysMenu dept : menus) {
            tempList.add(dept.getId());
        }
        for (Iterator<SysMenuVo> iterator = menus.iterator(); iterator.hasNext(); ) {
            SysMenuVo menu = iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(menu.getParentId())) {
                recursionFn(menus, menu);
                returnList.add(menu);
            }
        }
        if (returnList.isEmpty()) {
            returnList = menus;
        }
        return returnList;
    }

    /**
     * 递归列表
     *
     * @param list   总的菜单列表
     * @param parent 上级菜单
     */
    private void recursionFn(List<SysMenuVo> list, SysMenuVo parent) {
        // 获取子菜单
        List<SysMenuVo> childList = list.stream().filter(p -> Objects.equals(p.getParentId(), parent.getId())).collect(Collectors.toList());
        parent.setChildren(childList);

        if (CollectionUtils.isEmpty(childList)) {
            return;
        }
        for (SysMenuVo child : childList) {
            recursionFn(list, child);
        }
    }

    /**
     * 构建前端路由所需要的菜单
     *
     * @param menus 菜单列表
     * @return 路由列表
     */
    @Override
    public List<RouterVo> buildMenus(List<SysMenuVo> menus) {
        List<RouterVo> routers = new LinkedList<RouterVo>();
        for (SysMenuVo menu : menus) {
            RouterVo router = new RouterVo();
            router.setHidden(Constant.YES.equals(menu.getVisible()));
            router.setName(getRouteName(menu));
            router.setPath(getRouterPath(menu));
            router.setComponent(getComponent(menu));
            router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), StringUtils.equals("1", menu.getIsCache() + "")));
            List<SysMenuVo> cMenus = menu.getChildren();
            if (!CollectionUtils.isEmpty(cMenus) && Constant.TYPE_DIR.equals(menu.getMenuType())) {
                router.setAlwaysShow(true);
                router.setRedirect("noRedirect");
                router.setChildren(buildMenus(cMenus));
            } else if (isMeunFrame(menu)) {
                List<RouterVo> childrenList = new ArrayList<RouterVo>();
                RouterVo children = new RouterVo();
                children.setPath(menu.getPath());
                children.setComponent(menu.getComponent());
                children.setName(StringUtils.capitalize(menu.getPath()));
                children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), StringUtils.equals("1", menu.getIsCache() + "")));
                childrenList.add(children);
                router.setChildren(childrenList);
            }
            routers.add(router);
        }
        return routers;
    }

    /**
     * 获取路由名称
     *
     * @param menu 菜单信息
     * @return 路由名称
     */
    public String getRouteName(SysMenu menu) {
        String routerName = StringUtils.capitalize(menu.getPath());
        // 非外链并且是一级目录（类型为目录）
        if (isMeunFrame(menu)) {
            routerName = StringUtils.EMPTY;
        }
        return routerName;
    }

    /**
     * 根据父节点的ID获取所有子节点
     *
     * @param list     分类表
     * @param parentId 传入的父节点ID
     * @return String
     */
    @Override
    public List<SysMenuVo> getChildPerms(List<SysMenuVo> list, String parentId) {
        List<SysMenuVo> returnList = new ArrayList<>();
        for (SysMenuVo vo : list) {
            if (parentId.equals(vo.getParentId())) {
                recursionFn(list, vo);
                returnList.add(vo);
            }
        }
        return returnList;
    }


    /**
     * 获取路由地址
     *
     * @param menu 菜单信息
     * @return 路由地址
     */
    public String getRouterPath(SysMenu menu) {
        String routerPath = menu.getPath();
        // 非外链并且是一级目录（类型为目录）
        if ("0".equals(menu.getParentId()) && Constant.TYPE_DIR.equals(menu.getMenuType()) && Constant.NO_FRAME.equals(menu.getIsFrame())) {
            routerPath = "/" + menu.getPath();
        }
        // 非外链并且是一级目录（类型为菜单）
        else if (isMeunFrame(menu)) {
            routerPath = "/";
        }
        return routerPath;
    }

    /**
     * 获取组件信息
     *
     * @param menu 菜单信息
     * @return 组件信息
     */
    public String getComponent(SysMenu menu) {
        String component = Constant.LAYOUT;
        if (StringUtils.isNotEmpty(menu.getComponent()) && !isMeunFrame(menu)) {
            component = menu.getComponent();
        } else if (StringUtils.isEmpty(menu.getComponent()) && isParentView(menu)) {
            component = Constant.PARENT_VIEW;
        }
        return component;
    }

    /**
     * 是否为菜单内部跳转
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isMeunFrame(SysMenu menu) {
        return "0".equals(menu.getParentId()) && Constant.TYPE_MENU.equals(menu.getMenuType()) && menu.getIsFrame().equals(Constant.NO_FRAME);
    }

    /**
     * 是否为parent_view组件
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isParentView(SysMenu menu) {
        return !"0".equals(menu.getParentId()) && Constant.TYPE_DIR.equals(menu.getMenuType());
    }

    /**
     * 根据用户机构类型过滤路由中的数据上报菜单
     */
    @Override
    public List<RouterVo> filterRoutersByOrgType(List<RouterVo> routerVos, String userId) {
        if (CollectionUtils.isEmpty(routerVos) || StringUtils.isEmpty(userId)) {
            return routerVos;
        }

        try {
            // 获取用户信息
            SysUserVo user = sysUserMapper.selectBycurrentUserId(userId);
            if (user == null || StringUtils.isEmpty(user.getOrgType())) {
                logger.info("用户{}没有机构类型信息，不进行路由过滤", userId);
                return routerVos;
            }

//            logger.info("开始过滤路由，用户ID: {}, 路由数量: {}, 用户机构类型: {}", userId, routerVos.size(), user.getOrgType());

            // 解析用户的机构类型
             String[] orgTypes = user.getOrgType().split(",");
             List<String> orgTypeList = Arrays.asList(orgTypes);
             
             // 根据用户的机构类型获取对应的菜单名称
             Set<String> allowedMenuTitles = OrgTypeEnum.getMenuNamesByStrings(orgTypeList);
             
             // 如果用户没有对应的机构类型菜单，则不进行过滤
             if (allowedMenuTitles.isEmpty()) {
                 logger.info("用户{}的机构类型{}没有对应的菜单配置，不进行过滤", userId, user.getOrgType());
                 return routerVos;
             }

//            logger.info("用户机构类型: {}, 允许访问的菜单标题: {}", user.getOrgType(), allowedMenuTitles);

            // 过滤路由
            return filterDataReportRouters(routerVos, allowedMenuTitles);

        } catch (Exception e) {
            logger.error("过滤路由时发生异常", e);
            return routerVos;
        }
    }

    /**
     * 过滤数据上报路由，只过滤收费统计和消费减负下的菜单
     */
    private List<RouterVo> filterDataReportRouters(List<RouterVo> routerVos, Set<String> allowedMenuTitles) {
        List<RouterVo> filteredRouters = new ArrayList<>();
        
        for (RouterVo router : routerVos) {
            // 检查是否是数据上报路由
            if (isDataReportRouter(router)) {
                // 如果是数据上报路由，需要过滤子路由
                RouterVo filteredRouter = new RouterVo();
                BeanUtils.copyProperties(router, filteredRouter);
                
//                logger.info("处理数据上报路由，原始子路由数量: {}", router.getChildren() != null ? router.getChildren().size() : 0);
                
                // 过滤子路由，只过滤收费统计和消费减负
                List<RouterVo> filteredChildren = filterDataReportRouterChildren(router.getChildren(), allowedMenuTitles);
                filteredRouter.setChildren(filteredChildren);
                
//                logger.info("过滤后子路由数量: {}", filteredChildren != null ? filteredChildren.size() : 0);
                
                // 数据上报路由始终保留，即使某些子路由被过滤
                filteredRouters.add(filteredRouter);
//                logger.info("保留数据上报路由");
            } else {
                // 非数据上报路由，直接保留
                filteredRouters.add(router);
            }
        }
        
        return filteredRouters;
    }

    /**
     * 过滤数据上报的子路由，只过滤收费统计和消费减负下的菜单
     */
    private List<RouterVo> filterDataReportRouterChildren(List<RouterVo> children, Set<String> allowedMenuTitles) {
        if (CollectionUtils.isEmpty(children)) {
            return children;
        }

//        logger.info("开始过滤数据上报子路由，允许的菜单标题: {}", allowedMenuTitles);
        
        List<RouterVo> filteredChildren = new ArrayList<>();
        for (RouterVo child : children) {
            String childTitle = child.getMeta() != null ? child.getMeta().getTitle() : "";
//            logger.info("检查子路由: name={}, path={}, title={}, 子路由数量={}",
//                       child.getName(), child.getPath(), childTitle,
//                       child.getChildren() != null ? child.getChildren().size() : 0);
            
            // 检查是否是收费统计或消费减负路由
            boolean isTargetRoute = "收费统计".equals(childTitle) || "消费减负".equals(childTitle);
            
            // 递归处理子路由的子路由
            if (!CollectionUtils.isEmpty(child.getChildren())) {
                RouterVo filteredChild = new RouterVo();
                BeanUtils.copyProperties(child, filteredChild);
                
                // 如果是收费统计或消费减负路由，需要过滤其子路由
                if (isTargetRoute) {
                    // 对于收费统计和消费减负，只保留用户机构类型对应的子菜单
                    List<RouterVo> filteredGrandChildren = new ArrayList<>();
                    
                    for (RouterVo grandChild : child.getChildren()) {
                        String grandChildTitle = grandChild.getMeta() != null ? grandChild.getMeta().getTitle() : "";
                        
                        // 如果子菜单标题在允许列表中，或者是隐藏菜单（以#开头的路径），则保留
                        if (allowedMenuTitles.contains(grandChildTitle) || 
                            (grandChild.getPath() != null && grandChild.getPath().startsWith("#"))) {
                            filteredGrandChildren.add(grandChild);
//                            logger.info("保留子菜单: {}, 原因: 在允许的菜单标题列表中或是隐藏菜单", grandChildTitle);
                        } else {
//                            logger.info("过滤掉子菜单: {}, 原因: 不在允许的菜单标题列表中", grandChildTitle);
                        }
                    }
                    
                    filteredChild.setChildren(filteredGrandChildren);
                    
                    // 即使没有符合条件的子路由，也保留收费统计和消费减负菜单
                    filteredChildren.add(filteredChild);
//                    logger.info("保留目标路由: {}, 子路由数量: {}", childTitle, filteredGrandChildren.size());
                } else {
                    // 非目标路由，直接保留所有子路由
                    filteredChildren.add(child);
//                    logger.info("保留非目标路由: {}, 原因: 不是收费统计或消费减负路由", childTitle);
                }
            } else {
                // 叶子路由处理
                if (isTargetRoute) {
                    // 如果是收费统计或消费减负的叶子路由，检查是否在允许列表中
                    if (StringUtils.isNotEmpty(childTitle) && allowedMenuTitles.contains(childTitle)) {
                        filteredChildren.add(child);
//                        logger.info("保留目标叶子路由: {}, 原因: 在允许的菜单标题列表中", childTitle);
                    } else {
//                        logger.info("过滤掉目标叶子路由: {}, 原因: 不在允许的菜单标题列表中", childTitle);
                    }
                } else {
                    // 非目标路由的叶子路由，直接保留
                    filteredChildren.add(child);
//                    logger.info("保留非目标叶子路由: {}, 原因: 不是收费统计或消费减负路由", childTitle);
                }
            }
        }
        
        return filteredChildren;
    }

    /**
     * 判断是否是数据上报路由
     */
    private boolean isDataReportRouter(RouterVo router) {
        // 根据路由名称、路径或标题判断是否是数据上报路由
        boolean isDataReport = "Datareport".equals(router.getName()) || 
                              "/datareport".equals(router.getPath()) ||
                              "datareport".equals(router.getPath()) ||
                              (router.getMeta() != null && "数据上报".equals(router.getMeta().getTitle()));
        
        if (isDataReport) {
            String title = router.getMeta() != null ? router.getMeta().getTitle() : "";
//            logger.info("识别到数据上报路由: name={}, path={}, title={}", router.getName(), router.getPath(), title);
        }
        
        return isDataReport;
    }
}
