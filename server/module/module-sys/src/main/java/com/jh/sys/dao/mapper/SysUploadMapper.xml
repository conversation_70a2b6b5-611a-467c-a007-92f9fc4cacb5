<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.sys.dao.SysUploadMapper" >
    <resultMap id="CommonBaseResultMap" type="com.jh.common.bean.BaseEntity">
    <result column="ID" property="id" jdbcType="VARCHAR" />
    <result column="YN" property="yn" jdbcType="INTEGER" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR" />
    <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
    <result column="CREATE_USER_NICKNAME" property="createUserNickname" jdbcType="VARCHAR" />
    <result column="UPDATE_USER_NICKNAME" property="updateUserNickname" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 我的上传 -->
  <resultMap id="BaseResultMap" type="com.jh.sys.bean.SysUpload" extends="CommonBaseResultMap">
    <!--
      WARNING - @mbg.generated
    -->
    <!-- 所属用户 -->
    <result column="user_id" property="userId" jdbcType="VARCHAR" />
    <!-- 触发上传的时间 -->
    <result column="upload_time" property="uploadTime" jdbcType="TIMESTAMP" />
    <!-- 生成文件的时间(结束时间) -->
    <result column="gre_time" property="greTime" jdbcType="TIMESTAMP" />
    <!-- 上传的文件下载地址 -->
    <result column="upload_file_url" property="uploadFileUrl" jdbcType="VARCHAR" />
    <!-- 上传文件名称 -->
    <result column="upload_file_name" property="uploadFileName" jdbcType="VARCHAR" />
    <!-- 返回的文件下载地址 -->
    <result column="return_file_url" property="returnFileUrl" jdbcType="VARCHAR" />
    <!-- 返回文件名称 -->
    <result column="return_file_name" property="returnFileName" jdbcType="VARCHAR" />
    <!-- 上传类型 批量上传 -->
    <result column="upload_type" property="uploadType" jdbcType="VARCHAR" />
    <!-- 模块 -->
    <result column="mod_type" property="modType" jdbcType="VARCHAR" />
    <!-- 子模块 -->
    <result column="son_mod" property="sonMod" jdbcType="VARCHAR" />
    <!-- 状态0导入中、1导入完成、2导入失败 -->
    <result column="status" property="status" jdbcType="VARCHAR" />
    <!-- 备注 -->
    <result column="redme" property="redme" jdbcType="VARCHAR" />
    <!-- 总条数 -->
    <result column="total_num" property="totalNum" jdbcType="INTEGER" />
    <!-- 成功条数 -->
    <result column="success_num" property="successNum" jdbcType="INTEGER" />
    <!-- 失败条数 -->
    <result column="error_num" property="errorNum" jdbcType="INTEGER" />
  </resultMap>
</mapper>
