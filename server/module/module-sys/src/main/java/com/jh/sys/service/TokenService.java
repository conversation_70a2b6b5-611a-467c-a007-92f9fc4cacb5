package com.jh.sys.service;

import com.jh.common.bean.LoginUser;
import com.jh.common.bean.Token;
import jakarta.servlet.http.HttpServletRequest;

/**
 * Token管理器<br>
 * 可存储到redis或者数据库<br>
 * 具体可看实现类<br>
 * 默认基于redis，实现类为 com.boot.security.server.service.impl.TokenServiceJWTImpl<br>
 * 如要换成数据库存储，将TokenServiceImpl类上的注解@Primary挪到com.boot.security.server.service.impl.TokenServiceDbImpl
 *
 * <AUTHOR>
 * <p>
 * 2017年10月14日
 */
public interface TokenService {

    String getToken(HttpServletRequest request);

    Token saveToken(LoginUser loginUser);

    void refresh(LoginUser loginUser);

    Object getLoginUser(String token);

    boolean deleteToken(String token);

}
