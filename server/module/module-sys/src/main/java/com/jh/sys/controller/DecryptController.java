package com.jh.sys.controller;

import com.jh.common.bean.RestApiResponse;
import com.jh.common.controller.BaseController;
import com.jh.common.redis.RedisUtil;
import com.jh.common.util.txt.StringUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/sys/decrypt")
@Tag(name = "脱敏反解密")
public class DecryptController extends BaseController {
    public static final Logger logger = LoggerFactory.getLogger(DecryptController.class);
    private static final String PER_PREFIX = "btn:sys:user:";
    @Autowired
    private RedisUtil redisUtil;

    @GetMapping("/getByKey")
    @Operation(summary="根据KEY得到脱密后的数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "updatePwd')")
    public RestApiResponse<?> getByKey(@RequestParam(value = "key", required = true) String key) {

        if (StringUtils.isBlank(key)) {
            return RestApiResponse.error("参数错误");
        }
        String value= (String) redisUtil.get(key);
        if (StringUtils.isBlank(value)) {
            return RestApiResponse.error("页面数据已过期，请刷新页面");
        }
        redisUtil.del(key);
        //记录读取日志
        logger.info("user={},key={},value={}",getCurrentUserName(),key,value);
        return RestApiResponse.ok(value);
    }

    @PostMapping("/getByKeys")
    @Operation(summary="根据KEY得到脱密后的数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "updatePwd')")
    public RestApiResponse<?> getByKeys(@RequestBody List<String> keys) {

        if (keys== null || keys.isEmpty()) {
            return RestApiResponse.error("参数错误");
        }
        List<String> values= new ArrayList<>(keys.size());
        for (String key:keys) {
            String value= (String) redisUtil.get(key);
            if (StringUtils.isBlank(value)) {
                values.add("页面数据已过期，请刷新页面");
            }
            values.add(value);
            redisUtil.del(key);
            //记录读取日志
            logger.info("user={},key={},value={}",getCurrentUserName(),key,value);
        }
        return RestApiResponse.ok(values);
    }
}
