package com.jh.sys.dao;

import com.jh.common.bean.SysLogOper;
import com.jh.common.mybatis.BaseInfoMapper;
import com.jh.sys.bean.vo.SysLogOperExportVo;
import com.jh.sys.bean.vo.SysLogOperVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户操作日志表
 *
 * <AUTHOR>
 * @date 2020-07-07 17:51:45
 */
public interface SysLogOperMapper extends BaseInfoMapper<SysLogOper> {
    /**
     * @param sysLogOperVo
     * @return List<SysLogOperExportVo>
     * @Description:查询要导出日志信息
     * <AUTHOR>
     */
    List<SysLogOperExportVo> selectExportData(@Param("sysLogOperVo") SysLogOperVo sysLogOperVo);
}