package com.jh.sys.bean;

import com.jh.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Table;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;

/**
 * 系统菜单表
 *
 * <AUTHOR>
 * @date 2021-10-20 11:20:42
 */
@Table(name = "SYS_MENU")
@Schema(description = "系统菜单表")
public class SysMenu extends BaseEntity {
    /**
     *
     */
    private static final long serialVersionUID = 1L;

    @Column(name = "MENU_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "资源名称")
    private String menuName;

    @Column(name = "PARENT_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "父ID")
    private String parentId;

    @Column(name = "ORDER_VAL")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "排序值")
    private Integer orderVal;

    @Column(name = "PATH")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "路由地址")
    private String path;

    @Column(name = "COMPONENT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "组件路径")
    private String component;

    @Column(name = "IS_FRAME")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "是否为外链（0是 1否）")
    private Integer isFrame;

    @Column(name = "IS_CACHE")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "是否缓存（0缓存 1不缓存）")
    private Integer isCache;

    @Column(name = "MENU_TYPE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "菜单类型（M目录 C菜单 F按钮）")
    private String menuType;

    @Column(name = "VISIBLE")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "菜单状态（0显示 1隐藏）")
    private Integer visible;

    @Column(name = "STATUS")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "菜单状态（0正常 1停用）")
    private Integer status;

    @Column(name = "PERMS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "权限标识")
    private String perms;

    @Column(name = "ICON")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "图标")
    private String icon;

    @Column(name = "REMARK")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "备注信息")
    private String remark;

    /**
     * GET 资源名称
     *
     * @return menuName
     */
    public String getMenuName() {
        return menuName;
    }

    /**
     * SET 资源名称
     *
     * @param menuName
     */
    public void setMenuName(String menuName) {
        this.menuName = menuName == null ? null : menuName.trim();
    }

    /**
     * GET 父ID
     *
     * @return parentId
     */
    public String getParentId() {
        return parentId;
    }

    /**
     * SET 父ID
     *
     * @param parentId
     */
    public void setParentId(String parentId) {
        this.parentId = parentId == null ? null : parentId.trim();
    }

    /**
     * GET 排序值
     *
     * @return orderVal
     */
    public Integer getOrderVal() {
        return orderVal;
    }

    /**
     * SET 排序值
     *
     * @param orderVal
     */
    public void setOrderVal(Integer orderVal) {
        this.orderVal = orderVal;
    }

    /**
     * GET 路由地址
     *
     * @return path
     */
    public String getPath() {
        return path;
    }

    /**
     * SET 路由地址
     *
     * @param path
     */
    public void setPath(String path) {
        this.path = path == null ? null : path.trim();
    }

    /**
     * GET 组件路径
     *
     * @return component
     */
    public String getComponent() {
        return component;
    }

    /**
     * SET 组件路径
     *
     * @param component
     */
    public void setComponent(String component) {
        this.component = component == null ? null : component.trim();
    }

    /**
     * GET 是否为外链（0是 1否）
     *
     * @return isFrame
     */
    public Integer getIsFrame() {
        return isFrame;
    }

    /**
     * SET 是否为外链（0是 1否）
     *
     * @param isFrame
     */
    public void setIsFrame(Integer isFrame) {
        this.isFrame = isFrame;
    }

    /**
     * GET 是否缓存（0缓存 1不缓存）
     *
     * @return isCache
     */
    public Integer getIsCache() {
        return isCache;
    }

    /**
     * SET 是否缓存（0缓存 1不缓存）
     *
     * @param isCache
     */
    public void setIsCache(Integer isCache) {
        this.isCache = isCache;
    }

    /**
     * GET 菜单类型（M目录 C菜单 F按钮）
     *
     * @return menuType
     */
    public String getMenuType() {
        return menuType;
    }

    /**
     * SET 菜单类型（M目录 C菜单 F按钮）
     *
     * @param menuType
     */
    public void setMenuType(String menuType) {
        this.menuType = menuType == null ? null : menuType.trim();
    }

    /**
     * GET 菜单状态（0显示 1隐藏）
     *
     * @return visible
     */
    public Integer getVisible() {
        return visible;
    }

    /**
     * SET 菜单状态（0显示 1隐藏）
     *
     * @param visible
     */
    public void setVisible(Integer visible) {
        this.visible = visible;
    }

    /**
     * GET 菜单状态（0正常 1停用）
     *
     * @return status
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * SET 菜单状态（0正常 1停用）
     *
     * @param status
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * GET 权限标识
     *
     * @return perms
     */
    public String getPerms() {
        return perms;
    }

    /**
     * SET 权限标识
     *
     * @param perms
     */
    public void setPerms(String perms) {
        this.perms = perms == null ? null : perms.trim();
    }

    /**
     * GET 图标
     *
     * @return icon
     */
    public String getIcon() {
        return icon;
    }

    /**
     * SET 图标
     *
     * @param icon
     */
    public void setIcon(String icon) {
        this.icon = icon == null ? null : icon.trim();
    }

    /**
     * GET 备注信息
     *
     * @return remark
     */
    public String getRemark() {
        return remark;
    }

    /**
     * SET 备注信息
     *
     * @param remark
     */
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }
}