package com.jh.sys.bean.vo;

import com.alibaba.excel.annotation.ExcelProperty;


import com.jh.common.bean.SysUser;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;

import java.util.List;

/**
 * 系统用户表Vo
 *
 * <AUTHOR>
 * @date 2020-07-05 16:25:12
 */
@Schema(description = "系统用户表Vo")
public class SysUserVo extends SysUser {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;
    /**
     * 角色名称
     */
    @ExcelProperty("角色名称")
    private String roleName;
    /**
     * 工作单位 查询条件
     */
    private String deptName;
    /**
     * 用户角色
     */
    private String roleCode;
    /**
     * 用户姓名(真实姓名)
     */

    private String realName;
    /**
     * 多个角色
     */
    private List<String> roleIds;
    /**
     * 多个功能角色
     */
    private List<String> roleFunctionIds;
    /**
     * 部门名称
     */
    private String departmentName;
    /**
     * 功能角色
     */
    private String roleFunctionCode;
    /**
     * 功能角色名
     */
    private String roleFunctionName;
    /**
     * 角色id
     */
    private String displayRoleIds;
    /**
     * 功能角色id
     */
    private String displayRoleFuntionIds;
    /**
     * 新密码
     */
    private String newPwd;
    /**
     * 旧密码
     */
    private String oldPwd;

    /**
     * 验证码
     */
    private String code;
    /**
     * 验证码对应的uuid
     */
    private String uuid;
    /**
     * 排序字段
     */
    private String sortField;
    /**
     * 排序类型
     */
    private String sortType;
    /**
     * 员工信息传递过来id
     */
    private String userId;
    /**
     * 功能角色Code
     */
    private String displayRoleCodes;
    /**
     * 专家的信息
     */

    private String name;
    private String sex;
    private String idCardNumber;
    private String degree;
    private String post;
    private String titleName;
    private String unitName;
    private String birthday;
    /**
     * 多个用户
     */
    private List<String> userIds;

    /**
     * 机构类型
     */
    @Column(name = "ORG_TYPE")
    private String orgType;

    /**
     * 机构地址
     */
    @Column(name = "ORG_ADDRESS")
    private String orgAddress;

    /**
     * 法人
     */
    @Column(name = "LEGAL_PERSON")
    private String legalPerson;

    /**
     * 法人联系方式
     */
    @Column(name = "LEGAL_PERSON_PHONE")
    private String legalPersonPhone;

    /**
     * 行政区划代码
     */
    @Column(name = "AREA_CODE")
    private String areaCode;

    /**
     * 行政区划名称
     */
    @Column(name = "AREA_NAME")
    private String areaName;


    /**
     * 一体化用户ID
     */
    @Column(name = "YTH_USER_ID")
    private String ythUserId;

    public String getYthUserId() {
        return ythUserId;
    }

    public void setYthUserId(String ythUserId) {
        this.ythUserId = ythUserId;
    }

    public List<String> getUserIds() {
        return userIds;
    }

    public void setUserIds(List<String> userIds) {
        this.userIds = userIds;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getDisplayRoleCodes() {
        return displayRoleCodes;
    }

    public void setDisplayRoleCodes(String displayRoleCodes) {
        this.displayRoleCodes = displayRoleCodes;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getOrgAddress() {
        return orgAddress;
    }

    public void setOrgAddress(String orgAddress) {
        this.orgAddress = orgAddress;
    }

    public String getLoginName() {
        return this.getUsername();
    }

    public List<String> getRoleIds() {
        return roleIds;
    }

    public void setRoleIds(List<String> roleIds) {
        this.roleIds = roleIds;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public List<String> getRoleFunctionIds() {
        return roleFunctionIds;
    }

    public void setRoleFunctionIds(List<String> roleFunctionIds) {
        this.roleFunctionIds = roleFunctionIds;
    }

    public String getRoleFunctionName() {
        return roleFunctionName;
    }

    public void setRoleFunctionName(String roleFunctionName) {
        this.roleFunctionName = roleFunctionName;
    }

    public String getDisplayRoleIds() {
        return displayRoleIds;
    }

    public void setDisplayRoleIds(String displayRoleIds) {
        this.displayRoleIds = displayRoleIds;
    }

    public String getDisplayRoleFuntionIds() {
        return displayRoleFuntionIds;
    }

    public void setDisplayRoleFuntionIds(String displayRoleFuntionIds) {
        this.displayRoleFuntionIds = displayRoleFuntionIds;
    }

    public String getNewPwd() {
        return newPwd;
    }

    public void setNewPwd(String newPwd) {
        this.newPwd = newPwd;
    }

    public String getOldPwd() {
        return oldPwd;
    }

    public void setOldPwd(String oldPwd) {
        this.oldPwd = oldPwd;
    }

    public String getSortField() {
        return sortField;
    }

    public void setSortField(String sortField) {
        this.sortField = sortField;
    }

    public String getSortType() {
        return sortType;
    }

    public void setSortType(String sortType) {
        this.sortType = sortType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getRoleFunctionCode() {
        return roleFunctionCode;
    }

    public void setRoleFunctionCode(String roleFunctionCode) {
        this.roleFunctionCode = roleFunctionCode;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String getSex() {
        return sex;
    }

    @Override
    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getIdCardNumber() {
        return idCardNumber;
    }

    public void setIdCardNumber(String idCardNumber) {
        this.idCardNumber = idCardNumber;
    }

    public String getDegree() {
        return degree;
    }

    public void setDegree(String degree) {
        this.degree = degree;
    }

    public String getPost() {
        return post;
    }

    public void setPost(String post) {
        this.post = post;
    }

    public String getTitleName() {
        return titleName;
    }

    public void setTitleName(String titleName) {
        this.titleName = titleName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public String getLegalPerson() {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
    }

    public String getLegalPersonPhone() {
        return legalPersonPhone;
    }

    public void setLegalPersonPhone(String legalPersonPhone) {
        this.legalPersonPhone = legalPersonPhone;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }
}
