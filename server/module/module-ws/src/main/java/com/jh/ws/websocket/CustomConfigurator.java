package com.jh.ws.websocket;

import jakarta.websocket.HandshakeResponse;
import jakarta.websocket.server.HandshakeRequest;
import jakarta.websocket.server.ServerEndpointConfig;
import org.apache.tomcat.websocket.server.WsHandshakeRequest;


public class CustomConfigurator extends ServerEndpointConfig.Configurator {
    @Override
    public void modifyHandshake(ServerEndpointConfig sec, HandshakeRequest request, HandshakeResponse response) {
        // 获取客户端IP地址
//
//        String ipAddress = request.getHeaders().get("X-Forwarded-For").get(0);
//        if (ipAddress == null || ipAddress.isEmpty()) {
//            ipAddress = request.getHeaders().get("Proxy-Client-IP").get(0);
//        }
//        if (ipAddress == null || ipAddress.isEmpty()) {
//            ipAddress = request.getHeaders().get("WL-Proxy-Client-IP").get(0);
//        }
//        if (ipAddress == null || ipAddress.isEmpty()) {
//            ipAddress = request.getHeaders().get("HTTP_CLIENT_IP").get(0);
//        }
//        if (ipAddress == null || ipAddress.isEmpty()) {
//            ipAddress = request.getHeaders().get("HTTP_X_FORWARDED_FOR").get(0);
//        }
//        if (ipAddress == null || ipAddress.isEmpty()) {
//            ipAddress = request.getHeaders().get("Remote_Addr").get(0);
//        }
//        if (ipAddress == null || ipAddress.isEmpty()) {
//            ipAddress = request.getHeaders().get("HTTP_FORWARDED_FOR").get(0);
//        }
//        if (ipAddress == null || ipAddress.isEmpty()) {
//            ipAddress = request.getHeaders().get("HTTP_FORWARDED").get(0);
//        }
//        if (ipAddress == null || ipAddress.isEmpty()) {
//            ipAddress = request.getHeaders().get("FORWARDED_FOR").get(0);
//        }
//        if (ipAddress == null || ipAddress.isEmpty()) {
//            ipAddress = request.getHeaders().get("FORWARDED").get(0);
//        }
//        if (ipAddress == null || ipAddress.isEmpty()) {
//            ipAddress = request.getHeaders().get("X-Real-IP").get(0);
//        }
//        if (ipAddress == null || ipAddress.isEmpty()) {
//            ipAddress = request.getHeaders().get("REMOTE_ADDR").get(0);
//        }
//
//        // 将IP地址存储在用户属性中
//        sec.getUserProperties().put("ipAddress", ipAddress);
    }
}
