package com.jh.ws.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

/**
 * websocket 配置
 * <AUTHOR>
 *
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig {
    public static final Logger logger = LoggerFactory.getLogger(WebSocketConfig.class);
	/**
     * ServerEndpointExporter 作用
     * 这个Bean会自动注册使用@ServerEndpoint注解声明的websocket endpoint
     * @return
     */
    @Bean
    public ServerEndpointExporter serverEndpointExporter() {
        logger.info("WS服务启动中....");
        return new ServerEndpointExporter();
    }
}
