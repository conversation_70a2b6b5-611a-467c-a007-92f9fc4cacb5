package com.jh.annotation;


import com.jh.constant.enums.DesTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

//解密字段注解
@Target(value = {ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface DecryptField {

    /**
     * 是否做况敏处理 默认是脱敏
     * @return
     */
    boolean isDes() default true;

    /**
     * 脱敏类型  默认是前后留一个字符全部脱敏
     * @return
     */
    DesTypeEnum desType() default DesTypeEnum.CHINESE_NAME;
    /**
     * 是否对字段做解密处理
     * @return
     */
    boolean isEncryptField() default true;
}
