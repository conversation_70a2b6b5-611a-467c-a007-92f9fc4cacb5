package com.jh.constant;

/**
 * 流程常量
 * <AUTHOR>
 * @date 2025-05-22
 *
 */
public class FlowConstant {
//    //进行中
//    public static final String FLOW_STATUS_RUNNING = "RUNNING";
//    //已完成
//    public static final String FLOW_STATUS_COMPLETED = "COMPLETED";
//    //已取消
//    public static final String FLOW_STATUS_CANCELLED = "CANCELLED";
//
//    //流程节点类型-开始节点
//    public static final String FLOW_NODE_START = "FLOW_NODE_START";
//    //流程节点类型-审批节点
//    public static final String FLOW_NODE_APPROVAL = "FLOW_NODE_APPROVAL";
//    //流程节点类型-结束节点
//    public static final String FLOW_NODE_END = "FLOW_NODE_END";

        /**
     * 数据上报-收费统计流程
     */
    public static final String REPORT_FEE = "数据上报-收费统计流程";

    /**
     * 流程KEY
     */
    public static final String REPORT_FEE_KEY = "REPORT_FEE";


    /**
     * 数据上报-消费减负流程
     */
    public static final String REPORT_FEE_POLICY = "数据上报-消费减负流程";

    /**
     * 流程KEY
     */
    public static final String REPORT_FEE_POLICY_KEY = "REPORT_FEE_POLICY";


    /**
     * 数据上报-减负降本流程
     */
    public static final String REPORT_FEE_REDUCE = "数据上报-减负降本流程";

    /**
     * 流程KEY
     */
    public static final String REPORT_FEE_REDUCE_KEY = "REPORT_FEE_REDUCE";

    /**
     * 文件管理流程
     */
    public static final String REPORT_DOCUMENT = "文件管理流程";

    /**
     * 流程KEY
     */
    public static final String REPORT_DOCUMENT_KEY = "REPORT_DOCUMENT";

}
