package com.jh.constant;

public class RedisConstant {

    public static final String SYS_REDIS = "financeReport:";

    /**
     * 验证码redis前缀
     */
    public static final String VALID_CODE_KEY = SYS_REDIS + "valid:captcha_codes:";

    /**
     * google安全验证码redis前缀
     */
    public static final String GOOGLE_VALID_CODE_KEY = SYS_REDIS + "valid:google_codes:";
    /**
     * 过期时间 5s 验证码到登录用时不超过5s
     */
    public static final Integer REDIS_EXPIRE_VALID = 5;

    /**
     * 字典类型查询字典条目的RedisKey
     */
    public static final String DICT_TYPE_CODE = SYS_REDIS + "dictionary:TYPE_CODE_";
    /**
     * 过期时间 1 天
     */
    public static final Integer REDIS_EXPIRE_ONE_DAY = 86400;
    /**
     * 过期时间 10分钟
     */
    public static final Integer REDIS_EXPIRE_TEN_MIN = 600;
    /**
     * 过期时间 5分钟
     */
    public static final Integer REDIS_EXPIRE_FIVE_MIN = 300;
    /**
     * 过期时间 2分钟
     */
    public static final Integer REDIS_EXPIRE_ONE_MIN = 120;
    /**
     * 过期时间10秒
     */
    public static final Integer REDIS_EXPIRE_TEN_SECOND = 10;
    /**
     * 时间10分钟
     */
    public static final Integer REDIS_EXPIRE_TEN_MINUTES = 10;

    public static final String REDIS_TOPIC_WEBSOCKET = SYS_REDIS+"websocket:REDIS_TOPIC_WEBSOCKET";

    public static final String REDIS_TOPIC_CMS_STATIC_HTML = SYS_REDIS+"websocket:REDIS_TOPIC_CMS_STATIC_HTML";

}
