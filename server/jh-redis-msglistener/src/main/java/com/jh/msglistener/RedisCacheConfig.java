package com.jh.msglistener;

import com.jh.common.redis.RedisReceiver;
import com.jh.constant.RedisConstant;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;

/**
 * redis 实现消息订阅发布模式
 * <AUTHOR>
 * @date 2020/08/20
 */
@Configuration
@EnableCaching
public class RedisCacheConfig {

    @Bean
    RedisMessageListenerContainer container(RedisConnectionFactory connectionFactory
            ,@Qualifier("listenerWSAdapter")MessageListenerAdapter listenerWSAdapter
//            ,@Qualifier("listenerStaticHtmlAdapter")MessageListenerAdapter listenerStaticHtmlAdapter
    ) {

        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        // 可以添加多个 messageListener，配置不同的交换机
        container.addMessageListener(listenerWSAdapter, new ChannelTopic(RedisConstant.REDIS_TOPIC_WEBSOCKET));
//        container.addMessageListener(listenerStaticHtmlAdapter, new ChannelTopic(RedisConstant.REDIS_TOPIC_CMS_STATIC_HTML));
        return container;
    }

    /**
     * 消息监听器适配器，绑定消息处理器，利用反射技术调用消息处理器的业务方法
     * ws服务消息
     *
     * @param receiver
     * @return
     */
    @Bean("listenerWSAdapter")
    MessageListenerAdapter listenerWSAdapter(@Qualifier("WSRedisReceiverImpl") RedisReceiver receiver) {
        //会自动调用 redisReceiver 的实现类，里面的receiveMessage 方法
        return new MessageListenerAdapter(receiver, "receiveMessage");
    }

//    /**
//     * 静态页面消息 cms
//     * @param receiver
//     * @return
//     */
//    @Bean("listenerStaticHtmlAdapter")
//    MessageListenerAdapter listenerStaticHtmlAdapter(@Qualifier("StaticHtmlServiceImpl") RedisReceiver receiver) {
//        //会自动调用 redisReceiver 的实现类，里面的receiveMessage 方法
//        return new MessageListenerAdapter(receiver, "receiveMessage");
//    }

    /**
     * redis 发布订阅消息发送对象
     * @param connectionFactory
     * @return
     */
    @Bean
    StringRedisTemplate template(RedisConnectionFactory connectionFactory) {
        return new StringRedisTemplate(connectionFactory);
    }
}
